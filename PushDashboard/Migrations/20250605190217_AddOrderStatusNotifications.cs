using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PushDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddOrderStatusNotifications : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OrderStatusChangeLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CompanyId = table.Column<int>(type: "integer", nullable: false),
                    OrderId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OrderNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CustomerEmail = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CustomerName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CustomerPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    OldStatus = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    NewStatus = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    NewStatusDisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    OrderAmount = table.Column<decimal>(type: "numeric", nullable: true),
                    OrderCurrency = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    StatusChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    WebhookPayload = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    NotificationSent = table.Column<bool>(type: "boolean", nullable: false),
                    NotificationSentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NotificationChannels = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NotificationError = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderStatusChangeLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderStatusChangeLogs_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrderStatusNotifications",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CompanyId = table.Column<int>(type: "integer", nullable: false),
                    OrderStatus = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OrderStatusDisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    EmailNotificationEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    EmailTemplateId = table.Column<int>(type: "integer", nullable: true),
                    SmsNotificationEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    SmsTemplateId = table.Column<int>(type: "integer", nullable: true),
                    WhatsAppNotificationEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    WhatsAppTemplateId = table.Column<int>(type: "integer", nullable: true),
                    NotificationOrder = table.Column<int>(type: "integer", nullable: false),
                    DelayMinutes = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastNotificationAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TotalNotificationsSent = table.Column<int>(type: "integer", nullable: false),
                    AdditionalSettings = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderStatusNotifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderStatusNotifications_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusChangeLogs_Company_Date_Performance",
                table: "OrderStatusChangeLogs",
                columns: new[] { "CompanyId", "StatusChangedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusChangeLogs_Company_Status_Date_Performance",
                table: "OrderStatusChangeLogs",
                columns: new[] { "CompanyId", "NewStatus", "StatusChangedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusChangeLogs_CompanyId_Performance",
                table: "OrderStatusChangeLogs",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusChangeLogs_OrderId_Performance",
                table: "OrderStatusChangeLogs",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusChangeLogs_StatusChangedAt_Performance",
                table: "OrderStatusChangeLogs",
                column: "StatusChangedAt");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusNotifications_CompanyId_OrderStatus",
                table: "OrderStatusNotifications",
                columns: new[] { "CompanyId", "OrderStatus" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusNotifications_CompanyId_Performance",
                table: "OrderStatusNotifications",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusNotifications_IsActive_Performance",
                table: "OrderStatusNotifications",
                column: "IsActive");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OrderStatusChangeLogs");

            migrationBuilder.DropTable(
                name: "OrderStatusNotifications");
        }
    }
}
