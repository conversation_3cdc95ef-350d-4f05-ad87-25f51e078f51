using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PushDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddBulkMessagingTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BulkMessages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CompanyId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    Title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CustomerFiltersJson = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    ChannelSettingsJson = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TotalRecipients = table.Column<int>(type: "integer", nullable: false),
                    ProcessedRecipients = table.Column<int>(type: "integer", nullable: false),
                    SuccessfulSends = table.Column<int>(type: "integer", nullable: false),
                    FailedSends = table.Column<int>(type: "integer", nullable: false),
                    EstimatedCost = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    ActualCost = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    StartedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CurrentBatch = table.Column<int>(type: "integer", nullable: false),
                    TotalBatches = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BulkMessages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BulkMessages_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_BulkMessages_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CompanyExpenses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CompanyId = table.Column<int>(type: "integer", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    Description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    ModuleId = table.Column<int>(type: "integer", nullable: true),
                    ReferenceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyExpenses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyExpenses_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyExpenses_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Companies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CompanyExpenses_Modules_ModuleId",
                        column: x => x.ModuleId,
                        principalTable: "Modules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "BulkMessageRecipients",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BulkMessageId = table.Column<int>(type: "integer", nullable: false),
                    CustomerId = table.Column<int>(type: "integer", nullable: false),
                    SentChannels = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Cost = table.Column<decimal>(type: "numeric(10,2)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BulkMessageRecipients", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BulkMessageRecipients_BulkMessages_BulkMessageId",
                        column: x => x.BulkMessageId,
                        principalTable: "BulkMessages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BulkMessageRecipients_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessageRecipients_BulkMessage_Status_Performance",
                table: "BulkMessageRecipients",
                columns: new[] { "BulkMessageId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessageRecipients_BulkMessageId_Performance",
                table: "BulkMessageRecipients",
                column: "BulkMessageId");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessageRecipients_CustomerId",
                table: "BulkMessageRecipients",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessageRecipients_Status_Performance",
                table: "BulkMessageRecipients",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessages_Company_Status_Date_Performance",
                table: "BulkMessages",
                columns: new[] { "CompanyId", "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessages_CompanyId_Performance",
                table: "BulkMessages",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessages_CreatedAt_Performance",
                table: "BulkMessages",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessages_Status_Performance",
                table: "BulkMessages",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_BulkMessages_UserId",
                table: "BulkMessages",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyExpenses_Company_Category_Date_Performance",
                table: "CompanyExpenses",
                columns: new[] { "CompanyId", "Category", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_CompanyExpenses_CompanyId_Performance",
                table: "CompanyExpenses",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyExpenses_CreatedAt_Performance",
                table: "CompanyExpenses",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyExpenses_ModuleId",
                table: "CompanyExpenses",
                column: "ModuleId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyExpenses_UserId",
                table: "CompanyExpenses",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BulkMessageRecipients");

            migrationBuilder.DropTable(
                name: "CompanyExpenses");

            migrationBuilder.DropTable(
                name: "BulkMessages");
        }
    }
}
