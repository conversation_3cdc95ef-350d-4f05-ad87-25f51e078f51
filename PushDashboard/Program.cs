using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services;
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Configure DbContext with PostgreSQL
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"),
        npgsqlOptions => npgsqlOptions.CommandTimeout(300))); // 5 dakika timeout

// Configure Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 5;

    // User settings
    options.User.RequireUniqueEmail = true;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure cookie settings
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromDays(7);
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
});

// Register TwoFactorService
builder.Services.AddScoped<PushDashboard.Services.ITwoFactorService, PushDashboard.Services.TwoFactorService>();
builder.Services.AddScoped<PushDashboard.Services.IStoreService, PushDashboard.Services.StoreService>();
builder.Services.AddScoped<PushDashboard.Services.IModuleSeedService, PushDashboard.Services.ModuleSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IIntegrationSeedService, PushDashboard.Services.IntegrationSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailTemplateSeedService, PushDashboard.Services.EmailTemplateSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailTemplateService, PushDashboard.Services.EmailTemplateService>();
builder.Services.AddScoped<PushDashboard.Services.ISessionService, PushDashboard.Services.SessionService>();
builder.Services.AddScoped<PushDashboard.Services.ICustomerService, PushDashboard.Services.CustomerService>();
builder.Services.AddScoped<PushDashboard.Services.ICustomerImportService, PushDashboard.Services.CustomerImportService>();
builder.Services.AddScoped<PushDashboard.Services.IBasketService, PushDashboard.Services.BasketService>();
builder.Services.AddScoped<PushDashboard.Services.ICommentService, PushDashboard.Services.CommentService>();
builder.Services.AddHttpClient(); // HttpClient for external API calls
builder.Services.AddScoped<Microsoft.AspNetCore.Authentication.IClaimsTransformation, PushDashboard.Services.ClaimsTransformationService>();

// Configure Email Settings
builder.Services.Configure<PushDashboard.Models.EmailSettings>(
    builder.Configuration.GetSection("EmailSettings"));

// Configure Comment Scraper API Settings
builder.Services.Configure<PushDashboard.Configuration.CommentScraperApiSettings>(
    builder.Configuration.GetSection(PushDashboard.Configuration.CommentScraperApiSettings.SectionName));

// Register Invitation Services
builder.Services.AddScoped<PushDashboard.Services.IInvitationService, PushDashboard.Services.InvitationService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailService, PushDashboard.Services.EmailService>();

// Register Integration Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.IEcommerceServiceFactory, PushDashboard.Services.Integrations.EcommerceServiceFactory>();

// Connection test services are now integrated into notification channel services

// Register Welcome Service (commented out - service not implemented yet)
// builder.Services.AddScoped<PushDashboard.Controllers.IWelcomeService, PushDashboard.Services.WelcomeService>();

// Register Webhook Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.IEcommerceWebhookFactory, PushDashboard.Services.Integrations.EcommerceWebhookFactory>();

// Register Module Usage Service
builder.Services.AddScoped<PushDashboard.Services.IModuleUsageService, PushDashboard.Services.ModuleUsageService>();
builder.Services.AddScoped<PushDashboard.Services.ModuleUsageSeedService>();

// Register Notification Services
builder.Services.AddScoped<PushDashboard.Services.Notifications.INotificationChannelService, PushDashboard.Services.Notifications.EmailNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.EmailNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.WhatsAppNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.INotificationChannelFactory, PushDashboard.Services.Notifications.NotificationChannelFactory>();

// Register WhatsApp Services
builder.Services.AddHttpClient<PushDashboard.Services.WhatsApp.IWhatsAppService, PushDashboard.Services.WhatsApp.WhatsAppService>();
builder.Services.AddScoped<PushDashboard.Services.IWhatsAppTemplateService, PushDashboard.Services.WhatsAppTemplateService>();
builder.Services.AddScoped<PushDashboard.Services.IWhatsAppTemplateSampleService, PushDashboard.Services.WhatsAppTemplateSampleService>();
// Register Module Services
builder.Services.AddScoped<PushDashboard.Services.Modules.Birthday.IBirthdayModuleService, PushDashboard.Services.Modules.Birthday.BirthdayModuleService>();
// Register First Order Notification Service
builder.Services.AddScoped<PushDashboard.Services.Modules.FirstOrder.IFirstOrderModuleService, PushDashboard.Services.Modules.FirstOrder.FirstOrderModuleService>();

// Register Dashboard Service
builder.Services.AddScoped<PushDashboard.Services.IDashboardService, PushDashboard.Services.DashboardService>();
// Register Ticimax Base Service as IEcommerceService
builder.Services.AddScoped<PushDashboard.Services.Integrations.Common.IEcommerceService, PushDashboard.Services.Integrations.Ticimax.TicimaxService>();

// Register TicimaxService directly for connection testing
builder.Services.AddScoped<PushDashboard.Services.Integrations.Ticimax.TicimaxService>();

// Register E-commerce Gift Voucher Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.EcommerceGiftVoucherFactory>();

// Register Bulk Messaging Services
builder.Services.AddScoped<PushDashboard.Services.BulkMessaging.IBulkMessagingService, PushDashboard.Services.BulkMessaging.BulkMessagingService>();

// Register Order Status Notification Service
builder.Services.AddScoped<PushDashboard.Services.IOrderStatusNotificationService, PushDashboard.Services.OrderStatusNotificationService>();

// Add session services
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
});
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.AccessDeniedPath = "/access-denied";
    });

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseSession();
app.UseAuthentication();
app.UseAuthorization();

// Create database and apply migrations if they don't exist
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        // Seed initial roles and admin user if needed
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
        SeedData.Initialize(userManager, roleManager, context).Wait();

        // Seed module data
        var moduleSeedService = services.GetRequiredService<IModuleSeedService>();
        moduleSeedService.SeedAsync().Wait();

        // Seed integration data
        var integrationSeedService = services.GetRequiredService<IIntegrationSeedService>();
        integrationSeedService.SeedAsync().Wait();

        // Seed email template data
        var emailTemplateSeedService = services.GetRequiredService<IEmailTemplateSeedService>();
        emailTemplateSeedService.SeedAsync().Wait();

        // Seed module usage data
        var moduleUsageSeedService = services.GetRequiredService<ModuleUsageSeedService>();
        moduleUsageSeedService.SeedAsync().Wait();

        // Seed first order module data

        // Test entegrasyon mimarisi (sadece development ortamında)
        // if (app.Environment.IsDevelopment())
        // {
        //     try
        //     {
        //         await PushDashboard.Tests.IntegrationArchitectureTest.TestIntegrationArchitecture(services);
        //     }
        //     catch (Exception testEx)
        //     {
        //         var logger = services.GetRequiredService<ILogger<Program>>();
        //         logger.LogError(testEx, "Entegrasyon mimarisi testi başarısız");
        //     }
        // }
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while migrating or seeding the database.");
    }
}

app.MapHub<PushDashboard.Hubs.SessionHub>("/sessionHub");
app.MapHub<PushDashboard.Hubs.CustomerImportHub>("/customerImportHub");
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();