using PushDashboard.Models;

namespace PushDashboard.ViewModels;

public class StoreIndexViewModel
{
    public List<ModuleCategoryViewModel> Categories { get; set; } = new();

    public List<ModuleViewModel> Modules { get; set; } = new();

    public List<ModuleViewModel> OwnedModules { get; set; } = new();
    public List<TransactionHistoryViewModel> RecentTransactions { get; set; } = new();
    public List<ModuleViewModel> AvailableModules { get; set; } = new();

    public StoreStatsViewModel Stats { get; set; } = new();

    public class ModuleCategoryViewModel
    {
        public int Id { get; set; }

        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }

        public string? IconClass { get; set; }

        public int ModuleCount { get; set; }
    }

    public class ModuleViewModel
    {
        public int Id { get; set; }

        public string Name { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public string? DetailedDescription { get; set; }

        public decimal Price { get; set; }

        public string? IconClass { get; set; }

        public string? IconColor { get; set; }

        public string? BackgroundColor { get; set; }

        public bool IsActive { get; set; }

        public bool IsNew { get; set; }

        public bool IsFeatured { get; set; }

        public bool IsOwned { get; set; }

        public int CategoryId { get; set; }
        
        public string CategoryName { get; set; } = string.Empty;

        public List<string> Features { get; set; } = new();

        public DateTime? PurchasedAt { get; set; }

        public DateTime? LastUsedAt { get; set; }

        public string FormattedPrice => $"₺{Price:N0}";

        public string StatusBadge => IsOwned ? "Aktif" : (IsNew ? "Yeni" : "");

        public string StatusBadgeClass => IsOwned ? "bg-green-100 text-green-800" : (IsNew ? "bg-blue-100 text-blue-800" : "");
    }

    public class StoreStatsViewModel
    {
        public decimal TotalCredits { get; set; }

        public DateTime? LastUsage { get; set; }

        public int ActiveModules { get; set; }

        public List<string> ActiveModuleNames { get; set; } = new();

        public string FormattedCredits => $"₺{TotalCredits:N2}";

        public string FormattedLastUsage => LastUsage?.ToString("dd MMMM yyyy") ?? "Henüz kullanılmadı";
    }

    public class PurchaseModuleViewModel
    {
        public int ModuleId { get; set; }

        public string ModuleName { get; set; } = string.Empty;

        public decimal Price { get; set; }

        public string PaymentMethod { get; set; } = "credit"; // credit, card

        public bool AcceptTerms { get; set; }
    }

    public class ModuleDetailsViewModel
    {
        public ModuleViewModel Module { get; set; } = new();

        public bool CanPurchase { get; set; }

        public string? PurchaseBlockReason { get; set; }

        public List<ModuleViewModel> RelatedModules { get; set; } = new();
    }

    public class PagedTransactionHistoryViewModel
    {
        public List<TransactionHistoryViewModel> Transactions { get; set; } = new();

        public int CurrentPage { get; set; } = 1;

        public int TotalPages { get; set; } = 1;

        public int TotalItems { get; set; } = 0;

        public int PageSize { get; set; } = 10;

        public bool HasPreviousPage => CurrentPage > 1;

        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class TransactionHistoryViewModel
    {
        public int Id { get; set; }

        public string ModuleName { get; set; } = string.Empty;

        public decimal PaidAmount { get; set; }

        public DateTime PurchasedAt { get; set; }

        public string? TransactionId { get; set; }

        public string Status { get; set; } = "Tamamlandı";

        public int CategoryId { get; set; }
        
        public string CategoryName { get; set; } = string.Empty;

        public string? ModuleIcon { get; set; }

        public string? ModuleIconColor { get; set; }

        public string? ModuleBackgroundColor { get; set; }

        public string FormattedAmount => $"₺{PaidAmount:N2}";

        public string FormattedDate => PurchasedAt.ToString("dd MMM yyyy");

        public string FormattedTime => PurchasedAt.ToString("HH:mm");

        public string StatusBadgeClass => Status switch
        {
            "Tamamlandı" => "bg-green-100 text-green-800",
            "Beklemede" => "bg-yellow-100 text-yellow-800",
            "İptal" => "bg-red-100 text-red-800",
            _ => "bg-gray-100 text-gray-800"
        };
    }
}

public class TransactionHistoryViewModel
{
    public int Id { get; set; }
    public string ModuleName { get; set; } = string.Empty;
    public decimal PaidAmount { get; set; }
    public DateTime PurchasedAt { get; set; }
    public string? TransactionId { get; set; }
    public string Status { get; set; } = "Tamamlandı";
    public int CategoryId { get; set; }
        
        public string CategoryName { get; set; } = string.Empty;
    public string? ModuleIcon { get; set; }
    public string? ModuleIconColor { get; set; }
    public string? ModuleBackgroundColor { get; set; }
    
    public string FormattedAmount => $"₺{PaidAmount:N2}";
    public string FormattedDate => PurchasedAt.ToString("dd MMM yyyy");
    public string FormattedTime => PurchasedAt.ToString("HH:mm");
    public string StatusBadgeClass => Status switch
    {
        "Tamamlandı" => "bg-green-100 text-green-800",
        "Beklemede" => "bg-yellow-100 text-yellow-800",
        "İptal" => "bg-red-100 text-red-800",
        _ => "bg-gray-100 text-gray-800"
    };
}
