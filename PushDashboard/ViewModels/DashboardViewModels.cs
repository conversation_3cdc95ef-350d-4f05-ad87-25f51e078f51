using PushDashboard.Models;

namespace PushDashboard.ViewModels;

public class DashboardIndexViewModel
{
    public DashboardStatsViewModel Stats { get; set; } = new();
    public List<ModuleUsageOverviewViewModel> ModuleUsage { get; set; } = new();
    public List<IntegrationStatusViewModel> IntegrationStatus { get; set; } = new();
    public List<RecentActivityViewModel> RecentActivities { get; set; } = new();
    public List<NotificationMetricsViewModel> NotificationMetrics { get; set; } = new();
    public DashboardChartsViewModel Charts { get; set; } = new();
    public List<QuickActionViewModel> QuickActions { get; set; } = new();
}

public class DashboardStatsViewModel
{
    public int ActiveModules { get; set; }
    public decimal CreditBalance { get; set; }
    public int TotalCustomers { get; set; }
    public int NotificationsSentToday { get; set; }
    public int NotificationsSentThisMonth { get; set; }
    public decimal MonthlySpending { get; set; }
    public int ActiveIntegrations { get; set; }
    public double AverageDeliveryRate { get; set; }
    public DateTime? LastActivity { get; set; }

    // Formatted properties
    public string FormattedCreditBalance => $"₺{CreditBalance:N2}";
    public string FormattedMonthlySpending => $"₺{MonthlySpending:N2}";
    public string FormattedDeliveryRate => $"{AverageDeliveryRate:F1}%";
    public string FormattedLastActivity => LastActivity?.ToString("dd MMM yyyy HH:mm") ?? "Henüz aktivite yok";
}

public class ModuleUsageOverviewViewModel
{
    public int ModuleId { get; set; }
    public string ModuleName { get; set; } = string.Empty;
    public string? ModuleIcon { get; set; }
    public string? ModuleColor { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastUsed { get; set; }
    public int UsageCountThisMonth { get; set; }
    public decimal CostThisMonth { get; set; }
    public double SuccessRate { get; set; }

    public string FormattedCost => $"₺{CostThisMonth:N2}";
    public string FormattedSuccessRate => $"{SuccessRate:F1}%";
    public string FormattedLastUsed => LastUsed?.ToString("dd MMM") ?? "Hiç kullanılmadı";
    public string StatusBadgeClass => IsActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800";
    public string StatusText => IsActive ? "Aktif" : "Pasif";
}

public class IntegrationStatusViewModel
{
    public int IntegrationId { get; set; }
    public string IntegrationName { get; set; } = string.Empty;
    public string IntegrationType { get; set; } = string.Empty;
    public bool IsConnected { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastSync { get; set; }
    public string? LastError { get; set; }
    public int SuccessfulOperationsToday { get; set; }
    public int FailedOperationsToday { get; set; }

    public string StatusText => IsConnected ? (IsActive ? "Bağlı" : "Pasif") : "Bağlantı Hatası";
    public string StatusBadgeClass => IsConnected ? (IsActive ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800") : "bg-red-100 text-red-800";
    public string FormattedLastSync => LastSync?.ToString("dd MMM HH:mm") ?? "Henüz senkronize edilmedi";
    public double SuccessRate => (SuccessfulOperationsToday + FailedOperationsToday) > 0 
        ? (double)SuccessfulOperationsToday / (SuccessfulOperationsToday + FailedOperationsToday) * 100 
        : 0;
    public string FormattedSuccessRate => $"{SuccessRate:F1}%";
}

public class RecentActivityViewModel
{
    public string ActivityType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? ModuleName { get; set; }
    public string? Status { get; set; }
    public decimal? Cost { get; set; }

    public string FormattedTimestamp => Timestamp.ToString("dd MMM HH:mm");
    public string FormattedCost => Cost.HasValue ? $"₺{Cost.Value:N2}" : "";
    public string ActivityIcon => ActivityType switch
    {
        "notification" => "bell",
        "module_purchase" => "shopping-cart",
        "integration" => "link",
        "sync" => "refresh-cw",
        _ => "activity"
    };
}

public class NotificationMetricsViewModel
{
    public string ChannelType { get; set; } = string.Empty;
    public string ChannelName { get; set; } = string.Empty;
    public int SentToday { get; set; }
    public int SentThisWeek { get; set; }
    public int SentThisMonth { get; set; }
    public int DeliveredToday { get; set; }
    public int FailedToday { get; set; }
    public decimal CostToday { get; set; }
    public decimal CostThisMonth { get; set; }

    public double DeliveryRateToday => SentToday > 0 ? (double)DeliveredToday / SentToday * 100 : 0;
    public string FormattedDeliveryRate => $"{DeliveryRateToday:F1}%";
    public string FormattedCostToday => $"₺{CostToday:N2}";
    public string FormattedCostThisMonth => $"₺{CostThisMonth:N2}";
    public string ChannelIcon => ChannelType.ToLower() switch
    {
        "email" => "mail",
        "whatsapp" => "message-circle",
        "sms" => "smartphone",
        _ => "send"
    };
}

public class DashboardChartsViewModel
{
    public List<ChartDataPoint> UsageTrend { get; set; } = new();
    public List<ChartDataPoint> CostTrend { get; set; } = new();
    public List<ChartDataPoint> DeliveryRates { get; set; } = new();
    public List<PieChartDataPoint> ModuleDistribution { get; set; } = new();
    public List<PieChartDataPoint> ChannelDistribution { get; set; } = new();
}

public class ChartDataPoint
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
    public string? Category { get; set; }
}

public class PieChartDataPoint
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Color { get; set; } = string.Empty;
    public double Percentage { get; set; }
}

public class QuickActionViewModel
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string ActionUrl { get; set; } = string.Empty;
    public string ActionType { get; set; } = string.Empty; // "link", "button", "modal"
    public bool IsEnabled { get; set; } = true;
    public string? Badge { get; set; }
    public string? BadgeColor { get; set; }
}
