{"ConnectionStrings": {"DefaultConnection": "Server=dpg-d0qdo6be5dus739g3rb0-a;Port=5432;Database=pushonica;Userid=pushouse_postgres_user;Password=********************************;Timeout=300;Command Timeout=300;Connection Idle Lifetime=300;"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "Pushonica Info", "Username": "<EMAIL>", "Password": "swpb jpyl atgu cpdx"}, "BaseUrl": "https://pushoniceadmin.onrender.com", "CommentScraperApi": {"BaseUrl": "https://reviewscraper.onrender.com", "ScrapeReviewsEndpoint": "/scrape-reviews", "JobStatusEndpoint": "/job/{0}"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}