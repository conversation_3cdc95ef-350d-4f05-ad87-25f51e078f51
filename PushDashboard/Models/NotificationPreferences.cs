using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class NotificationPreferences
{
    [Key]
    public int Id { get; set; }
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    [ForeignKey("UserId")]
    public virtual ApplicationUser User { get; set; } = null!;
    
    // E-posta bildirimleri
    public bool EmailInvoiceNotifications { get; set; } = true;
    public bool EmailCreditNotifications { get; set; } = true;
    public bool EmailMarketingNotifications { get; set; } = false;
    
    // SMS bildirimleri
    public bool SmsSecurityAlerts { get; set; } = true;
    public bool SmsPaymentNotifications { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
