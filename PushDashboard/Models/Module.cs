using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace PushDashboard.Models;

public class Module
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? DetailedDescription { get; set; }

    [Required]
    [Column(TypeName = "decimal(10,2)")]
    public decimal Price { get; set; }

    [StringLength(50)]
    public string? IconClass { get; set; }

    [StringLength(20)]
    public string? IconColor { get; set; }

    [StringLength(20)]
    public string? BackgroundColor { get; set; }

    public bool IsActive { get; set; } = true;

    public bool IsNew { get; set; } = false;

    public bool IsFeatured { get; set; } = false;

    public int CategoryId { get; set; }

    [ForeignKey("CategoryId")]
    public virtual ModuleCategory Category { get; set; } = null!;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; set; }

    // Features as JSON string
    [StringLength(2000)]
    public string? Features { get; set; }

    // Default settings as JSON string
    [StringLength(4000)]
    public string? DefaultSettings { get; set; }
    // Navigation properties
    public virtual ICollection<CompanyModule> CompanyModules { get; set; } = new List<CompanyModule>();
}

public class ModuleCategory
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? IconClass { get; set; }

    public bool IsActive { get; set; } = true;

    public int SortOrder { get; set; } = 0;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<Module> Modules { get; set; } = new List<Module>();
}

/// <summary>
/// Company-based module ownership and management
/// </summary>
public class CompanyModule
{
    [Key]
    public int Id { get; set; }

    [Required]
    public int CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    public int ModuleId { get; set; }

    [ForeignKey("ModuleId")]
    public virtual Module Module { get; set; } = null!;

    public DateTime PurchasedAt { get; set; } = DateTime.UtcNow;

    public DateTime? ExpiresAt { get; set; }

    public bool IsActive { get; set; } = true;

    [Column(TypeName = "decimal(10,2)")]
    public decimal PaidAmount { get; set; }

    [StringLength(100)]
    public string? TransactionId { get; set; }

    public DateTime? LastUsedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    [Required]
    public string PurchasedByUserId { get; set; } = string.Empty;

    [ForeignKey("PurchasedByUserId")]
    public virtual ApplicationUser PurchasedByUser { get; set; } = null!;

    // Navigation property for settings
    public virtual CompanyModuleSettings? Settings { get; set; }

    // Unique constraint
    public static void ConfigureEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CompanyModule>()
            .HasIndex(cm => new { cm.CompanyId, cm.ModuleId })
            .IsUnique();
    }
}

/// <summary>
/// Company module settings - shared across all company users
/// </summary>
public class CompanyModuleSettings
{
    [Key]
    public int Id { get; set; }

    public int CompanyModuleId { get; set; }

    [ForeignKey("CompanyModuleId")]
    public virtual CompanyModule CompanyModule { get; set; } = null!;

    // Settings stored as JSON
    [StringLength(4000)]
    public string? SettingsJson { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    [Required]
    public string UpdatedByUserId { get; set; } = string.Empty;

    [ForeignKey("UpdatedByUserId")]
    public virtual ApplicationUser UpdatedByUser { get; set; } = null!;

    // Computed property for settings
    [NotMapped]
    public Dictionary<string, object> Settings
    {
        get
        {
            if (string.IsNullOrEmpty(SettingsJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(SettingsJson)
                       ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }
        set
        {
            SettingsJson = JsonSerializer.Serialize(value);
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
