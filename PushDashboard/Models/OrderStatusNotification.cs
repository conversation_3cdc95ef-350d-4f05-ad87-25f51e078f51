using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

/// <summary>
/// Sipariş durumu değişikliği bildirim ayarları
/// </summary>
public class OrderStatusNotification
{
    [Key]
    public int Id { get; set; }

    public int CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    /// <summary>
    /// Sipariş durumu (Ticimax'tan gelen durum kodu)
    /// </summary>
    [Required]
    [StringLength(100)]
    public string OrderStatus { get; set; } = string.Empty;

    /// <summary>
    /// Sipariş durumunun görüntülenen adı
    /// </summary>
    [Required]
    [StringLength(200)]
    public string OrderStatusDisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Bu durum için bildirim aktif mi?
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// E-posta bildirimi aktif mi?
    /// </summary>
    public bool EmailNotificationEnabled { get; set; } = false;

    /// <summary>
    /// E-posta şablonu ID'si
    /// </summary>
    public int? EmailTemplateId { get; set; }

    /// <summary>
    /// SMS bildirimi aktif mi?
    /// </summary>
    public bool SmsNotificationEnabled { get; set; } = false;

    /// <summary>
    /// SMS şablonu ID'si
    /// </summary>
    public int? SmsTemplateId { get; set; }

    /// <summary>
    /// WhatsApp bildirimi aktif mi?
    /// </summary>
    public bool WhatsAppNotificationEnabled { get; set; } = false;

    /// <summary>
    /// WhatsApp şablonu ID'si
    /// </summary>
    public string? WhatsAppTemplateId { get; set; }

    /// <summary>
    /// Bildirim gönderilme sırası (1: hemen, 2: 1 saat sonra, vb.)
    /// </summary>
    public int NotificationOrder { get; set; } = 1;

    /// <summary>
    /// Bildirim gecikmesi (dakika cinsinden)
    /// </summary>
    public int DelayMinutes { get; set; } = 0;

    /// <summary>
    /// Oluşturulma tarihi
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Güncellenme tarihi
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Son bildirim gönderilme tarihi
    /// </summary>
    public DateTime? LastNotificationAt { get; set; }

    /// <summary>
    /// Toplam gönderilen bildirim sayısı
    /// </summary>
    public int TotalNotificationsSent { get; set; } = 0;

    /// <summary>
    /// Ek ayarlar (JSON formatında)
    /// </summary>
    [StringLength(2000)]
    public string? AdditionalSettings { get; set; }
}

/// <summary>
/// Sipariş durumu değişikliği geçmişi
/// </summary>
public class OrderStatusChangeLog
{
    [Key]
    public int Id { get; set; }

    public int CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    /// <summary>
    /// Sipariş ID'si (e-ticaret platformundan gelen)
    /// </summary>
    [Required]
    [StringLength(100)]
    public string OrderId { get; set; } = string.Empty;

    /// <summary>
    /// Sipariş numarası
    /// </summary>
    [StringLength(100)]
    public string? OrderNumber { get; set; }

    /// <summary>
    /// Müşteri e-posta adresi
    /// </summary>
    [Required]
    [StringLength(255)]
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Müşteri adı
    /// </summary>
    [StringLength(200)]
    public string? CustomerName { get; set; }

    /// <summary>
    /// Müşteri telefonu
    /// </summary>
    [StringLength(20)]
    public string? CustomerPhone { get; set; }

    /// <summary>
    /// Eski sipariş durumu
    /// </summary>
    [StringLength(100)]
    public string? OldStatus { get; set; }

    /// <summary>
    /// Yeni sipariş durumu
    /// </summary>
    [Required]
    [StringLength(100)]
    public string NewStatus { get; set; } = string.Empty;

    /// <summary>
    /// Yeni durumun görüntülenen adı
    /// </summary>
    [StringLength(200)]
    public string? NewStatusDisplayName { get; set; }

    /// <summary>
    /// Sipariş tutarı
    /// </summary>
    public decimal? OrderAmount { get; set; }

    /// <summary>
    /// Sipariş para birimi
    /// </summary>
    [StringLength(10)]
    public string? OrderCurrency { get; set; }

    /// <summary>
    /// Durum değişikliği tarihi
    /// </summary>
    public DateTime StatusChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Webhook'tan gelen ham veri (JSON)
    /// </summary>
    [StringLength(4000)]
    public string? WebhookPayload { get; set; }

    /// <summary>
    /// Bildirim gönderildi mi?
    /// </summary>
    public bool NotificationSent { get; set; } = false;

    /// <summary>
    /// Bildirim gönderilme tarihi
    /// </summary>
    public DateTime? NotificationSentAt { get; set; }

    /// <summary>
    /// Bildirim kanalları (JSON: ["email", "sms", "whatsapp"])
    /// </summary>
    [StringLength(500)]
    public string? NotificationChannels { get; set; }

    /// <summary>
    /// Bildirim hatası varsa hata mesajı
    /// </summary>
    [StringLength(1000)]
    public string? NotificationError { get; set; }

    /// <summary>
    /// Kayıt oluşturulma tarihi
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
