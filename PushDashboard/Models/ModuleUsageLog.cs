using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

/// <summary>
/// Modül kullanım maliyetlerini ve harcama takibini yönetir
/// </summary>
public class ModuleUsageLog
{
    [Key]
    public int Id { get; set; }

    [Required]
    public int? CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company? Company { get; set; }

    [Required]
    public int ModuleId { get; set; }

    [ForeignKey("ModuleId")]
    public virtual Module Module { get; set; } = null!;

    [Required]
    [StringLength(50)]
    public string UsageType { get; set; } = string.Empty; // "email", "sms", "whatsapp", etc.

    [Required]
    [StringLength(200)]
    public string Description { get; set; } = string.Empty; // "Birthday notification <NAME_EMAIL>"

    [Required]
    [Column(TypeName = "decimal(10,2)")]
    public decimal Cost { get; set; } // Bu kullanım için düşülen tutar

    [Required]
    [Column(TypeName = "decimal(10,2)")]
    public decimal BalanceBefore { get; set; } // İşlem öncesi bakiye

    [Required]
    [Column(TypeName = "decimal(10,2)")]
    public decimal BalanceAfter { get; set; } // İşlem sonrası bakiye

    [Required]
    public string UserId { get; set; } = string.Empty; // İşlemi tetikleyen kullanıcı

    [ForeignKey("UserId")]
    public virtual ApplicationUser User { get; set; } = null!;

    [StringLength(100)]
    public string? ReferenceId { get; set; } // Müşteri ID, bildirim ID, vb.

    [StringLength(50)]
    public string? Channel { get; set; } // "email", "sms", "whatsapp"

    public bool IsSuccessful { get; set; } = true; // İşlem başarılı mı?

    [StringLength(500)]
    public string? ErrorMessage { get; set; } // Hata mesajı (varsa)

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // JSON metadata for additional information
    [StringLength(1000)]
    public string? Metadata { get; set; }
}
