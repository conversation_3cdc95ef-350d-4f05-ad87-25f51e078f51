using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class BaseController : Controller
{
    protected int? GetCurrentUserCompanyId()
    {
        var companyIdClaim = User.FindFirst("CompanyId");
        if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int companyId))
        {
            return companyId;
        }
        return null;
    }

    protected string? GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}