using System.Diagnostics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PushDashboard.Models;
using PushDashboard.Services;
using PushDashboard.ViewModels;

namespace PushDashboard.Controllers;

[Authorize]
public class HomeController : BaseController
{
    private readonly ILogger<HomeController> _logger;
    private readonly IDashboardService _dashboardService;

    public HomeController(ILogger<HomeController> logger, IDashboardService dashboardService)
    {
        _logger = logger;
        _dashboardService = dashboardService;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Login", "Account");
        }

        try
        {
            var dashboardData = await _dashboardService.GetDashboardDataAsync(companyId.Value);
            return View(dashboardData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard for company {CompanyId}", companyId);
            TempData["ErrorMessage"] = "Dashboard yüklenirken bir hata oluştu.";
            return View(new DashboardIndexViewModel());
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetRealTimeData()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var data = await _dashboardService.GetRealTimeDataAsync(companyId.Value);
            return Json(new { success = true, data });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time data for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Veriler alınırken hata oluştu." });
        }
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
