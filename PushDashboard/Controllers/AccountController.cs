using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services;
using PushDashboard.ViewModels;

namespace PushDashboard.Controllers;

public class AccountController : Controller
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<AccountController> _logger;
    private readonly ITwoFactorService _twoFactorService;
    private readonly ISessionService _sessionService;
    private readonly IInvitationService _invitationService;

    public AccountController(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ApplicationDbContext context,
        ILogger<AccountController> logger,
        ITwoFactorService twoFactorService,
        ISessionService sessionService,
        IInvitationService invitationService)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _context = context;
        _logger = logger;
        _twoFactorService = twoFactorService;
        _sessionService = sessionService;
        _invitationService = invitationService;
    }

    [HttpGet]
    public IActionResult Login(string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    [HttpGet]
    public IActionResult VerifyTwoFactor(string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> VerifyTwoFactor(TwoFactorVerifyViewModel model)
    {
        if (!ModelState.IsValid)
            return View(model);

        var email = TempData["Email"] as string;
        if (string.IsNullOrEmpty(email))
        {
            ModelState.AddModelError(string.Empty, "Oturum süresi doldu. Lütfen tekrar giriş yapın.");
            return RedirectToAction(nameof(Login));
        }

        var user = await _userManager.FindByEmailAsync(email);
        if (user == null)
        {
            ModelState.AddModelError(string.Empty, "Invalid login attempt.");
            return View(model);
        }

        var result = await _twoFactorService.VerifyTwoFactorTokenAsync(user, model.Code);
        if (!result)
        {
            ModelState.AddModelError(string.Empty, "Geçersiz doğrulama kodu.");
            return View(model);
        }

        // Sign in the user
        var rememberMe = TempData["RememberMe"] as bool? ?? false;
        await _signInManager.SignInAsync(user, rememberMe);

        // Handle "Remember Machine" for 30 days
        if (model.RememberMachine)
        {
            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddDays(30),
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict
            };

            Response.Cookies.Append($"RememberMachine_{user.Id}", "true", cookieOptions);
        }

        // Update last login time
        user.LastLoginAt = DateTime.UtcNow;
        await _userManager.UpdateAsync(user);

        // Create session record
        try
        {
            var sessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
            await _sessionService.CreateSessionAsync(user.Id, sessionId, HttpContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating session for user {UserId}", user.Id);
        }

        _logger.LogInformation("User {Email} logged in with 2FA.", email);

        return RedirectToLocal(model.ReturnUrl);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;

        if (ModelState.IsValid)
        {
            var result = await _signInManager.PasswordSignInAsync(
                model.Email,
                model.Password,
                model.RememberMe,
                lockoutOnFailure: true);

            if (result.Succeeded || result.RequiresTwoFactor)
            {
                _logger.LogInformation("User logged in.");

                // Check if user has 2FA enabled
                var user = await _userManager.FindByEmailAsync(model.Email);
                if (user != null && user.TwoFactorEnabled && !string.IsNullOrEmpty(user.TwoFactorSecretKey))
                {
                    // Check if this machine is remembered for 30 days
                    var rememberMachineCookie = Request.Cookies[$"RememberMachine_{user.Id}"];
                    if (rememberMachineCookie == "true")
                    {
                        // Machine is remembered, skip 2FA
                        _logger.LogInformation("User {Email} logged in with remembered machine, skipping 2FA.", model.Email);
                    }
                    else
                    {
                        // Sign out the user and redirect to 2FA verification
                        await _signInManager.SignOutAsync();
                        TempData["Email"] = model.Email;
                        TempData["RememberMe"] = model.RememberMe;
                        return RedirectToAction("VerifyTwoFactor", new { returnUrl });
                    }
                }

                // Update last login time
                if (user != null)
                {
                    user.LastLoginAt = DateTime.UtcNow;
                    await _userManager.UpdateAsync(user);

                    // Create session record
                    try
                    {
                        var sessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
                        await _sessionService.CreateSessionAsync(user.Id, sessionId, HttpContext);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error creating session for user {UserId}", user.Id);
                    }
                }

                return RedirectToLocal(returnUrl);
            }

            if (result.IsLockedOut)
            {
                _logger.LogWarning("User account locked out.");
                return RedirectToAction(nameof(Lockout));
            }
            else
            {
                ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                return View(model);
            }
        }

        // If we got this far, something failed, redisplay form
        return View(model);
    }

    [HttpGet]
    public IActionResult Register(string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Register(RegisterViewModel model, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;

        if (ModelState.IsValid)
        {
            // Check if company with the same name already exists
            var existingCompany = await _context.Companies
                .FirstOrDefaultAsync(c => c.Name.ToLower() == model.CompanyName.ToLower());

            if (existingCompany != null)
            {
                ModelState.AddModelError("CompanyName", "Bu firma adı zaten kayıtlı. Lütfen farklı bir firma adı giriniz.");
                return View(model);
            }

            // Create new company
            var company = new Company
            {
                Name = model.CompanyName,
                CreatedAt = DateTime.UtcNow
            };

            _context.Companies.Add(company);
            await _context.SaveChangesAsync();

            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                CompanyId = company.Id,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                _logger.LogInformation("User created a new account with password.");

                // Add user to the User and CompanyOwner roles
                await _userManager.AddToRoleAsync(user, "User");
                await _userManager.AddToRoleAsync(user, "CompanyOwner");

                await _signInManager.SignInAsync(user, isPersistent: false);
                _logger.LogInformation("User signed in after registration.");

                return RedirectToLocal(returnUrl);
            }

            // If user creation failed, remove the company we just created
            _context.Companies.Remove(company);
            await _context.SaveChangesAsync();

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }
        }

        // If we got this far, something failed, redisplay form
        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        await _signInManager.SignOutAsync();
        _logger.LogInformation("User logged out.");

        return RedirectToAction(nameof(HomeController.Index), "Home");
    }

    [HttpGet]
    public async Task<IActionResult> AcceptInvitation(string? token)
    {
        if (string.IsNullOrEmpty(token))
        {
            TempData["ErrorMessage"] = "Geçersiz davetiye bağlantısı.";
            return RedirectToAction(nameof(Register));
        }

        var validationResult = await _invitationService.ValidateInvitationTokenAsync(token);
        if (!validationResult.IsValid)
        {
            TempData["ErrorMessage"] = validationResult.Message;
            return RedirectToAction(nameof(Register));
        }

        var model = new AcceptInvitationRequest
        {
            Token = token
        };

        ViewData["CompanyName"] = validationResult.CompanyName;
        ViewData["InviterName"] = validationResult.InviterName;
        ViewData["ExpirationDate"] = validationResult.ExpirationDate;

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AcceptInvitation(AcceptInvitationRequest model)
    {
        if (!ModelState.IsValid)
        {
            var validationResult = await _invitationService.ValidateInvitationTokenAsync(model.Token);
            if (validationResult.IsValid)
            {
                ViewData["CompanyName"] = validationResult.CompanyName;
                ViewData["InviterName"] = validationResult.InviterName;
                ViewData["ExpirationDate"] = validationResult.ExpirationDate;
            }
            return View(model);
        }

        try
        {
            // Validate invitation token again
            var validationResult = await _invitationService.ValidateInvitationTokenAsync(model.Token);
            if (!validationResult.IsValid)
            {
                ModelState.AddModelError("", validationResult.Message ?? "Geçersiz davetiye.");
                return View(model);
            }

            // Get invitation details
            var invitation = await _context.UserInvitations
                .Include(i => i.Company)
                .FirstOrDefaultAsync(i => i.InvitationToken == model.Token && !i.IsUsed && i.ExpirationDate > DateTime.UtcNow);

            if (invitation == null)
            {
                ModelState.AddModelError("", "Davetiye bulunamadı veya geçersiz.");
                return View(model);
            }

            // Check if user already exists
            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "Bu e-posta adresi ile kayıtlı bir kullanıcı zaten mevcut.");
                ViewData["CompanyName"] = validationResult.CompanyName;
                ViewData["InviterName"] = validationResult.InviterName;
                ViewData["ExpirationDate"] = validationResult.ExpirationDate;
                return View(model);
            }

            // Create new user
            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                PhoneNumber = model.PhoneNumber,
                CompanyId = invitation.CompanyId,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                _logger.LogInformation("User created via invitation: {Email} for company {CompanyId}", model.Email, invitation.CompanyId);

                // Add user to the User role (not CompanyOwner)
                await _userManager.AddToRoleAsync(user, "User");

                // Mark invitation as used
                invitation.IsUsed = true;
                invitation.UsedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Sign in the user
                await _signInManager.SignInAsync(user, isPersistent: false);
                _logger.LogInformation("User signed in after accepting invitation.");

                TempData["SuccessMessage"] = $"Hoş geldiniz! {invitation.Company.Name} firmasına başarıyla katıldınız.";
                return RedirectToAction("Index", "Home");
            }

            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            ViewData["CompanyName"] = validationResult.CompanyName;
            ViewData["InviterName"] = validationResult.InviterName;
            ViewData["ExpirationDate"] = validationResult.ExpirationDate;
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting invitation for token {Token}", model.Token);
            ModelState.AddModelError("", "Davetiye kabul edilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");

            var validationResult = await _invitationService.ValidateInvitationTokenAsync(model.Token);
            if (validationResult.IsValid)
            {
                ViewData["CompanyName"] = validationResult.CompanyName;
                ViewData["InviterName"] = validationResult.InviterName;
                ViewData["ExpirationDate"] = validationResult.ExpirationDate;
            }
            return View(model);
        }
    }

    [HttpGet]
    public IActionResult Lockout()
    {
        return View();
    }

    [HttpGet]
    public IActionResult AccessDenied()
    {
        return View();
    }

    [Authorize]
    [HttpGet]
    public async Task<IActionResult> Profile()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return NotFound();
        }

        var model = new ProfileViewModel
        {
            FirstName = user.FirstName,
            LastName = user.LastName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber
        };

        return View(model);
    }

    [Authorize]
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Profile(ProfileViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return NotFound();
        }

        user.FirstName = model.FirstName;
        user.LastName = model.LastName;
        user.PhoneNumber = model.PhoneNumber;

        var result = await _userManager.UpdateAsync(user);

        // Create session record
        try
        {
            var sessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
            await _sessionService.CreateSessionAsync(user.Id, sessionId, HttpContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating session for user {UserId}", user.Id);
        }

        if (!result.Succeeded)
        {
            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            return View(model);
        }

        TempData["StatusMessage"] = "Your profile has been updated";
        return RedirectToAction(nameof(Profile));
    }

    [Authorize]
    [HttpGet]
    public IActionResult ChangePassword()
    {
        return View();
    }

    [Authorize]
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return NotFound();
        }

        var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);

        if (!result.Succeeded)
        {
            foreach (var error in result.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            return View(model);
        }

        await _signInManager.RefreshSignInAsync(user);
        _logger.LogInformation("User changed their password successfully.");

        TempData["StatusMessage"] = "Your password has been changed.";
        return RedirectToAction(nameof(Profile));
    }


    [Authorize]
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangePasswordAjax([FromBody] ChangePasswordRequest model)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            _logger.LogInformation("ChangePasswordAjax called");

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);

            if (!result.Succeeded)
            {
                var errorMessages = result.Errors.Select(e => e.Description).ToList();
                return Json(new { success = false, message = string.Join(" ", errorMessages) });
            }

            await _signInManager.RefreshSignInAsync(user);
            _logger.LogInformation("User changed their password successfully via AJAX.");

            return Json(new { success = true, message = "Şifreniz başarıyla değiştirildi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password via AJAX");
            return Json(new { success = false, message = "Şifre değiştirilirken bir hata oluştu." });
        }
    }

    private IActionResult RedirectToLocal(string? returnUrl)
    {
        if (Url.IsLocalUrl(returnUrl))
        {
            return Redirect(returnUrl);
        }
        else
        {
            return RedirectToAction(nameof(HomeController.Index), "Home");
        }
    }
}