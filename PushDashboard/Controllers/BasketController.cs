using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using PushDashboard.Services;
using PushDashboard.ViewModels;
using PushDashboard.Data;

namespace PushDashboard.Controllers;

[Authorize]
public class BasketController : BaseController
{
    private readonly IBasketService _basketService;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<BasketController> _logger;

    public BasketController(IBasketService basketService, ApplicationDbContext context, ILogger<BasketController> logger)
    {
        _basketService = basketService;
        _context = context;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index", "Home");
        }

        // İlk yüklemede sadece boş bir view model döndür, veriler AJAX ile yüklenecek
        var emptyViewModel = new BasketIndexViewModel
        {
            Baskets = new List<BasketIndexViewModel.BasketViewModel>(),
            Stats = new BasketIndexViewModel.BasketStatsViewModel(),
            Pagination = new BasketIndexViewModel.PaginationViewModel(),
            RecentSyncLogs = new List<BasketIndexViewModel.BasketSyncLogViewModel>()
        };

        return View(emptyViewModel);
    }

    [HttpGet]
    public async Task<IActionResult> GetBaskets(int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var viewModel = await _basketService.GetBasketsAsync(companyId.Value, page, pageSize, searchTerm, status, sortBy, sortDirection);

            return Json(new {
                success = true,
                data = new {
                    baskets = viewModel.Baskets,
                    stats = viewModel.Stats,
                    pagination = viewModel.Pagination
                }
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Sepetler yüklenirken hata oluştu: " + ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> SyncBaskets()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _basketService.SyncBasketsAsync(companyId.Value);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetBasketDetails(string guidBasketId)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (string.IsNullOrEmpty(guidBasketId))
        {
            return Json(new { success = false, message = "Sepet ID'si gereklidir." });
        }

        try
        {
            var basket = await _basketService.GetBasketDetailsAsync(guidBasketId, companyId.Value);
            if (basket == null)
            {
                return Json(new { success = false, message = "Sepet bulunamadı." });
            }

            return Json(new { success = true, data = basket });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Sepet detayları yüklenirken hata oluştu: " + ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetSyncLogs()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var logs = await _basketService.GetRecentSyncLogsAsync(companyId.Value, 10);
            return Json(new { success = true, data = logs });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Sync logları yüklenirken hata oluştu: " + ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetBasketItemsPaged(string guidBasketId, int page = 1, int pageSize = 10)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (string.IsNullOrEmpty(guidBasketId))
        {
            return Json(new { success = false, message = "Sepet ID'si gereklidir." });
        }

        try
        {
            var pagedItems = await _basketService.GetBasketItemsPagedAsync(guidBasketId, companyId.Value, page, pageSize);
            return Json(new { success = true, data = pagedItems });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged basket items for basket {BasketId}", guidBasketId);
            return Json(new { success = false, message = "Sepet ürünleri alınırken bir hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetCurrencyTotals()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var currencyTotals = await _basketService.GetCurrencyTotalsAsync(companyId.Value);
            return Json(new { success = true, data = currencyTotals });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting currency totals for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Para birimi toplamları alınırken bir hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetBasketsByCustomer(int customerId)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var baskets = await _basketService.GetBasketsByCustomerAsync(customerId, companyId.Value);
            return Json(new { success = true, data = baskets });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting baskets for customer {CustomerId}", customerId);
            return Json(new { success = false, message = "Müşteri sepetleri alınırken bir hata oluştu." });
        }
    }
}
