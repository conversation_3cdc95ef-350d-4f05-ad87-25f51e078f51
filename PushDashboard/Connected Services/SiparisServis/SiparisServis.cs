//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PushDashboard.SiparisServis
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisFiltre : object
    {
        
        private System.Nullable<System.DateTime> DurumTarihiBasField;
        
        private System.Nullable<System.DateTime> DurumTarihiSonField;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihiBasField;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihiSonField;
        
        private System.Nullable<int> EFaturaURLField;
        
        private int EntegrasyonAktarildiField;
        
        private PushDashboard.SiparisServis.WebSiparisEntegrasyon EntegrasyonParamsField;
        
        private string FaturaNoField;
        
        private bool IptalEdilmisUrunlerField;
        
        private bool KampanyaGetirField;
        
        private System.Nullable<int> KargoEntegrasyonTakipDurumuField;
        
        private int KargoFirmaIDField;
        
        private int OdemeDurumuField;
        
        private System.Nullable<bool> OdemeGetirField;
        
        private System.Nullable<int> OdemeTamamlandiField;
        
        private int OdemeTipiField;
        
        private System.Nullable<int> PaketlemeDurumuField;
        
        private System.Nullable<int> PazaryeriIhracatField;
        
        private int SiparisDurumuField;
        
        private int SiparisIDField;
        
        private string SiparisKaynagiField;
        
        private string SiparisKoduField;
        
        private string SiparisNoField;
        
        private System.Nullable<System.DateTime> SiparisTarihiBasField;
        
        private System.Nullable<System.DateTime> SiparisTarihiSonField;
        
        private string StrPaketlemeDurumuField;
        
        private string StrSiparisDurumuField;
        
        private string StrSiparisIDField;
        
        private int TedarikciIDField;
        
        private System.Nullable<System.DateTime> TeslimatGunuBasField;
        
        private System.Nullable<System.DateTime> TeslimatGunuSonField;
        
        private System.Nullable<int> TeslimatMagazaIDField;
        
        private System.Nullable<bool> UrunGetirField;
        
        private int UyeIDField;
        
        private string UyeTelefonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DurumTarihiBas
        {
            get
            {
                return this.DurumTarihiBasField;
            }
            set
            {
                this.DurumTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DurumTarihiSon
        {
            get
            {
                return this.DurumTarihiSonField;
            }
            set
            {
                this.DurumTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihiBas
        {
            get
            {
                return this.DuzenlemeTarihiBasField;
            }
            set
            {
                this.DuzenlemeTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihiSon
        {
            get
            {
                return this.DuzenlemeTarihiSonField;
            }
            set
            {
                this.DuzenlemeTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> EFaturaURL
        {
            get
            {
                return this.EFaturaURLField;
            }
            set
            {
                this.EFaturaURLField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EntegrasyonAktarildi
        {
            get
            {
                return this.EntegrasyonAktarildiField;
            }
            set
            {
                this.EntegrasyonAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisEntegrasyon EntegrasyonParams
        {
            get
            {
                return this.EntegrasyonParamsField;
            }
            set
            {
                this.EntegrasyonParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaNo
        {
            get
            {
                return this.FaturaNoField;
            }
            set
            {
                this.FaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IptalEdilmisUrunler
        {
            get
            {
                return this.IptalEdilmisUrunlerField;
            }
            set
            {
                this.IptalEdilmisUrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KampanyaGetir
        {
            get
            {
                return this.KampanyaGetirField;
            }
            set
            {
                this.KampanyaGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> KargoEntegrasyonTakipDurumu
        {
            get
            {
                return this.KargoEntegrasyonTakipDurumuField;
            }
            set
            {
                this.KargoEntegrasyonTakipDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoFirmaID
        {
            get
            {
                return this.KargoFirmaIDField;
            }
            set
            {
                this.KargoFirmaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeDurumu
        {
            get
            {
                return this.OdemeDurumuField;
            }
            set
            {
                this.OdemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> OdemeGetir
        {
            get
            {
                return this.OdemeGetirField;
            }
            set
            {
                this.OdemeGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OdemeTamamlandi
        {
            get
            {
                return this.OdemeTamamlandiField;
            }
            set
            {
                this.OdemeTamamlandiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PaketlemeDurumu
        {
            get
            {
                return this.PaketlemeDurumuField;
            }
            set
            {
                this.PaketlemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PazaryeriIhracat
        {
            get
            {
                return this.PazaryeriIhracatField;
            }
            set
            {
                this.PazaryeriIhracatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisDurumu
        {
            get
            {
                return this.SiparisDurumuField;
            }
            set
            {
                this.SiparisDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKodu
        {
            get
            {
                return this.SiparisKoduField;
            }
            set
            {
                this.SiparisKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiBas
        {
            get
            {
                return this.SiparisTarihiBasField;
            }
            set
            {
                this.SiparisTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiSon
        {
            get
            {
                return this.SiparisTarihiSonField;
            }
            set
            {
                this.SiparisTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StrPaketlemeDurumu
        {
            get
            {
                return this.StrPaketlemeDurumuField;
            }
            set
            {
                this.StrPaketlemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StrSiparisDurumu
        {
            get
            {
                return this.StrSiparisDurumuField;
            }
            set
            {
                this.StrSiparisDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StrSiparisID
        {
            get
            {
                return this.StrSiparisIDField;
            }
            set
            {
                this.StrSiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TedarikciID
        {
            get
            {
                return this.TedarikciIDField;
            }
            set
            {
                this.TedarikciIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TeslimatGunuBas
        {
            get
            {
                return this.TeslimatGunuBasField;
            }
            set
            {
                this.TeslimatGunuBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TeslimatGunuSon
        {
            get
            {
                return this.TeslimatGunuSonField;
            }
            set
            {
                this.TeslimatGunuSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> TeslimatMagazaID
        {
            get
            {
                return this.TeslimatMagazaIDField;
            }
            set
            {
                this.TeslimatMagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> UrunGetir
        {
            get
            {
                return this.UrunGetirField;
            }
            set
            {
                this.UrunGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeTelefon
        {
            get
            {
                return this.UyeTelefonField;
            }
            set
            {
                this.UyeTelefonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisEntegrasyon", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisEntegrasyon : object
    {
        
        private string AlanDegerField;
        
        private string DegerField;
        
        private string EntegrasyonKoduField;
        
        private bool EntegrasyonParamsAktifField;
        
        private string TabloAlanField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AlanDeger
        {
            get
            {
                return this.AlanDegerField;
            }
            set
            {
                this.AlanDegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Deger
        {
            get
            {
                return this.DegerField;
            }
            set
            {
                this.DegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EntegrasyonKodu
        {
            get
            {
                return this.EntegrasyonKoduField;
            }
            set
            {
                this.EntegrasyonKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EntegrasyonParamsAktif
        {
            get
            {
                return this.EntegrasyonParamsAktifField;
            }
            set
            {
                this.EntegrasyonParamsAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TabloAlan
        {
            get
            {
                return this.TabloAlanField;
            }
            set
            {
                this.TabloAlanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisSayfalama", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisSayfalama : object
    {
        
        private int BaslangicIndexField;
        
        private int KayitSayisiField;
        
        private string SiralamaDegeriField;
        
        private string SiralamaYonuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BaslangicIndex
        {
            get
            {
                return this.BaslangicIndexField;
            }
            set
            {
                this.BaslangicIndexField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KayitSayisi
        {
            get
            {
                return this.KayitSayisiField;
            }
            set
            {
                this.KayitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaDegeri
        {
            get
            {
                return this.SiralamaDegeriField;
            }
            set
            {
                this.SiralamaDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaYonu
        {
            get
            {
                return this.SiralamaYonuField;
            }
            set
            {
                this.SiralamaYonuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparis", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparis : object
    {
        
        private string AdiSoyadiField;
        
        private bool AfadYardimSiparisField;
        
        private string BNPLNoField;
        
        private double BakiyeField;
        
        private string DigerFaturaNoField;
        
        private int DurumField;
        
        private System.Nullable<System.DateTime> DurumGuncellemeTarihiField;
        
        private System.DateTime DuzenlemeTarihiField;
        
        private string EFaturaUrlField;
        
        private bool EntegrasyonAktarildiField;
        
        private int FaturaAdresIDField;
        
        private PushDashboard.SiparisServis.FaturaAdres FaturaAdresiField;
        
        private string FaturaNoField;
        
        private System.DateTime FaturaTarihiField;
        
        private string FaturaUUIDField;
        
        private string FaturaUrlField;
        
        private string HediyeCekiField;
        
        private int HediyeCekiOlusturmaTipiField;
        
        private string HediyeCekiOlusturmaTipiTanimField;
        
        private double HediyeCekiTutariField;
        
        private string HediyePaketiNotuField;
        
        private double HediyePaketiTutariField;
        
        private bool HediyePaketiVarField;
        
        private int IDField;
        
        private string IPAdresiField;
        
        private string IadeKomisyonFaturaNoField;
        
        private double IadeKomisyonTutariField;
        
        private bool IndirimDagitildiField;
        
        private double IndirimTutariField;
        
        private bool IsMarketplaceField;
        
        private double IscilikTutariField;
        
        private PushDashboard.SiparisServis.WebSiparisKampanya[] KampanyalarField;
        
        private int KargoAdresIDField;
        
        private int KargoEntegrasyonIDField;
        
        private string KargoEntegrasyonTakipNoField;
        
        private int KargoFirmaIDField;
        
        private string KargoFirmaTanimField;
        
        private PushDashboard.SiparisServis.WebSiparisKargoGondericiBilgi KargoGondericiBilgiField;
        
        private string KargoKatkiPayiFaturaNoField;
        
        private string KargoTakipLinkField;
        
        private string KargoTakipNoField;
        
        private double KargoTutariField;
        
        private System.Nullable<System.DateTime> KargoyaSonVerilmeTarihiField;
        
        private int KaynakField;
        
        private string KomisyonFaturaNoField;
        
        private double KurField;
        
        private string MagazaTeslimKoduField;
        
        private bool MagazadanTeslimField;
        
        private string MailField;
        
        private double MaliyetField;
        
        private bool MarketPlaceOdemeAlindiField;
        
        private string MarketplaceKampanyaKoduField;
        
        private string MarketplaceParamsField;
        
        private System.Nullable<System.DateTime> MusteriKargoTeslimTarihiField;
        
        private string OdemeLinkiField;
        
        private System.Nullable<System.DateTime> OdemeVadeTarihiField;
        
        private PushDashboard.SiparisServis.WebSiparisOdeme[] OdemelerField;
        
        private double OdenenTutarField;
        
        private int OlusturanIdField;
        
        private string OzelAlan1Field;
        
        private string OzelAlan2Field;
        
        private string OzelAlan3Field;
        
        private double OzellestirmeTutariField;
        
        private string PaketlemeDurumuField;
        
        private int PaketlemeDurumuIDField;
        
        private string ParaBirimiField;
        
        private int PazaryeriButikIdField;
        
        private int PazaryeriIhracatField;
        
        private double PazaryeriKomisyonTutariField;
        
        private double PazaryeriOdemeTutariField;
        
        private double PuanIndirimiField;
        
        private PushDashboard.SiparisServis.PuanKullanim PuanKullanimField;
        
        private string ReferansNoField;
        
        private string RefererField;
        
        private string ReklamKaynagiField;
        
        private double SepetKampanyasiIndirimiField;
        
        private string SiparisDiliField;
        
        private string SiparisDurumuField;
        
        private string SiparisKaynagiField;
        
        private string SiparisKoduField;
        
        private string SiparisNoField;
        
        private string SiparisNotuField;
        
        private System.DateTime SiparisTarihiField;
        
        private double SiparisToplamTutariField;
        
        private bool StokDustuField;
        
        private string TeslimMagazaKoduField;
        
        private PushDashboard.SiparisServis.TeslimatAdres TeslimatAdresiField;
        
        private System.DateTime TeslimatGunuField;
        
        private int TeslimatMagazaIDField;
        
        private string TeslimatSaatiField;
        
        private double ToplamKdvField;
        
        private double ToplamTutarField;
        
        private double TutarField;
        
        private PushDashboard.SiparisServis.WebSiparisUrun[] UrunlerField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        private string UyeMusteriKoduField;
        
        private bool UyeSilindiField;
        
        private string UyeSoyadiField;
        
        private int UyeTuruIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdiSoyadi
        {
            get
            {
                return this.AdiSoyadiField;
            }
            set
            {
                this.AdiSoyadiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AfadYardimSiparis
        {
            get
            {
                return this.AfadYardimSiparisField;
            }
            set
            {
                this.AfadYardimSiparisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BNPLNo
        {
            get
            {
                return this.BNPLNoField;
            }
            set
            {
                this.BNPLNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Bakiye
        {
            get
            {
                return this.BakiyeField;
            }
            set
            {
                this.BakiyeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DigerFaturaNo
        {
            get
            {
                return this.DigerFaturaNoField;
            }
            set
            {
                this.DigerFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DurumGuncellemeTarihi
        {
            get
            {
                return this.DurumGuncellemeTarihiField;
            }
            set
            {
                this.DurumGuncellemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DuzenlemeTarihi
        {
            get
            {
                return this.DuzenlemeTarihiField;
            }
            set
            {
                this.DuzenlemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EFaturaUrl
        {
            get
            {
                return this.EFaturaUrlField;
            }
            set
            {
                this.EFaturaUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool EntegrasyonAktarildi
        {
            get
            {
                return this.EntegrasyonAktarildiField;
            }
            set
            {
                this.EntegrasyonAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FaturaAdresID
        {
            get
            {
                return this.FaturaAdresIDField;
            }
            set
            {
                this.FaturaAdresIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.FaturaAdres FaturaAdresi
        {
            get
            {
                return this.FaturaAdresiField;
            }
            set
            {
                this.FaturaAdresiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaNo
        {
            get
            {
                return this.FaturaNoField;
            }
            set
            {
                this.FaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime FaturaTarihi
        {
            get
            {
                return this.FaturaTarihiField;
            }
            set
            {
                this.FaturaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaUUID
        {
            get
            {
                return this.FaturaUUIDField;
            }
            set
            {
                this.FaturaUUIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaUrl
        {
            get
            {
                return this.FaturaUrlField;
            }
            set
            {
                this.FaturaUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCeki
        {
            get
            {
                return this.HediyeCekiField;
            }
            set
            {
                this.HediyeCekiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HediyeCekiOlusturmaTipi
        {
            get
            {
                return this.HediyeCekiOlusturmaTipiField;
            }
            set
            {
                this.HediyeCekiOlusturmaTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCekiOlusturmaTipiTanim
        {
            get
            {
                return this.HediyeCekiOlusturmaTipiTanimField;
            }
            set
            {
                this.HediyeCekiOlusturmaTipiTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyeCekiTutari
        {
            get
            {
                return this.HediyeCekiTutariField;
            }
            set
            {
                this.HediyeCekiTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyePaketiNotu
        {
            get
            {
                return this.HediyePaketiNotuField;
            }
            set
            {
                this.HediyePaketiNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyePaketiTutari
        {
            get
            {
                return this.HediyePaketiTutariField;
            }
            set
            {
                this.HediyePaketiTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool HediyePaketiVar
        {
            get
            {
                return this.HediyePaketiVarField;
            }
            set
            {
                this.HediyePaketiVarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IPAdresi
        {
            get
            {
                return this.IPAdresiField;
            }
            set
            {
                this.IPAdresiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IadeKomisyonFaturaNo
        {
            get
            {
                return this.IadeKomisyonFaturaNoField;
            }
            set
            {
                this.IadeKomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IadeKomisyonTutari
        {
            get
            {
                return this.IadeKomisyonTutariField;
            }
            set
            {
                this.IadeKomisyonTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IndirimDagitildi
        {
            get
            {
                return this.IndirimDagitildiField;
            }
            set
            {
                this.IndirimDagitildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimTutari
        {
            get
            {
                return this.IndirimTutariField;
            }
            set
            {
                this.IndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsMarketplace
        {
            get
            {
                return this.IsMarketplaceField;
            }
            set
            {
                this.IsMarketplaceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IscilikTutari
        {
            get
            {
                return this.IscilikTutariField;
            }
            set
            {
                this.IscilikTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisKampanya[] Kampanyalar
        {
            get
            {
                return this.KampanyalarField;
            }
            set
            {
                this.KampanyalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoAdresID
        {
            get
            {
                return this.KargoAdresIDField;
            }
            set
            {
                this.KargoAdresIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoEntegrasyonID
        {
            get
            {
                return this.KargoEntegrasyonIDField;
            }
            set
            {
                this.KargoEntegrasyonIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoEntegrasyonTakipNo
        {
            get
            {
                return this.KargoEntegrasyonTakipNoField;
            }
            set
            {
                this.KargoEntegrasyonTakipNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoFirmaID
        {
            get
            {
                return this.KargoFirmaIDField;
            }
            set
            {
                this.KargoFirmaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoFirmaTanim
        {
            get
            {
                return this.KargoFirmaTanimField;
            }
            set
            {
                this.KargoFirmaTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisKargoGondericiBilgi KargoGondericiBilgi
        {
            get
            {
                return this.KargoGondericiBilgiField;
            }
            set
            {
                this.KargoGondericiBilgiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoKatkiPayiFaturaNo
        {
            get
            {
                return this.KargoKatkiPayiFaturaNoField;
            }
            set
            {
                this.KargoKatkiPayiFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNo
        {
            get
            {
                return this.KargoTakipNoField;
            }
            set
            {
                this.KargoTakipNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoTutari
        {
            get
            {
                return this.KargoTutariField;
            }
            set
            {
                this.KargoTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> KargoyaSonVerilmeTarihi
        {
            get
            {
                return this.KargoyaSonVerilmeTarihiField;
            }
            set
            {
                this.KargoyaSonVerilmeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Kaynak
        {
            get
            {
                return this.KaynakField;
            }
            set
            {
                this.KaynakField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KomisyonFaturaNo
        {
            get
            {
                return this.KomisyonFaturaNoField;
            }
            set
            {
                this.KomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Kur
        {
            get
            {
                return this.KurField;
            }
            set
            {
                this.KurField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaTeslimKodu
        {
            get
            {
                return this.MagazaTeslimKoduField;
            }
            set
            {
                this.MagazaTeslimKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MagazadanTeslim
        {
            get
            {
                return this.MagazadanTeslimField;
            }
            set
            {
                this.MagazadanTeslimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Maliyet
        {
            get
            {
                return this.MaliyetField;
            }
            set
            {
                this.MaliyetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MarketPlaceOdemeAlindi
        {
            get
            {
                return this.MarketPlaceOdemeAlindiField;
            }
            set
            {
                this.MarketPlaceOdemeAlindiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceKampanyaKodu
        {
            get
            {
                return this.MarketplaceKampanyaKoduField;
            }
            set
            {
                this.MarketplaceKampanyaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceParams
        {
            get
            {
                return this.MarketplaceParamsField;
            }
            set
            {
                this.MarketplaceParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> MusteriKargoTeslimTarihi
        {
            get
            {
                return this.MusteriKargoTeslimTarihiField;
            }
            set
            {
                this.MusteriKargoTeslimTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeLinki
        {
            get
            {
                return this.OdemeLinkiField;
            }
            set
            {
                this.OdemeLinkiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeVadeTarihi
        {
            get
            {
                return this.OdemeVadeTarihiField;
            }
            set
            {
                this.OdemeVadeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisOdeme[] Odemeler
        {
            get
            {
                return this.OdemelerField;
            }
            set
            {
                this.OdemelerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OdenenTutar
        {
            get
            {
                return this.OdenenTutarField;
            }
            set
            {
                this.OdenenTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OlusturanId
        {
            get
            {
                return this.OlusturanIdField;
            }
            set
            {
                this.OlusturanIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan1
        {
            get
            {
                return this.OzelAlan1Field;
            }
            set
            {
                this.OzelAlan1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan2
        {
            get
            {
                return this.OzelAlan2Field;
            }
            set
            {
                this.OzelAlan2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan3
        {
            get
            {
                return this.OzelAlan3Field;
            }
            set
            {
                this.OzelAlan3Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OzellestirmeTutari
        {
            get
            {
                return this.OzellestirmeTutariField;
            }
            set
            {
                this.OzellestirmeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PaketlemeDurumu
        {
            get
            {
                return this.PaketlemeDurumuField;
            }
            set
            {
                this.PaketlemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PaketlemeDurumuID
        {
            get
            {
                return this.PaketlemeDurumuIDField;
            }
            set
            {
                this.PaketlemeDurumuIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriButikId
        {
            get
            {
                return this.PazaryeriButikIdField;
            }
            set
            {
                this.PazaryeriButikIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriIhracat
        {
            get
            {
                return this.PazaryeriIhracatField;
            }
            set
            {
                this.PazaryeriIhracatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriKomisyonTutari
        {
            get
            {
                return this.PazaryeriKomisyonTutariField;
            }
            set
            {
                this.PazaryeriKomisyonTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriOdemeTutari
        {
            get
            {
                return this.PazaryeriOdemeTutariField;
            }
            set
            {
                this.PazaryeriOdemeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PuanIndirimi
        {
            get
            {
                return this.PuanIndirimiField;
            }
            set
            {
                this.PuanIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.PuanKullanim PuanKullanim
        {
            get
            {
                return this.PuanKullanimField;
            }
            set
            {
                this.PuanKullanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ReferansNo
        {
            get
            {
                return this.ReferansNoField;
            }
            set
            {
                this.ReferansNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Referer
        {
            get
            {
                return this.RefererField;
            }
            set
            {
                this.RefererField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ReklamKaynagi
        {
            get
            {
                return this.ReklamKaynagiField;
            }
            set
            {
                this.ReklamKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SepetKampanyasiIndirimi
        {
            get
            {
                return this.SepetKampanyasiIndirimiField;
            }
            set
            {
                this.SepetKampanyasiIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisDili
        {
            get
            {
                return this.SiparisDiliField;
            }
            set
            {
                this.SiparisDiliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisDurumu
        {
            get
            {
                return this.SiparisDurumuField;
            }
            set
            {
                this.SiparisDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKodu
        {
            get
            {
                return this.SiparisKoduField;
            }
            set
            {
                this.SiparisKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNotu
        {
            get
            {
                return this.SiparisNotuField;
            }
            set
            {
                this.SiparisNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SiparisTarihi
        {
            get
            {
                return this.SiparisTarihiField;
            }
            set
            {
                this.SiparisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SiparisToplamTutari
        {
            get
            {
                return this.SiparisToplamTutariField;
            }
            set
            {
                this.SiparisToplamTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool StokDustu
        {
            get
            {
                return this.StokDustuField;
            }
            set
            {
                this.StokDustuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TeslimMagazaKodu
        {
            get
            {
                return this.TeslimMagazaKoduField;
            }
            set
            {
                this.TeslimMagazaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.TeslimatAdres TeslimatAdresi
        {
            get
            {
                return this.TeslimatAdresiField;
            }
            set
            {
                this.TeslimatAdresiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime TeslimatGunu
        {
            get
            {
                return this.TeslimatGunuField;
            }
            set
            {
                this.TeslimatGunuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TeslimatMagazaID
        {
            get
            {
                return this.TeslimatMagazaIDField;
            }
            set
            {
                this.TeslimatMagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TeslimatSaati
        {
            get
            {
                return this.TeslimatSaatiField;
            }
            set
            {
                this.TeslimatSaatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamKdv
        {
            get
            {
                return this.ToplamKdvField;
            }
            set
            {
                this.ToplamKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamTutar
        {
            get
            {
                return this.ToplamTutarField;
            }
            set
            {
                this.ToplamTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeMusteriKodu
        {
            get
            {
                return this.UyeMusteriKoduField;
            }
            set
            {
                this.UyeMusteriKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyeSilindi
        {
            get
            {
                return this.UyeSilindiField;
            }
            set
            {
                this.UyeSilindiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeSoyadi
        {
            get
            {
                return this.UyeSoyadiField;
            }
            set
            {
                this.UyeSoyadiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeTuruId
        {
            get
            {
                return this.UyeTuruIdField;
            }
            set
            {
                this.UyeTuruIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FaturaAdres", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class FaturaAdres : object
    {
        
        private string AdresField;
        
        private string AliciTelefonField;
        
        private string EntegrasyonIdField;
        
        private string FirmaAdiField;
        
        private int IDField;
        
        private string IlField;
        
        private int IlIDField;
        
        private string IlKoduField;
        
        private string IlceField;
        
        private int IlceIDField;
        
        private string IlceKoduField;
        
        private string MahalleField;
        
        private int MahalleIDField;
        
        private string MahalleKoduField;
        
        private string PostaKoduField;
        
        private string SemtField;
        
        private int SemtIDField;
        
        private string SemtKoduField;
        
        private PushDashboard.SiparisServis.WebUlke UlkeField;
        
        private string VergiDairesiField;
        
        private string VergiNoField;
        
        private bool isKurumsalField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciTelefon
        {
            get
            {
                return this.AliciTelefonField;
            }
            set
            {
                this.AliciTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EntegrasyonId
        {
            get
            {
                return this.EntegrasyonIdField;
            }
            set
            {
                this.EntegrasyonIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FirmaAdi
        {
            get
            {
                return this.FirmaAdiField;
            }
            set
            {
                this.FirmaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IlKodu
        {
            get
            {
                return this.IlKoduField;
            }
            set
            {
                this.IlKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceID
        {
            get
            {
                return this.IlceIDField;
            }
            set
            {
                this.IlceIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IlceKodu
        {
            get
            {
                return this.IlceKoduField;
            }
            set
            {
                this.IlceKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mahalle
        {
            get
            {
                return this.MahalleField;
            }
            set
            {
                this.MahalleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MahalleID
        {
            get
            {
                return this.MahalleIDField;
            }
            set
            {
                this.MahalleIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MahalleKodu
        {
            get
            {
                return this.MahalleKoduField;
            }
            set
            {
                this.MahalleKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PostaKodu
        {
            get
            {
                return this.PostaKoduField;
            }
            set
            {
                this.PostaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Semt
        {
            get
            {
                return this.SemtField;
            }
            set
            {
                this.SemtField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SemtID
        {
            get
            {
                return this.SemtIDField;
            }
            set
            {
                this.SemtIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SemtKodu
        {
            get
            {
                return this.SemtKoduField;
            }
            set
            {
                this.SemtKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebUlke Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiDairesi
        {
            get
            {
                return this.VergiDairesiField;
            }
            set
            {
                this.VergiDairesiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiNo
        {
            get
            {
                return this.VergiNoField;
            }
            set
            {
                this.VergiNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool isKurumsal
        {
            get
            {
                return this.isKurumsalField;
            }
            set
            {
                this.isKurumsalField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisKargoGondericiBilgi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisKargoGondericiBilgi : object
    {
        
        private string KargoUnvanField;
        
        private string KargoVknField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoUnvan
        {
            get
            {
                return this.KargoUnvanField;
            }
            set
            {
                this.KargoUnvanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoVkn
        {
            get
            {
                return this.KargoVknField;
            }
            set
            {
                this.KargoVknField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PuanKullanim", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class PuanKullanim : object
    {
        
        private string DiscountPointTypeCodeField;
        
        private string KartNumarasiField;
        
        private double KazanilanPuanField;
        
        private double KazanilanPuanTLKarsiligiField;
        
        private double KullanilanPuanField;
        
        private double KullanilanPuanTLKarsiligiField;
        
        private System.DateTime KullanimTarihiField;
        
        private bool PuanBildirildiField;
        
        private int TipField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DiscountPointTypeCode
        {
            get
            {
                return this.DiscountPointTypeCodeField;
            }
            set
            {
                this.DiscountPointTypeCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KartNumarasi
        {
            get
            {
                return this.KartNumarasiField;
            }
            set
            {
                this.KartNumarasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KazanilanPuan
        {
            get
            {
                return this.KazanilanPuanField;
            }
            set
            {
                this.KazanilanPuanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KazanilanPuanTLKarsiligi
        {
            get
            {
                return this.KazanilanPuanTLKarsiligiField;
            }
            set
            {
                this.KazanilanPuanTLKarsiligiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KullanilanPuan
        {
            get
            {
                return this.KullanilanPuanField;
            }
            set
            {
                this.KullanilanPuanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KullanilanPuanTLKarsiligi
        {
            get
            {
                return this.KullanilanPuanTLKarsiligiField;
            }
            set
            {
                this.KullanilanPuanTLKarsiligiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime KullanimTarihi
        {
            get
            {
                return this.KullanimTarihiField;
            }
            set
            {
                this.KullanimTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PuanBildirildi
        {
            get
            {
                return this.PuanBildirildiField;
            }
            set
            {
                this.PuanBildirildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="TeslimatAdres", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class TeslimatAdres : object
    {
        
        private string AdresField;
        
        private string AdresTarifiField;
        
        private string AliciAdiField;
        
        private string AliciTelefonField;
        
        private int IDField;
        
        private string IlField;
        
        private int IlIDField;
        
        private string IlKoduField;
        
        private string IlceField;
        
        private int IlceIDField;
        
        private string IlceKoduField;
        
        private string MahalleField;
        
        private int MahalleIDField;
        
        private string MahalleKoduField;
        
        private string PostaKoduField;
        
        private string SemtField;
        
        private int SemtIDField;
        
        private string SemtKoduField;
        
        private PushDashboard.SiparisServis.WebUlke UlkeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdresTarifi
        {
            get
            {
                return this.AdresTarifiField;
            }
            set
            {
                this.AdresTarifiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciAdi
        {
            get
            {
                return this.AliciAdiField;
            }
            set
            {
                this.AliciAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciTelefon
        {
            get
            {
                return this.AliciTelefonField;
            }
            set
            {
                this.AliciTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IlKodu
        {
            get
            {
                return this.IlKoduField;
            }
            set
            {
                this.IlKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceID
        {
            get
            {
                return this.IlceIDField;
            }
            set
            {
                this.IlceIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IlceKodu
        {
            get
            {
                return this.IlceKoduField;
            }
            set
            {
                this.IlceKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mahalle
        {
            get
            {
                return this.MahalleField;
            }
            set
            {
                this.MahalleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MahalleID
        {
            get
            {
                return this.MahalleIDField;
            }
            set
            {
                this.MahalleIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MahalleKodu
        {
            get
            {
                return this.MahalleKoduField;
            }
            set
            {
                this.MahalleKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PostaKodu
        {
            get
            {
                return this.PostaKoduField;
            }
            set
            {
                this.PostaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Semt
        {
            get
            {
                return this.SemtField;
            }
            set
            {
                this.SemtField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SemtID
        {
            get
            {
                return this.SemtIDField;
            }
            set
            {
                this.SemtIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SemtKodu
        {
            get
            {
                return this.SemtKoduField;
            }
            set
            {
                this.SemtKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebUlke Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisKampanya", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisKampanya : object
    {
        
        private string KampanyaAciklamaField;
        
        private int KampanyaIDField;
        
        private string KampanyaTanimField;
        
        private int KampanyaTipiField;
        
        private int SiparisIDField;
        
        private int UrunIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KampanyaAciklama
        {
            get
            {
                return this.KampanyaAciklamaField;
            }
            set
            {
                this.KampanyaAciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KampanyaTanim
        {
            get
            {
                return this.KampanyaTanimField;
            }
            set
            {
                this.KampanyaTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaTipi
        {
            get
            {
                return this.KampanyaTipiField;
            }
            set
            {
                this.KampanyaTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisOdeme", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisOdeme : object
    {
        
        private double BankaKomisyonuField;
        
        private string CheckSumField;
        
        private PushDashboard.SiparisServis.OdemeEkBilgi EkBilgiField;
        
        private int HavaleBankaIDField;
        
        private int HavaleHesapIDField;
        
        private int IDField;
        
        private int KKOdemeBankaIDField;
        
        private double KapidaOdemeTutariField;
        
        private double OdemeIndirimiField;
        
        private string OdemeNotuField;
        
        private int OdemeSecenekIDField;
        
        private string OdemeSiparisIDField;
        
        private int OdemeTipiField;
        
        private int OnaylandiField;
        
        private string ParaBirimiField;
        
        private string PosReferansIDField;
        
        private int SiparisIDField;
        
        private int TaksitSayisiField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double BankaKomisyonu
        {
            get
            {
                return this.BankaKomisyonuField;
            }
            set
            {
                this.BankaKomisyonuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CheckSum
        {
            get
            {
                return this.CheckSumField;
            }
            set
            {
                this.CheckSumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.OdemeEkBilgi EkBilgi
        {
            get
            {
                return this.EkBilgiField;
            }
            set
            {
                this.EkBilgiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HavaleBankaID
        {
            get
            {
                return this.HavaleBankaIDField;
            }
            set
            {
                this.HavaleBankaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HavaleHesapID
        {
            get
            {
                return this.HavaleHesapIDField;
            }
            set
            {
                this.HavaleHesapIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KKOdemeBankaID
        {
            get
            {
                return this.KKOdemeBankaIDField;
            }
            set
            {
                this.KKOdemeBankaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KapidaOdemeTutari
        {
            get
            {
                return this.KapidaOdemeTutariField;
            }
            set
            {
                this.KapidaOdemeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OdemeIndirimi
        {
            get
            {
                return this.OdemeIndirimiField;
            }
            set
            {
                this.OdemeIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeNotu
        {
            get
            {
                return this.OdemeNotuField;
            }
            set
            {
                this.OdemeNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeSecenekID
        {
            get
            {
                return this.OdemeSecenekIDField;
            }
            set
            {
                this.OdemeSecenekIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeSiparisID
        {
            get
            {
                return this.OdemeSiparisIDField;
            }
            set
            {
                this.OdemeSiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Onaylandi
        {
            get
            {
                return this.OnaylandiField;
            }
            set
            {
                this.OnaylandiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PosReferansID
        {
            get
            {
                return this.PosReferansIDField;
            }
            set
            {
                this.PosReferansIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaksitSayisi
        {
            get
            {
                return this.TaksitSayisiField;
            }
            set
            {
                this.TaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisUrun : object
    {
        
        private double AdetField;
        
        private int AsortiGrupIdField;
        
        private string BarkodField;
        
        private int DurumField;
        
        private string DurumAdField;
        
        private PushDashboard.SiparisServis.WebSiparisUrunEkSecenekOzellik[] EkSecenekListField;
        
        private int EskiSiparisUrunIdField;
        
        private double FiyatIndirimOraniField;
        
        private double FiyatIndirimiField;
        
        private int FormIdField;
        
        private int[] FormIdListField;
        
        private string GtipKoduField;
        
        private double HediyeCekiIndirimiField;
        
        private int IDField;
        
        private string IadeNedenField;
        
        private int IadeNedenIdField;
        
        private double IscilikAgirlikField;
        
        private int IscilikParaBirimiIDField;
        
        private string IslemAdField;
        
        private int IslemIDField;
        
        private System.DateTime IslemTarihiField;
        
        private int KampanyaIDField;
        
        private double KampanyaIndirimOraniField;
        
        private double KampanyaIndirimTutariField;
        
        private int KargoFirmaIDField;
        
        private int KargoPaketIDField;
        
        private string KargoTakipLinkField;
        
        private string KargoTakipNumarasiField;
        
        private int KargoTipiField;
        
        private double KargoTutariField;
        
        private double KdvOraniField;
        
        private double KdvTutariField;
        
        private System.DateTime MagazaAtamaTarihiField;
        
        private int MagazaDurumField;
        
        private System.DateTime MagazaGonderimTarihiField;
        
        private int MagazaIDField;
        
        private string MagazaKoduField;
        
        private double MaliyetField;
        
        private string MarketplaceParamsField;
        
        private PushDashboard.SiparisServis.WebSepetOzellestirme[] OzellestirmeField;
        
        private string ResimYoluField;
        
        private double SatisAniIndirimliFiyatField;
        
        private double SatisAniIndirimliFiyatKdvField;
        
        private double SatisAniSatisFiyatField;
        
        private double SatisAniSatisFiyatKdvField;
        
        private string SatisBirimiField;
        
        private int SiparisIDField;
        
        private string StokKoduField;
        
        private int TedarikciIDField;
        
        private string TedarikciKoduField;
        
        private string TedarikciKodu2Field;
        
        private double ToplamIndirimField;
        
        private double TutarField;
        
        private string UrunAdiField;
        
        private double UrunAgirligiField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private string UrunOlusturmaGrupAdiField;
        
        private int UrunOlusturmaGrupIdField;
        
        private string UrunOlusturmaGuidField;
        
        private int UrunOlusturmaKartIdField;
        
        private string UrunOlusturmaUrunAdiField;
        
        private PushDashboard.SiparisServis.WebUrunTipi UrunTipiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int AsortiGrupId
        {
            get
            {
                return this.AsortiGrupIdField;
            }
            set
            {
                this.AsortiGrupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Barkod
        {
            get
            {
                return this.BarkodField;
            }
            set
            {
                this.BarkodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DurumAd
        {
            get
            {
                return this.DurumAdField;
            }
            set
            {
                this.DurumAdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisUrunEkSecenekOzellik[] EkSecenekList
        {
            get
            {
                return this.EkSecenekListField;
            }
            set
            {
                this.EkSecenekListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EskiSiparisUrunId
        {
            get
            {
                return this.EskiSiparisUrunIdField;
            }
            set
            {
                this.EskiSiparisUrunIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double FiyatIndirimOrani
        {
            get
            {
                return this.FiyatIndirimOraniField;
            }
            set
            {
                this.FiyatIndirimOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double FiyatIndirimi
        {
            get
            {
                return this.FiyatIndirimiField;
            }
            set
            {
                this.FiyatIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FormId
        {
            get
            {
                return this.FormIdField;
            }
            set
            {
                this.FormIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] FormIdList
        {
            get
            {
                return this.FormIdListField;
            }
            set
            {
                this.FormIdListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GtipKodu
        {
            get
            {
                return this.GtipKoduField;
            }
            set
            {
                this.GtipKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyeCekiIndirimi
        {
            get
            {
                return this.HediyeCekiIndirimiField;
            }
            set
            {
                this.HediyeCekiIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IadeNeden
        {
            get
            {
                return this.IadeNedenField;
            }
            set
            {
                this.IadeNedenField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeNedenId
        {
            get
            {
                return this.IadeNedenIdField;
            }
            set
            {
                this.IadeNedenIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IscilikAgirlik
        {
            get
            {
                return this.IscilikAgirlikField;
            }
            set
            {
                this.IscilikAgirlikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IscilikParaBirimiID
        {
            get
            {
                return this.IscilikParaBirimiIDField;
            }
            set
            {
                this.IscilikParaBirimiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IslemAd
        {
            get
            {
                return this.IslemAdField;
            }
            set
            {
                this.IslemAdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemID
        {
            get
            {
                return this.IslemIDField;
            }
            set
            {
                this.IslemIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime IslemTarihi
        {
            get
            {
                return this.IslemTarihiField;
            }
            set
            {
                this.IslemTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyaIndirimOrani
        {
            get
            {
                return this.KampanyaIndirimOraniField;
            }
            set
            {
                this.KampanyaIndirimOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyaIndirimTutari
        {
            get
            {
                return this.KampanyaIndirimTutariField;
            }
            set
            {
                this.KampanyaIndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoFirmaID
        {
            get
            {
                return this.KargoFirmaIDField;
            }
            set
            {
                this.KargoFirmaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoPaketID
        {
            get
            {
                return this.KargoPaketIDField;
            }
            set
            {
                this.KargoPaketIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNumarasi
        {
            get
            {
                return this.KargoTakipNumarasiField;
            }
            set
            {
                this.KargoTakipNumarasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoTipi
        {
            get
            {
                return this.KargoTipiField;
            }
            set
            {
                this.KargoTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoTutari
        {
            get
            {
                return this.KargoTutariField;
            }
            set
            {
                this.KargoTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KdvOrani
        {
            get
            {
                return this.KdvOraniField;
            }
            set
            {
                this.KdvOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KdvTutari
        {
            get
            {
                return this.KdvTutariField;
            }
            set
            {
                this.KdvTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime MagazaAtamaTarihi
        {
            get
            {
                return this.MagazaAtamaTarihiField;
            }
            set
            {
                this.MagazaAtamaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaDurum
        {
            get
            {
                return this.MagazaDurumField;
            }
            set
            {
                this.MagazaDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime MagazaGonderimTarihi
        {
            get
            {
                return this.MagazaGonderimTarihiField;
            }
            set
            {
                this.MagazaGonderimTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaID
        {
            get
            {
                return this.MagazaIDField;
            }
            set
            {
                this.MagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaKodu
        {
            get
            {
                return this.MagazaKoduField;
            }
            set
            {
                this.MagazaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Maliyet
        {
            get
            {
                return this.MaliyetField;
            }
            set
            {
                this.MaliyetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceParams
        {
            get
            {
                return this.MarketplaceParamsField;
            }
            set
            {
                this.MarketplaceParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSepetOzellestirme[] Ozellestirme
        {
            get
            {
                return this.OzellestirmeField;
            }
            set
            {
                this.OzellestirmeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimYolu
        {
            get
            {
                return this.ResimYoluField;
            }
            set
            {
                this.ResimYoluField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SatisAniIndirimliFiyat
        {
            get
            {
                return this.SatisAniIndirimliFiyatField;
            }
            set
            {
                this.SatisAniIndirimliFiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SatisAniIndirimliFiyatKdv
        {
            get
            {
                return this.SatisAniIndirimliFiyatKdvField;
            }
            set
            {
                this.SatisAniIndirimliFiyatKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SatisAniSatisFiyat
        {
            get
            {
                return this.SatisAniSatisFiyatField;
            }
            set
            {
                this.SatisAniSatisFiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SatisAniSatisFiyatKdv
        {
            get
            {
                return this.SatisAniSatisFiyatKdvField;
            }
            set
            {
                this.SatisAniSatisFiyatKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SatisBirimi
        {
            get
            {
                return this.SatisBirimiField;
            }
            set
            {
                this.SatisBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TedarikciID
        {
            get
            {
                return this.TedarikciIDField;
            }
            set
            {
                this.TedarikciIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TedarikciKodu
        {
            get
            {
                return this.TedarikciKoduField;
            }
            set
            {
                this.TedarikciKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TedarikciKodu2
        {
            get
            {
                return this.TedarikciKodu2Field;
            }
            set
            {
                this.TedarikciKodu2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamIndirim
        {
            get
            {
                return this.ToplamIndirimField;
            }
            set
            {
                this.ToplamIndirimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunAgirligi
        {
            get
            {
                return this.UrunAgirligiField;
            }
            set
            {
                this.UrunAgirligiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunOlusturmaGrupAdi
        {
            get
            {
                return this.UrunOlusturmaGrupAdiField;
            }
            set
            {
                this.UrunOlusturmaGrupAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunOlusturmaGrupId
        {
            get
            {
                return this.UrunOlusturmaGrupIdField;
            }
            set
            {
                this.UrunOlusturmaGrupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunOlusturmaGuid
        {
            get
            {
                return this.UrunOlusturmaGuidField;
            }
            set
            {
                this.UrunOlusturmaGuidField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunOlusturmaKartId
        {
            get
            {
                return this.UrunOlusturmaKartIdField;
            }
            set
            {
                this.UrunOlusturmaKartIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunOlusturmaUrunAdi
        {
            get
            {
                return this.UrunOlusturmaUrunAdiField;
            }
            set
            {
                this.UrunOlusturmaUrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebUrunTipi UrunTipi
        {
            get
            {
                return this.UrunTipiField;
            }
            set
            {
                this.UrunTipiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebUlke", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebUlke : object
    {
        
        private string Alpha2CodeField;
        
        private string Alpha3CodeField;
        
        private int IDField;
        
        private string NumericCodeField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Alpha2Code
        {
            get
            {
                return this.Alpha2CodeField;
            }
            set
            {
                this.Alpha2CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Alpha3Code
        {
            get
            {
                return this.Alpha3CodeField;
            }
            set
            {
                this.Alpha3CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NumericCode
        {
            get
            {
                return this.NumericCodeField;
            }
            set
            {
                this.NumericCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeEkBilgi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class OdemeEkBilgi : object
    {
        
        private string AuthCodeField;
        
        private string HostRefNumField;
        
        private string IssuerField;
        
        private PushDashboard.SiparisServis.OdemeKampanyasi OdemeKampanyasiField;
        
        private string PaymentTypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AuthCode
        {
            get
            {
                return this.AuthCodeField;
            }
            set
            {
                this.AuthCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HostRefNum
        {
            get
            {
                return this.HostRefNumField;
            }
            set
            {
                this.HostRefNumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Issuer
        {
            get
            {
                return this.IssuerField;
            }
            set
            {
                this.IssuerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.OdemeKampanyasi OdemeKampanyasi
        {
            get
            {
                return this.OdemeKampanyasiField;
            }
            set
            {
                this.OdemeKampanyasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PaymentType
        {
            get
            {
                return this.PaymentTypeField;
            }
            set
            {
                this.PaymentTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeKampanyasi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class OdemeKampanyasi : object
    {
        
        private double ToplamKargoIndirimiField;
        
        private double ToplamOdemeIndirimiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamKargoIndirimi
        {
            get
            {
                return this.ToplamKargoIndirimiField;
            }
            set
            {
                this.ToplamKargoIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamOdemeIndirimi
        {
            get
            {
                return this.ToplamOdemeIndirimiField;
            }
            set
            {
                this.ToplamOdemeIndirimiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisUrunEkSecenekOzellik", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisUrunEkSecenekOzellik : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        private int TipIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TipID
        {
            get
            {
                return this.TipIDField;
            }
            set
            {
                this.TipIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSepetOzellestirme", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSepetOzellestirme : object
    {
        
        private PushDashboard.SiparisServis.FormDesignService FormDesignField;
        
        private int FormIdField;
        
        private PushDashboard.SiparisServis.WebOzellestirmeItem[] OzellestirmeItemField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.FormDesignService FormDesign
        {
            get
            {
                return this.FormDesignField;
            }
            set
            {
                this.FormDesignField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FormId
        {
            get
            {
                return this.FormIdField;
            }
            set
            {
                this.FormIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebOzellestirmeItem[] OzellestirmeItem
        {
            get
            {
                return this.OzellestirmeItemField;
            }
            set
            {
                this.OzellestirmeItemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebUrunTipi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebUrunTipi : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Urun = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        HediyeKarti = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Poset = 2,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FormDesignService", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class FormDesignService : object
    {
        
        private double FormDesignTotalPriceField;
        
        private double FormDesignUnitPriceField;
        
        private string NameToBePrintedField;
        
        private double NameToBePrintedTotalPriceField;
        
        private double NameToBePrintedUnitPriceField;
        
        private string NumberToBePrintedField;
        
        private double NumberToBePrintedTotalPriceField;
        
        private double NumberToBePrintedUnitPriceField;
        
        private PushDashboard.SiparisServis.SelectedPlayerService SelectedPlayerField;
        
        private PushDashboard.SiparisServis.SelectedSignature[] SelectedSignaturesField;
        
        private double SignatureTotalPriceField;
        
        private double SignatureUnitPriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double FormDesignTotalPrice
        {
            get
            {
                return this.FormDesignTotalPriceField;
            }
            set
            {
                this.FormDesignTotalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double FormDesignUnitPrice
        {
            get
            {
                return this.FormDesignUnitPriceField;
            }
            set
            {
                this.FormDesignUnitPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NameToBePrinted
        {
            get
            {
                return this.NameToBePrintedField;
            }
            set
            {
                this.NameToBePrintedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double NameToBePrintedTotalPrice
        {
            get
            {
                return this.NameToBePrintedTotalPriceField;
            }
            set
            {
                this.NameToBePrintedTotalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double NameToBePrintedUnitPrice
        {
            get
            {
                return this.NameToBePrintedUnitPriceField;
            }
            set
            {
                this.NameToBePrintedUnitPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NumberToBePrinted
        {
            get
            {
                return this.NumberToBePrintedField;
            }
            set
            {
                this.NumberToBePrintedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double NumberToBePrintedTotalPrice
        {
            get
            {
                return this.NumberToBePrintedTotalPriceField;
            }
            set
            {
                this.NumberToBePrintedTotalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double NumberToBePrintedUnitPrice
        {
            get
            {
                return this.NumberToBePrintedUnitPriceField;
            }
            set
            {
                this.NumberToBePrintedUnitPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.SelectedPlayerService SelectedPlayer
        {
            get
            {
                return this.SelectedPlayerField;
            }
            set
            {
                this.SelectedPlayerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.SelectedSignature[] SelectedSignatures
        {
            get
            {
                return this.SelectedSignaturesField;
            }
            set
            {
                this.SelectedSignaturesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SignatureTotalPrice
        {
            get
            {
                return this.SignatureTotalPriceField;
            }
            set
            {
                this.SignatureTotalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SignatureUnitPrice
        {
            get
            {
                return this.SignatureUnitPriceField;
            }
            set
            {
                this.SignatureUnitPriceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebOzellestirmeItem", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebOzellestirmeItem : object
    {
        
        private string CevapField;
        
        private string SoruField;
        
        private string TipField;
        
        private double UcretField;
        
        private string UcretStrField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevap
        {
            get
            {
                return this.CevapField;
            }
            set
            {
                this.CevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Soru
        {
            get
            {
                return this.SoruField;
            }
            set
            {
                this.SoruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Ucret
        {
            get
            {
                return this.UcretField;
            }
            set
            {
                this.UcretField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UcretStr
        {
            get
            {
                return this.UcretStrField;
            }
            set
            {
                this.UcretStrField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectedPlayerService", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectedPlayerService : object
    {
        
        private string FormNameField;
        
        private int IdField;
        
        private string NameField;
        
        private int NumberField;
        
        private double TotalPriceField;
        
        private double UnitPriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FormName
        {
            get
            {
                return this.FormNameField;
            }
            set
            {
                this.FormNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Number
        {
            get
            {
                return this.NumberField;
            }
            set
            {
                this.NumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double TotalPrice
        {
            get
            {
                return this.TotalPriceField;
            }
            set
            {
                this.TotalPriceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UnitPrice
        {
            get
            {
                return this.UnitPriceField;
            }
            set
            {
                this.UnitPriceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectedSignature", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public partial class SelectedSignature : object
    {
        
        private string FormNameField;
        
        private int IdField;
        
        private string NameField;
        
        private int NumberField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FormName
        {
            get
            {
                return this.FormNameField;
            }
            set
            {
                this.FormNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Number
        {
            get
            {
                return this.NumberField;
            }
            set
            {
                this.NumberField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisOdemeFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisOdemeFiltre : object
    {
        
        private System.Nullable<bool> IsAktarildiField;
        
        private int OdemeDurumuField;
        
        private int OdemeIDField;
        
        private System.Nullable<System.DateTime> OdemeTarihiBasField;
        
        private System.Nullable<System.DateTime> OdemeTarihiSonField;
        
        private int OdemeTipiField;
        
        private int SiparisIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IsAktarildi
        {
            get
            {
                return this.IsAktarildiField;
            }
            set
            {
                this.IsAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeDurumu
        {
            get
            {
                return this.OdemeDurumuField;
            }
            set
            {
                this.OdemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeID
        {
            get
            {
                return this.OdemeIDField;
            }
            set
            {
                this.OdemeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeTarihiBas
        {
            get
            {
                return this.OdemeTarihiBasField;
            }
            set
            {
                this.OdemeTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeTarihiSon
        {
            get
            {
                return this.OdemeTarihiSonField;
            }
            set
            {
                this.OdemeTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisSaveRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisSaveRequest : object
    {
        
        private string BNPLNoField;
        
        private PushDashboard.SiparisServis.WebFaturaAdres FaturaAdresField;
        
        private int FaturaAdresIdField;
        
        private string HediyeCekiField;
        
        private double HediyePaketiTutariField;
        
        private System.Nullable<bool> HediyePaketiVarField;
        
        private double IndirimTutariField;
        
        private bool IsMarketplaceField;
        
        private int KargoAdresIdField;
        
        private int KargoFirmaIdField;
        
        private PushDashboard.SiparisServis.WebSiparisKargoGondericiBilgi KargoGondericiBilgiField;
        
        private double KargoKatkiPayiField;
        
        private double KargoTutariField;
        
        private System.Nullable<System.DateTime> KargoyaSonVerilmeTarihiField;
        
        private System.Nullable<bool> KdvOraniniSiparisUrundenAlField;
        
        private bool MailGonderField;
        
        private System.Nullable<bool> MaliyetiSiparisUrundenAlField;
        
        private bool MarketPlaceOdemeAlindiField;
        
        private string MarketplaceKampanyaKoduField;
        
        private string MarketplaceParamsField;
        
        private System.Nullable<System.DateTime> MusteriKargoTeslimTarihiField;
        
        private PushDashboard.SiparisServis.WebSiparisSaveOdeme OdemeField;
        
        private System.Nullable<System.DateTime> OdemeVadeTarihiField;
        
        private PushDashboard.SiparisServis.WebSiparisSaveOdeme[] OdemelerField;
        
        private string OzelAlan1Field;
        
        private string OzelAlan2Field;
        
        private string OzelAlan3Field;
        
        private string ParaBirimiField;
        
        private int PazaryeriButikIdField;
        
        private int PazaryeriIhracatField;
        
        private string ReferansNoField;
        
        private string SiparisKaynagiField;
        
        private string SiparisNoField;
        
        private string SiparisNotuField;
        
        private bool SmsGonderField;
        
        private PushDashboard.SiparisServis.WebTeslimatAdres TeslimatAdresField;
        
        private string TeslimatSaatiField;
        
        private System.DateTime TeslimatTarihiField;
        
        private double UrunTutariField;
        
        private double UrunTutariKdvField;
        
        private PushDashboard.SiparisServis.WebSiparisSaveUrun[] UrunlerField;
        
        private string UyeAdiField;
        
        private string UyeCepField;
        
        private int UyeIdField;
        
        private bool UyeKazanimAktifField;
        
        private string UyeMailField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BNPLNo
        {
            get
            {
                return this.BNPLNoField;
            }
            set
            {
                this.BNPLNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebFaturaAdres FaturaAdres
        {
            get
            {
                return this.FaturaAdresField;
            }
            set
            {
                this.FaturaAdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FaturaAdresId
        {
            get
            {
                return this.FaturaAdresIdField;
            }
            set
            {
                this.FaturaAdresIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCeki
        {
            get
            {
                return this.HediyeCekiField;
            }
            set
            {
                this.HediyeCekiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyePaketiTutari
        {
            get
            {
                return this.HediyePaketiTutariField;
            }
            set
            {
                this.HediyePaketiTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> HediyePaketiVar
        {
            get
            {
                return this.HediyePaketiVarField;
            }
            set
            {
                this.HediyePaketiVarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimTutari
        {
            get
            {
                return this.IndirimTutariField;
            }
            set
            {
                this.IndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsMarketplace
        {
            get
            {
                return this.IsMarketplaceField;
            }
            set
            {
                this.IsMarketplaceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoAdresId
        {
            get
            {
                return this.KargoAdresIdField;
            }
            set
            {
                this.KargoAdresIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoFirmaId
        {
            get
            {
                return this.KargoFirmaIdField;
            }
            set
            {
                this.KargoFirmaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisKargoGondericiBilgi KargoGondericiBilgi
        {
            get
            {
                return this.KargoGondericiBilgiField;
            }
            set
            {
                this.KargoGondericiBilgiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoKatkiPayi
        {
            get
            {
                return this.KargoKatkiPayiField;
            }
            set
            {
                this.KargoKatkiPayiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoTutari
        {
            get
            {
                return this.KargoTutariField;
            }
            set
            {
                this.KargoTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> KargoyaSonVerilmeTarihi
        {
            get
            {
                return this.KargoyaSonVerilmeTarihiField;
            }
            set
            {
                this.KargoyaSonVerilmeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KdvOraniniSiparisUrundenAl
        {
            get
            {
                return this.KdvOraniniSiparisUrundenAlField;
            }
            set
            {
                this.KdvOraniniSiparisUrundenAlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGonder
        {
            get
            {
                return this.MailGonderField;
            }
            set
            {
                this.MailGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> MaliyetiSiparisUrundenAl
        {
            get
            {
                return this.MaliyetiSiparisUrundenAlField;
            }
            set
            {
                this.MaliyetiSiparisUrundenAlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MarketPlaceOdemeAlindi
        {
            get
            {
                return this.MarketPlaceOdemeAlindiField;
            }
            set
            {
                this.MarketPlaceOdemeAlindiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceKampanyaKodu
        {
            get
            {
                return this.MarketplaceKampanyaKoduField;
            }
            set
            {
                this.MarketplaceKampanyaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceParams
        {
            get
            {
                return this.MarketplaceParamsField;
            }
            set
            {
                this.MarketplaceParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> MusteriKargoTeslimTarihi
        {
            get
            {
                return this.MusteriKargoTeslimTarihiField;
            }
            set
            {
                this.MusteriKargoTeslimTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisSaveOdeme Odeme
        {
            get
            {
                return this.OdemeField;
            }
            set
            {
                this.OdemeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeVadeTarihi
        {
            get
            {
                return this.OdemeVadeTarihiField;
            }
            set
            {
                this.OdemeVadeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisSaveOdeme[] Odemeler
        {
            get
            {
                return this.OdemelerField;
            }
            set
            {
                this.OdemelerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan1
        {
            get
            {
                return this.OzelAlan1Field;
            }
            set
            {
                this.OzelAlan1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan2
        {
            get
            {
                return this.OzelAlan2Field;
            }
            set
            {
                this.OzelAlan2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan3
        {
            get
            {
                return this.OzelAlan3Field;
            }
            set
            {
                this.OzelAlan3Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriButikId
        {
            get
            {
                return this.PazaryeriButikIdField;
            }
            set
            {
                this.PazaryeriButikIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriIhracat
        {
            get
            {
                return this.PazaryeriIhracatField;
            }
            set
            {
                this.PazaryeriIhracatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ReferansNo
        {
            get
            {
                return this.ReferansNoField;
            }
            set
            {
                this.ReferansNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNotu
        {
            get
            {
                return this.SiparisNotuField;
            }
            set
            {
                this.SiparisNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsGonder
        {
            get
            {
                return this.SmsGonderField;
            }
            set
            {
                this.SmsGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebTeslimatAdres TeslimatAdres
        {
            get
            {
                return this.TeslimatAdresField;
            }
            set
            {
                this.TeslimatAdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TeslimatSaati
        {
            get
            {
                return this.TeslimatSaatiField;
            }
            set
            {
                this.TeslimatSaatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime TeslimatTarihi
        {
            get
            {
                return this.TeslimatTarihiField;
            }
            set
            {
                this.TeslimatTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunTutari
        {
            get
            {
                return this.UrunTutariField;
            }
            set
            {
                this.UrunTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunTutariKdv
        {
            get
            {
                return this.UrunTutariKdvField;
            }
            set
            {
                this.UrunTutariKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisSaveUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeCep
        {
            get
            {
                return this.UyeCepField;
            }
            set
            {
                this.UyeCepField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyeKazanimAktif
        {
            get
            {
                return this.UyeKazanimAktifField;
            }
            set
            {
                this.UyeKazanimAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeMail
        {
            get
            {
                return this.UyeMailField;
            }
            set
            {
                this.UyeMailField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebFaturaAdres", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebFaturaAdres : object
    {
        
        private string AdresField;
        
        private string AdresTarifiField;
        
        private string AliciTelefonField;
        
        private string EntegrasyonIdField;
        
        private string FirmaAdiField;
        
        private string IlField;
        
        private string IlceField;
        
        private string MahalleField;
        
        private string PostaKoduField;
        
        private string SemtField;
        
        private string UlkeField;
        
        private string VergiDairesiField;
        
        private string VergiNoField;
        
        private bool isKurumsalField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdresTarifi
        {
            get
            {
                return this.AdresTarifiField;
            }
            set
            {
                this.AdresTarifiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciTelefon
        {
            get
            {
                return this.AliciTelefonField;
            }
            set
            {
                this.AliciTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EntegrasyonId
        {
            get
            {
                return this.EntegrasyonIdField;
            }
            set
            {
                this.EntegrasyonIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FirmaAdi
        {
            get
            {
                return this.FirmaAdiField;
            }
            set
            {
                this.FirmaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mahalle
        {
            get
            {
                return this.MahalleField;
            }
            set
            {
                this.MahalleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PostaKodu
        {
            get
            {
                return this.PostaKoduField;
            }
            set
            {
                this.PostaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Semt
        {
            get
            {
                return this.SemtField;
            }
            set
            {
                this.SemtField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiDairesi
        {
            get
            {
                return this.VergiDairesiField;
            }
            set
            {
                this.VergiDairesiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiNo
        {
            get
            {
                return this.VergiNoField;
            }
            set
            {
                this.VergiNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool isKurumsal
        {
            get
            {
                return this.isKurumsalField;
            }
            set
            {
                this.isKurumsalField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisSaveOdeme", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisSaveOdeme : object
    {
        
        private double BankaKomisyonuField;
        
        private int HavaleHesapIDField;
        
        private double KapidaOdemeTutariField;
        
        private int OdemeDurumuField;
        
        private double OdemeIndirimiField;
        
        private string OdemeNotuField;
        
        private int OdemeSecenekIDField;
        
        private int OdemeTipiField;
        
        private int TaksitSayisiField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double BankaKomisyonu
        {
            get
            {
                return this.BankaKomisyonuField;
            }
            set
            {
                this.BankaKomisyonuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HavaleHesapID
        {
            get
            {
                return this.HavaleHesapIDField;
            }
            set
            {
                this.HavaleHesapIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KapidaOdemeTutari
        {
            get
            {
                return this.KapidaOdemeTutariField;
            }
            set
            {
                this.KapidaOdemeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeDurumu
        {
            get
            {
                return this.OdemeDurumuField;
            }
            set
            {
                this.OdemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OdemeIndirimi
        {
            get
            {
                return this.OdemeIndirimiField;
            }
            set
            {
                this.OdemeIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeNotu
        {
            get
            {
                return this.OdemeNotuField;
            }
            set
            {
                this.OdemeNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeSecenekID
        {
            get
            {
                return this.OdemeSecenekIDField;
            }
            set
            {
                this.OdemeSecenekIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaksitSayisi
        {
            get
            {
                return this.TaksitSayisiField;
            }
            set
            {
                this.TaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTeslimatAdres", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTeslimatAdres : object
    {
        
        private string AdresField;
        
        private string AdresTarifiField;
        
        private string AliciAdiField;
        
        private string AliciTelefonField;
        
        private string IlField;
        
        private string IlceField;
        
        private string MahalleField;
        
        private string PostaKoduField;
        
        private string SemtField;
        
        private string UlkeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdresTarifi
        {
            get
            {
                return this.AdresTarifiField;
            }
            set
            {
                this.AdresTarifiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciAdi
        {
            get
            {
                return this.AliciAdiField;
            }
            set
            {
                this.AliciAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciTelefon
        {
            get
            {
                return this.AliciTelefonField;
            }
            set
            {
                this.AliciTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mahalle
        {
            get
            {
                return this.MahalleField;
            }
            set
            {
                this.MahalleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PostaKodu
        {
            get
            {
                return this.PostaKoduField;
            }
            set
            {
                this.PostaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Semt
        {
            get
            {
                return this.SemtField;
            }
            set
            {
                this.SemtField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisSaveUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisSaveUrun : object
    {
        
        private double AdetField;
        
        private double KdvOraniField;
        
        private double KdvTutariField;
        
        private int MagazaIdField;
        
        private bool MagazaStokKontrolEtField;
        
        private double MaliyetField;
        
        private string MarketplaceParamsField;
        
        private double TutarField;
        
        private int UrunIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KdvOrani
        {
            get
            {
                return this.KdvOraniField;
            }
            set
            {
                this.KdvOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KdvTutari
        {
            get
            {
                return this.KdvTutariField;
            }
            set
            {
                this.KdvTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaId
        {
            get
            {
                return this.MagazaIdField;
            }
            set
            {
                this.MagazaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MagazaStokKontrolEt
        {
            get
            {
                return this.MagazaStokKontrolEtField;
            }
            set
            {
                this.MagazaStokKontrolEtField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Maliyet
        {
            get
            {
                return this.MaliyetField;
            }
            set
            {
                this.MaliyetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceParams
        {
            get
            {
                return this.MarketplaceParamsField;
            }
            set
            {
                this.MarketplaceParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebServisResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisOzelAlanlarResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebHediyeCekiUygulaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebHediyeCekiOlusturResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetHediyeCekiKullanildiResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebHediyeCekiResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisUrunDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SiparisKargoTakipNoKontrolResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.UrunIdaneNedenleriResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SaveSiparisKargoPaketKargoTakipNoResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebUpdateSepetResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSepetKampanyaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebSaveMagazaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisOdemeDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectEFaturaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SaveEFaturaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.EFaturaIptalResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectIadeOdemeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SaveBekleyenAramaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.UpdateTelefonlaSiparisDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectSiparisDurumlariResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectOdemeTipleriResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetReferansNoResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisDurumListeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisDurumListError))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectSiparisDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SelectSiparisDurumLogListResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.UpdateEfaturaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.IadeOdemeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SiparisAnonimlestirResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.UpdateMarketPlaceAreasResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SetSiparisFaturaUrlResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SiparisUrunIptalIadeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.OdemeOnProvizyonKapatResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.SaveSiparisOdemeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.WebSiparisSaveResponse))]
    public partial class WebServisResponse : object
    {
        
        private int ErrorCodeField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisOzelAlanlarResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisOzelAlanlarResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebHediyeCekiUygulaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebHediyeCekiUygulaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebHediyeCekiOlusturResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebHediyeCekiOlusturResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private int HediyeCekiIdField;
        
        private string HediyeCekiKoduField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HediyeCekiId
        {
            get
            {
                return this.HediyeCekiIdField;
            }
            set
            {
                this.HediyeCekiIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCekiKodu
        {
            get
            {
                return this.HediyeCekiKoduField;
            }
            set
            {
                this.HediyeCekiKoduField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetHediyeCekiKullanildiResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetHediyeCekiKullanildiResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebHediyeCekiResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebHediyeCekiResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private string CodeField;
        
        private System.DateTime CreatedDateField;
        
        private string CreatedUserField;
        
        private string DiscountValueField;
        
        private System.DateTime EndDateField;
        
        private string GrupAdiField;
        
        private int IdField;
        
        private bool IsAndroidActiveField;
        
        private bool IsIOSActiveField;
        
        private bool IsMobileActiveField;
        
        private bool IsWebActiveField;
        
        private string MinProductAmountField;
        
        private int QuantityField;
        
        private int RemainingUseField;
        
        private System.DateTime StartDateField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Code
        {
            get
            {
                return this.CodeField;
            }
            set
            {
                this.CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreatedDate
        {
            get
            {
                return this.CreatedDateField;
            }
            set
            {
                this.CreatedDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedUser
        {
            get
            {
                return this.CreatedUserField;
            }
            set
            {
                this.CreatedUserField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DiscountValue
        {
            get
            {
                return this.DiscountValueField;
            }
            set
            {
                this.DiscountValueField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EndDate
        {
            get
            {
                return this.EndDateField;
            }
            set
            {
                this.EndDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GrupAdi
        {
            get
            {
                return this.GrupAdiField;
            }
            set
            {
                this.GrupAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsAndroidActive
        {
            get
            {
                return this.IsAndroidActiveField;
            }
            set
            {
                this.IsAndroidActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsIOSActive
        {
            get
            {
                return this.IsIOSActiveField;
            }
            set
            {
                this.IsIOSActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsMobileActive
        {
            get
            {
                return this.IsMobileActiveField;
            }
            set
            {
                this.IsMobileActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsWebActive
        {
            get
            {
                return this.IsWebActiveField;
            }
            set
            {
                this.IsWebActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MinProductAmount
        {
            get
            {
                return this.MinProductAmountField;
            }
            set
            {
                this.MinProductAmountField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Quantity
        {
            get
            {
                return this.QuantityField;
            }
            set
            {
                this.QuantityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RemainingUse
        {
            get
            {
                return this.RemainingUseField;
            }
            set
            {
                this.RemainingUseField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime StartDate
        {
            get
            {
                return this.StartDateField;
            }
            set
            {
                this.StartDateField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisDurumResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisUrunDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisUrunDurumResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.BLEnumsOdemeDurumlari OdemeDurumField;
        
        private string OdemeHataMesajField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.BLEnumsOdemeDurumlari OdemeDurum
        {
            get
            {
                return this.OdemeDurumField;
            }
            set
            {
                this.OdemeDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeHataMesaj
        {
            get
            {
                return this.OdemeHataMesajField;
            }
            set
            {
                this.OdemeHataMesajField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisKargoTakipNoKontrolResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisKargoTakipNoKontrolResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private string KargoTakipLinkField;
        
        private string KargoTakipNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNo
        {
            get
            {
                return this.KargoTakipNoField;
            }
            set
            {
                this.KargoTakipNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UrunIdaneNedenleriResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UrunIdaneNedenleriResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.UrunIadeNeden[] UrunIadeNedenListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.UrunIadeNeden[] UrunIadeNedenList
        {
            get
            {
                return this.UrunIadeNedenListField;
            }
            set
            {
                this.UrunIadeNedenListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveSiparisKargoPaketKargoTakipNoResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveSiparisKargoPaketKargoTakipNoResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebUpdateSepetResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebUpdateSepetResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSepetKampanyaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSepetKampanyaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSaveMagazaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSaveMagazaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.WebMagaza[] magazalarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebMagaza[] magazalar
        {
            get
            {
                return this.magazalarField;
            }
            set
            {
                this.magazalarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisOdemeDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisOdemeDurumResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectEFaturaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectEFaturaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.EFatura[] EFaturaListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.EFatura[] EFaturaList
        {
            get
            {
                return this.EFaturaListField;
            }
            set
            {
                this.EFaturaListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveEFaturaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveEFaturaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EFaturaIptalResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class EFaturaIptalResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIadeOdemeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIadeOdemeResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.IadeOdeme[] IadeOdemeListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.IadeOdeme[] IadeOdemeList
        {
            get
            {
                return this.IadeOdemeListField;
            }
            set
            {
                this.IadeOdemeListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveBekleyenAramaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveBekleyenAramaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private int IdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateTelefonlaSiparisDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateTelefonlaSiparisDurumResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSiparisDurumlariResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSiparisDurumlariResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.EnumKeyValue[] SiparisDurumlariField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.EnumKeyValue[] SiparisDurumlari
        {
            get
            {
                return this.SiparisDurumlariField;
            }
            set
            {
                this.SiparisDurumlariField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectOdemeTipleriResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectOdemeTipleriResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.EnumKeyValue[] OdemeTipleriField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.EnumKeyValue[] OdemeTipleri
        {
            get
            {
                return this.OdemeTipleriField;
            }
            set
            {
                this.OdemeTipleriField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetReferansNoResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetReferansNoResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisDurumListeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisDurumListeResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private int BasariliField;
        
        private int HataliField;
        
        private PushDashboard.SiparisServis.SetSiparisDurumListError[] HataliListeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Basarili
        {
            get
            {
                return this.BasariliField;
            }
            set
            {
                this.BasariliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Hatali
        {
            get
            {
                return this.HataliField;
            }
            set
            {
                this.HataliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.SetSiparisDurumListError[] HataliListe
        {
            get
            {
                return this.HataliListeField;
            }
            set
            {
                this.HataliListeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisDurumListError", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisDurumListError : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private int SiparisIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSiparisDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSiparisDurumResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.WebSiparisDurumlari DurumField;
        
        private string KargoTakipLinkField;
        
        private string KargoTakipNoField;
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisDurumlari Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNo
        {
            get
            {
                return this.KargoTakipNoField;
            }
            set
            {
                this.KargoTakipNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSiparisDurumLogListResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSiparisDurumLogListResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.WebSiparisDurumLog[] SiparisDurumLogListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisDurumLog[] SiparisDurumLogList
        {
            get
            {
                return this.SiparisDurumLogListField;
            }
            set
            {
                this.SiparisDurumLogListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateEfaturaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateEfaturaResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeOdemeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeOdemeResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisAnonimlestirResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisAnonimlestirResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateMarketPlaceAreasResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateMarketPlaceAreasResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisFaturaUrlResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisFaturaUrlResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisUrunIptalIadeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisUrunIptalIadeResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.BLEnumsOdemeDurumlari[] OdemeDurumListField;
        
        private string[] OdemeHataMesajListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.BLEnumsOdemeDurumlari[] OdemeDurumList
        {
            get
            {
                return this.OdemeDurumListField;
            }
            set
            {
                this.OdemeDurumListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] OdemeHataMesajList
        {
            get
            {
                return this.OdemeHataMesajListField;
            }
            set
            {
                this.OdemeHataMesajListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeOnProvizyonKapatResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class OdemeOnProvizyonKapatResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveSiparisOdemeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveSiparisOdemeResponse : PushDashboard.SiparisServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisSaveResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisSaveResponse : PushDashboard.SiparisServis.WebServisResponse
    {
        
        private PushDashboard.SiparisServis.WebServisResponse[] MessagesField;
        
        private PushDashboard.SiparisServis.WebSiparis SiparisDetayiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebServisResponse[] Messages
        {
            get
            {
                return this.MessagesField;
            }
            set
            {
                this.MessagesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparis SiparisDetayi
        {
            get
            {
                return this.SiparisDetayiField;
            }
            set
            {
                this.SiparisDetayiField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLEnums.OdemeDurumlari", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public enum BLEnumsOdemeDurumlari : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnayBekliyor = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Onaylandi = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hatali = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeEdilmis = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalEdilmis = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeBekliyor = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeTalepEdildi = 6,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UrunIadeNeden", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UrunIadeNeden : object
    {
        
        private bool AktifField;
        
        private int IDField;
        
        private int IslemField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMagaza", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebMagaza : object
    {
        
        private string AciklamaField;
        
        private string AdresField;
        
        private bool AktifField;
        
        private string ApiSifreField;
        
        private string FaksField;
        
        private string GsmField;
        
        private int IDField;
        
        private string IlField;
        
        private int IlIDField;
        
        private string IlceField;
        
        private int IlceIDField;
        
        private bool IletisimdeGosterField;
        
        private int KargoGonderimLimitiField;
        
        private string LatitudeField;
        
        private string LongitudeField;
        
        private string MagazaKoduField;
        
        private string MagazaResimField;
        
        private string MailField;
        
        private bool SiparisAdimindaGosterField;
        
        private int SiraField;
        
        private string TanimField;
        
        private string TelefonField;
        
        private bool TeslimatSaatiAktifField;
        
        private string UlkeField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Aciklama
        {
            get
            {
                return this.AciklamaField;
            }
            set
            {
                this.AciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ApiSifre
        {
            get
            {
                return this.ApiSifreField;
            }
            set
            {
                this.ApiSifreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Faks
        {
            get
            {
                return this.FaksField;
            }
            set
            {
                this.FaksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Gsm
        {
            get
            {
                return this.GsmField;
            }
            set
            {
                this.GsmField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceID
        {
            get
            {
                return this.IlceIDField;
            }
            set
            {
                this.IlceIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IletisimdeGoster
        {
            get
            {
                return this.IletisimdeGosterField;
            }
            set
            {
                this.IletisimdeGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoGonderimLimiti
        {
            get
            {
                return this.KargoGonderimLimitiField;
            }
            set
            {
                this.KargoGonderimLimitiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Latitude
        {
            get
            {
                return this.LatitudeField;
            }
            set
            {
                this.LatitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Longitude
        {
            get
            {
                return this.LongitudeField;
            }
            set
            {
                this.LongitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaKodu
        {
            get
            {
                return this.MagazaKoduField;
            }
            set
            {
                this.MagazaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaResim
        {
            get
            {
                return this.MagazaResimField;
            }
            set
            {
                this.MagazaResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisAdimindaGoster
        {
            get
            {
                return this.SiparisAdimindaGosterField;
            }
            set
            {
                this.SiparisAdimindaGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Sira
        {
            get
            {
                return this.SiraField;
            }
            set
            {
                this.SiraField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TeslimatSaatiAktif
        {
            get
            {
                return this.TeslimatSaatiAktifField;
            }
            set
            {
                this.TeslimatSaatiAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EFatura", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class EFatura : PushDashboard.SiparisServis.SaveEFatura
    {
        
        private string EFaturaUygulamaYanitField;
        
        private int EntegratorField;
        
        private string HataKoduField;
        
        private bool IptalEdildiField;
        
        private System.DateTime IptalTarihiField;
        
        private string SonucField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EFaturaUygulamaYanit
        {
            get
            {
                return this.EFaturaUygulamaYanitField;
            }
            set
            {
                this.EFaturaUygulamaYanitField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Entegrator
        {
            get
            {
                return this.EntegratorField;
            }
            set
            {
                this.EntegratorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HataKodu
        {
            get
            {
                return this.HataKoduField;
            }
            set
            {
                this.HataKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IptalEdildi
        {
            get
            {
                return this.IptalEdildiField;
            }
            set
            {
                this.IptalEdildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime IptalTarihi
        {
            get
            {
                return this.IptalTarihiField;
            }
            set
            {
                this.IptalTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sonuc
        {
            get
            {
                return this.SonucField;
            }
            set
            {
                this.SonucField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveEFatura", Namespace="http://schemas.datacontract.org/2004/07/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.SiparisServis.EFatura))]
    public partial class SaveEFatura : object
    {
        
        private string AciklamaField;
        
        private string CustomerInVIDField;
        
        private string DetayField;
        
        private string DosyaAdiField;
        
        private string DosyaUrlField;
        
        private PushDashboard.SiparisServis.EFaturaTuru EFaturaTuruField;
        
        private string EnVuuIdField;
        
        private string FaturaNumarasiField;
        
        private int FaturaTipiField;
        
        private int IdField;
        
        private double KdvHaricToplamTutarField;
        
        private System.Nullable<System.DateTime> OlusturmaTarihiField;
        
        private string Sha256HashField;
        
        private int SiparisIdField;
        
        private string SiparisNoField;
        
        private string UUIDField;
        
        private string VKNField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Aciklama
        {
            get
            {
                return this.AciklamaField;
            }
            set
            {
                this.AciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CustomerInVID
        {
            get
            {
                return this.CustomerInVIDField;
            }
            set
            {
                this.CustomerInVIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Detay
        {
            get
            {
                return this.DetayField;
            }
            set
            {
                this.DetayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DosyaAdi
        {
            get
            {
                return this.DosyaAdiField;
            }
            set
            {
                this.DosyaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DosyaUrl
        {
            get
            {
                return this.DosyaUrlField;
            }
            set
            {
                this.DosyaUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.EFaturaTuru EFaturaTuru
        {
            get
            {
                return this.EFaturaTuruField;
            }
            set
            {
                this.EFaturaTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EnVuuId
        {
            get
            {
                return this.EnVuuIdField;
            }
            set
            {
                this.EnVuuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaNumarasi
        {
            get
            {
                return this.FaturaNumarasiField;
            }
            set
            {
                this.FaturaNumarasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FaturaTipi
        {
            get
            {
                return this.FaturaTipiField;
            }
            set
            {
                this.FaturaTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KdvHaricToplamTutar
        {
            get
            {
                return this.KdvHaricToplamTutarField;
            }
            set
            {
                this.KdvHaricToplamTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OlusturmaTarihi
        {
            get
            {
                return this.OlusturmaTarihiField;
            }
            set
            {
                this.OlusturmaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sha256Hash
        {
            get
            {
                return this.Sha256HashField;
            }
            set
            {
                this.Sha256HashField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UUID
        {
            get
            {
                return this.UUIDField;
            }
            set
            {
                this.UUIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VKN
        {
            get
            {
                return this.VKNField;
            }
            set
            {
                this.VKNField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EFaturaTuru", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum EFaturaTuru : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Satis = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iptal = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iade = 3,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeOdeme", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeOdeme : object
    {
        
        private int BankaIdField;
        
        private PushDashboard.SiparisServis.BLEnumsOdemeDurumlari DurumField;
        
        private int IdField;
        
        private int IslemTipField;
        
        private string KartNoField;
        
        private string KartUzerindekiIsimField;
        
        private double KurField;
        
        private string ParaBirimiField;
        
        private string PosRefIdField;
        
        private int SiparisIdField;
        
        private PushDashboard.SiparisServis.WebIadeOdemeSiparisUrunBilgi[] SiparisUrunBilgisiField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaId
        {
            get
            {
                return this.BankaIdField;
            }
            set
            {
                this.BankaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.BLEnumsOdemeDurumlari Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTip
        {
            get
            {
                return this.IslemTipField;
            }
            set
            {
                this.IslemTipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KartNo
        {
            get
            {
                return this.KartNoField;
            }
            set
            {
                this.KartNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KartUzerindekiIsim
        {
            get
            {
                return this.KartUzerindekiIsimField;
            }
            set
            {
                this.KartUzerindekiIsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Kur
        {
            get
            {
                return this.KurField;
            }
            set
            {
                this.KurField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PosRefId
        {
            get
            {
                return this.PosRefIdField;
            }
            set
            {
                this.PosRefIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebIadeOdemeSiparisUrunBilgi[] SiparisUrunBilgisi
        {
            get
            {
                return this.SiparisUrunBilgisiField;
            }
            set
            {
                this.SiparisUrunBilgisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeOdemeSiparisUrunBilgi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeOdemeSiparisUrunBilgi : object
    {
        
        private double AdetField;
        
        private int SiparisUrunIdField;
        
        private int UrunIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunId
        {
            get
            {
                return this.SiparisUrunIdField;
            }
            set
            {
                this.SiparisUrunIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunId
        {
            get
            {
                return this.UrunIdField;
            }
            set
            {
                this.UrunIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EnumKeyValue", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class EnumKeyValue : object
    {
        
        private int KeyField;
        
        private string ValueField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Key
        {
            get
            {
                return this.KeyField;
            }
            set
            {
                this.KeyField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Value
        {
            get
            {
                return this.ValueField;
            }
            set
            {
                this.ValueField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisDurumlari", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebSiparisDurumlari : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnSiparis = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnayBekliyor = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Onaylandi = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeBekliyor = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Paketleniyor = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TedarikEdiliyor = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KargoyaVerildi = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimEdildi = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iptal = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iade = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Silinmis = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeTalepAlindi = 11,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeUlastiOdemeYapilacak = 12,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeOdemeYapildi = 13,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimOncesiIptal = 14,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalTalebi = 15,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KismiIadeTalebi = 16,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KismiIadeYapildi = 17,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimEdilemedi = 18,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisDurumLog", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisDurumLog : object
    {
        
        private PushDashboard.SiparisServis.WebSiparisDurumlari DurumField;
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        private System.DateTime TarihField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisDurumlari Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisOzelAlanlarRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisOzelAlanlarRequest : object
    {
        
        private string OzelAlan1Field;
        
        private string OzelAlan2Field;
        
        private string OzelAlan3Field;
        
        private int SiparisIdField;
        
        private bool SiparisOzelAlan1GuncelleField;
        
        private bool SiparisOzelAlan2GuncelleField;
        
        private bool SiparisOzelAlan3GuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan1
        {
            get
            {
                return this.OzelAlan1Field;
            }
            set
            {
                this.OzelAlan1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan2
        {
            get
            {
                return this.OzelAlan2Field;
            }
            set
            {
                this.OzelAlan2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OzelAlan3
        {
            get
            {
                return this.OzelAlan3Field;
            }
            set
            {
                this.OzelAlan3Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisOzelAlan1Guncelle
        {
            get
            {
                return this.SiparisOzelAlan1GuncelleField;
            }
            set
            {
                this.SiparisOzelAlan1GuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisOzelAlan2Guncelle
        {
            get
            {
                return this.SiparisOzelAlan2GuncelleField;
            }
            set
            {
                this.SiparisOzelAlan2GuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisOzelAlan3Guncelle
        {
            get
            {
                return this.SiparisOzelAlan3GuncelleField;
            }
            set
            {
                this.SiparisOzelAlan3GuncelleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSepetResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSepetResponse : object
    {
        
        private bool NextField;
        
        private PushDashboard.SiparisServis.WebSepet[] SepetlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Next
        {
            get
            {
                return this.NextField;
            }
            set
            {
                this.NextField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSepet[] Sepetler
        {
            get
            {
                return this.SepetlerField;
            }
            set
            {
                this.SepetlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSepet", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSepet : object
    {
        
        private string GuidSepetIDField;
        
        private int IDField;
        
        private System.DateTime SepetTarihiField;
        
        private PushDashboard.SiparisServis.WebSepetUrun[] UrunlerField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        private string UyeMailField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GuidSepetID
        {
            get
            {
                return this.GuidSepetIDField;
            }
            set
            {
                this.GuidSepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SepetTarihi
        {
            get
            {
                return this.SepetTarihiField;
            }
            set
            {
                this.SepetTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSepetUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeMail
        {
            get
            {
                return this.UyeMailField;
            }
            set
            {
                this.UyeMailField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSepetUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSepetUrun : object
    {
        
        private double AdetField;
        
        private string GuidSepetIDField;
        
        private string GuidSepetUrunIDField;
        
        private int IDField;
        
        private double KDVOraniField;
        
        private double KDVTutariField;
        
        private double KargoUcretiField;
        
        private string ParaBirimiField;
        
        private string ParaBirimiDilKoduField;
        
        private int SepetIDField;
        
        private string SpotResimField;
        
        private string StokKoduField;
        
        private bool UcretsizKargoField;
        
        private string UrunAdiField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private double UrunSepetFiyatiField;
        
        private double UrunSepetFiyatiKDVField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GuidSepetID
        {
            get
            {
                return this.GuidSepetIDField;
            }
            set
            {
                this.GuidSepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GuidSepetUrunID
        {
            get
            {
                return this.GuidSepetUrunIDField;
            }
            set
            {
                this.GuidSepetUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KDVOrani
        {
            get
            {
                return this.KDVOraniField;
            }
            set
            {
                this.KDVOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KDVTutari
        {
            get
            {
                return this.KDVTutariField;
            }
            set
            {
                this.KDVTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoUcreti
        {
            get
            {
                return this.KargoUcretiField;
            }
            set
            {
                this.KargoUcretiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimiDilKodu
        {
            get
            {
                return this.ParaBirimiDilKoduField;
            }
            set
            {
                this.ParaBirimiDilKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetID
        {
            get
            {
                return this.SepetIDField;
            }
            set
            {
                this.SepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SpotResim
        {
            get
            {
                return this.SpotResimField;
            }
            set
            {
                this.SpotResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UcretsizKargo
        {
            get
            {
                return this.UcretsizKargoField;
            }
            set
            {
                this.UcretsizKargoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSepetFiyati
        {
            get
            {
                return this.UrunSepetFiyatiField;
            }
            set
            {
                this.UrunSepetFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSepetFiyatiKDV
        {
            get
            {
                return this.UrunSepetFiyatiKDVField;
            }
            set
            {
                this.UrunSepetFiyatiKDVField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebHediyeCekiUygulaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebHediyeCekiUygulaRequest : object
    {
        
        private string HediyeCekiKoduField;
        
        private int SepetIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCekiKodu
        {
            get
            {
                return this.HediyeCekiKoduField;
            }
            set
            {
                this.HediyeCekiKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetID
        {
            get
            {
                return this.SepetIDField;
            }
            set
            {
                this.SepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebHediyeCekiOlusturRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebHediyeCekiOlusturRequest : object
    {
        
        private bool AndroidAktifField;
        
        private bool BaskaKampanyalarlaBilestirilebilirField;
        
        private PushDashboard.SiparisServis.BLHediyeCekiKampanya[] BirlestirilebilirKampanyalarField;
        
        private string GrupAdiField;
        
        private System.Nullable<bool> HediyeCekiniOdemeOlarakKaydetField;
        
        private bool IOSAktifField;
        
        private double IndirimDegeriField;
        
        private int IndirimTipiField;
        
        private bool IndirimliUrunleKullanilabilirField;
        
        private bool KargoUcretsizField;
        
        private int KategoriIdField;
        
        private string KodField;
        
        private int KullanimSayisiField;
        
        private int MarkaIDField;
        
        private bool MinTutarKosuldaGecerliField;
        
        private double MinimumUrunTutariField;
        
        private bool MobilAktifField;
        
        private System.Nullable<bool> ParcaliIadeAktifField;
        
        private System.Nullable<bool> ParcaliIptalAktifField;
        
        private System.DateTime TarihBaslagicField;
        
        private System.DateTime TarihBitisField;
        
        private System.Nullable<bool> TumuIadeAktifField;
        
        private System.Nullable<bool> TumuIptalAktifField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        private int UyeMaksimumKullanimSayisiField;
        
        private bool WebAktifField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AndroidAktif
        {
            get
            {
                return this.AndroidAktifField;
            }
            set
            {
                this.AndroidAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BaskaKampanyalarlaBilestirilebilir
        {
            get
            {
                return this.BaskaKampanyalarlaBilestirilebilirField;
            }
            set
            {
                this.BaskaKampanyalarlaBilestirilebilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.BLHediyeCekiKampanya[] BirlestirilebilirKampanyalar
        {
            get
            {
                return this.BirlestirilebilirKampanyalarField;
            }
            set
            {
                this.BirlestirilebilirKampanyalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GrupAdi
        {
            get
            {
                return this.GrupAdiField;
            }
            set
            {
                this.GrupAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> HediyeCekiniOdemeOlarakKaydet
        {
            get
            {
                return this.HediyeCekiniOdemeOlarakKaydetField;
            }
            set
            {
                this.HediyeCekiniOdemeOlarakKaydetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IOSAktif
        {
            get
            {
                return this.IOSAktifField;
            }
            set
            {
                this.IOSAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimDegeri
        {
            get
            {
                return this.IndirimDegeriField;
            }
            set
            {
                this.IndirimDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IndirimTipi
        {
            get
            {
                return this.IndirimTipiField;
            }
            set
            {
                this.IndirimTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IndirimliUrunleKullanilabilir
        {
            get
            {
                return this.IndirimliUrunleKullanilabilirField;
            }
            set
            {
                this.IndirimliUrunleKullanilabilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoUcretsiz
        {
            get
            {
                return this.KargoUcretsizField;
            }
            set
            {
                this.KargoUcretsizField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KategoriId
        {
            get
            {
                return this.KategoriIdField;
            }
            set
            {
                this.KategoriIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kod
        {
            get
            {
                return this.KodField;
            }
            set
            {
                this.KodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KullanimSayisi
        {
            get
            {
                return this.KullanimSayisiField;
            }
            set
            {
                this.KullanimSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MarkaID
        {
            get
            {
                return this.MarkaIDField;
            }
            set
            {
                this.MarkaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MinTutarKosuldaGecerli
        {
            get
            {
                return this.MinTutarKosuldaGecerliField;
            }
            set
            {
                this.MinTutarKosuldaGecerliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double MinimumUrunTutari
        {
            get
            {
                return this.MinimumUrunTutariField;
            }
            set
            {
                this.MinimumUrunTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MobilAktif
        {
            get
            {
                return this.MobilAktifField;
            }
            set
            {
                this.MobilAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ParcaliIadeAktif
        {
            get
            {
                return this.ParcaliIadeAktifField;
            }
            set
            {
                this.ParcaliIadeAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> ParcaliIptalAktif
        {
            get
            {
                return this.ParcaliIptalAktifField;
            }
            set
            {
                this.ParcaliIptalAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime TarihBaslagic
        {
            get
            {
                return this.TarihBaslagicField;
            }
            set
            {
                this.TarihBaslagicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime TarihBitis
        {
            get
            {
                return this.TarihBitisField;
            }
            set
            {
                this.TarihBitisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> TumuIadeAktif
        {
            get
            {
                return this.TumuIadeAktifField;
            }
            set
            {
                this.TumuIadeAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> TumuIptalAktif
        {
            get
            {
                return this.TumuIptalAktifField;
            }
            set
            {
                this.TumuIptalAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeMaksimumKullanimSayisi
        {
            get
            {
                return this.UyeMaksimumKullanimSayisiField;
            }
            set
            {
                this.UyeMaksimumKullanimSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool WebAktif
        {
            get
            {
                return this.WebAktifField;
            }
            set
            {
                this.WebAktifField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLHediyeCekiKampanya", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public partial class BLHediyeCekiKampanya : object
    {
        
        private int idField;
        
        private string tanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string tanim
        {
            get
            {
                return this.tanimField;
            }
            set
            {
                this.tanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetHediyeCekiKullanildiRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetHediyeCekiKullanildiRequest : object
    {
        
        private int HediyeCekiIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HediyeCekiId
        {
            get
            {
                return this.HediyeCekiIdField;
            }
            set
            {
                this.HediyeCekiIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisDurumRequest : object
    {
        
        private PushDashboard.SiparisServis.WebSiparisDurumlari DurumField;
        
        private string KargoTakipLinkField;
        
        private string KargoTakipNoField;
        
        private bool MailBilgilendirField;
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebSiparisDurumlari Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNo
        {
            get
            {
                return this.KargoTakipNoField;
            }
            set
            {
                this.KargoTakipNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailBilgilendir
        {
            get
            {
                return this.MailBilgilendirField;
            }
            set
            {
                this.MailBilgilendirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisUrunDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisUrunDurumRequest : object
    {
        
        private double AdetField;
        
        private bool BankaKomisyonuIadeField;
        
        private int DurumIDField;
        
        private int IadeNedenIDField;
        
        private int IslemField;
        
        private bool KargoTutariIadeField;
        
        private bool KartaIadeYapField;
        
        private int SiparisIDField;
        
        private int SiparisUrunIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BankaKomisyonuIade
        {
            get
            {
                return this.BankaKomisyonuIadeField;
            }
            set
            {
                this.BankaKomisyonuIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeNedenID
        {
            get
            {
                return this.IadeNedenIDField;
            }
            set
            {
                this.IadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoTutariIade
        {
            get
            {
                return this.KargoTutariIadeField;
            }
            set
            {
                this.KargoTutariIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KartaIadeYap
        {
            get
            {
                return this.KartaIadeYapField;
            }
            set
            {
                this.KartaIadeYapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisiKargoyaGonderRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisiKargoyaGonderRequest : object
    {
        
        private int KargoEntegrasyonIdField;
        
        private string MngMasrafkoduField;
        
        private int PaketIdField;
        
        private int SiparisIdField;
        
        private int SiparisUrunDurumuIdField;
        
        private bool SmsGonderField;
        
        private bool UpsGonderiOncesiBildirField;
        
        private int UpsPaketAdediField;
        
        private string UpsPaketTipiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoEntegrasyonId
        {
            get
            {
                return this.KargoEntegrasyonIdField;
            }
            set
            {
                this.KargoEntegrasyonIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MngMasrafkodu
        {
            get
            {
                return this.MngMasrafkoduField;
            }
            set
            {
                this.MngMasrafkoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PaketId
        {
            get
            {
                return this.PaketIdField;
            }
            set
            {
                this.PaketIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunDurumuId
        {
            get
            {
                return this.SiparisUrunDurumuIdField;
            }
            set
            {
                this.SiparisUrunDurumuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsGonder
        {
            get
            {
                return this.SmsGonderField;
            }
            set
            {
                this.SmsGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UpsGonderiOncesiBildir
        {
            get
            {
                return this.UpsGonderiOncesiBildirField;
            }
            set
            {
                this.UpsGonderiOncesiBildirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UpsPaketAdedi
        {
            get
            {
                return this.UpsPaketAdediField;
            }
            set
            {
                this.UpsPaketAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UpsPaketTipi
        {
            get
            {
                return this.UpsPaketTipiField;
            }
            set
            {
                this.UpsPaketTipiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisiKargoyaGonderResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisiKargoyaGonderResponse : object
    {
        
        private int CodeField;
        
        private string DetailField;
        
        private bool IsErrorField;
        
        private string MessageField;
        
        private string ModelField;
        
        private string SourceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Code
        {
            get
            {
                return this.CodeField;
            }
            set
            {
                this.CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Detail
        {
            get
            {
                return this.DetailField;
            }
            set
            {
                this.DetailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message
        {
            get
            {
                return this.MessageField;
            }
            set
            {
                this.MessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Model
        {
            get
            {
                return this.ModelField;
            }
            set
            {
                this.ModelField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Source
        {
            get
            {
                return this.SourceField;
            }
            set
            {
                this.SourceField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisUrunDurumlari", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisUrunDurumlari : object
    {
        
        private bool AktifField;
        
        private int IDField;
        
        private int IslemField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisKargoPaketFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisKargoPaketFiltre : object
    {
        
        private int KargoEntegrasyonIDField;
        
        private int KargoTakipNoDurumField;
        
        private System.Nullable<System.DateTime> PaketlenmeTarihBasField;
        
        private System.Nullable<System.DateTime> PaketlenmeTarihBitField;
        
        private bool SiparisDurumIptalField;
        
        private int SiparisIDField;
        
        private int[] SiparisIDListField;
        
        private int SiparisKargoPaketIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoEntegrasyonID
        {
            get
            {
                return this.KargoEntegrasyonIDField;
            }
            set
            {
                this.KargoEntegrasyonIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoTakipNoDurum
        {
            get
            {
                return this.KargoTakipNoDurumField;
            }
            set
            {
                this.KargoTakipNoDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> PaketlenmeTarihBas
        {
            get
            {
                return this.PaketlenmeTarihBasField;
            }
            set
            {
                this.PaketlenmeTarihBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> PaketlenmeTarihBit
        {
            get
            {
                return this.PaketlenmeTarihBitField;
            }
            set
            {
                this.PaketlenmeTarihBitField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisDurumIptal
        {
            get
            {
                return this.SiparisDurumIptalField;
            }
            set
            {
                this.SiparisDurumIptalField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] SiparisIDList
        {
            get
            {
                return this.SiparisIDListField;
            }
            set
            {
                this.SiparisIDListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisKargoPaketID
        {
            get
            {
                return this.SiparisKargoPaketIDField;
            }
            set
            {
                this.SiparisKargoPaketIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKargoPaket", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKargoPaket : object
    {
        
        private string BarkodBigisiField;
        
        private System.DateTime EklenmeTarihiField;
        
        private int IDField;
        
        private int KargoEntegrasyonIDField;
        
        private string KargoEntegrasyonTanimField;
        
        private string KargoTakipLinkField;
        
        private bool KargoTakipLinkGosterField;
        
        private string KargoTakipNumarasiField;
        
        private int SiparisDurumField;
        
        private int SiparisIDField;
        
        private int SiparisUrunDurumIDField;
        
        private string SiparisUrunDurumTanimField;
        
        private PushDashboard.SiparisServis.SiparisKargoPaketUrun[] Urun_ListeField;
        
        private int[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BarkodBigisi
        {
            get
            {
                return this.BarkodBigisiField;
            }
            set
            {
                this.BarkodBigisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklenmeTarihi
        {
            get
            {
                return this.EklenmeTarihiField;
            }
            set
            {
                this.EklenmeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoEntegrasyonID
        {
            get
            {
                return this.KargoEntegrasyonIDField;
            }
            set
            {
                this.KargoEntegrasyonIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoEntegrasyonTanim
        {
            get
            {
                return this.KargoEntegrasyonTanimField;
            }
            set
            {
                this.KargoEntegrasyonTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipLink
        {
            get
            {
                return this.KargoTakipLinkField;
            }
            set
            {
                this.KargoTakipLinkField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoTakipLinkGoster
        {
            get
            {
                return this.KargoTakipLinkGosterField;
            }
            set
            {
                this.KargoTakipLinkGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoTakipNumarasi
        {
            get
            {
                return this.KargoTakipNumarasiField;
            }
            set
            {
                this.KargoTakipNumarasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisDurum
        {
            get
            {
                return this.SiparisDurumField;
            }
            set
            {
                this.SiparisDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunDurumID
        {
            get
            {
                return this.SiparisUrunDurumIDField;
            }
            set
            {
                this.SiparisUrunDurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisUrunDurumTanim
        {
            get
            {
                return this.SiparisUrunDurumTanimField;
            }
            set
            {
                this.SiparisUrunDurumTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.SiparisKargoPaketUrun[] Urun_Liste
        {
            get
            {
                return this.Urun_ListeField;
            }
            set
            {
                this.Urun_ListeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisKargoPaketUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisKargoPaketUrun : object
    {
        
        private double AdetField;
        
        private int IDField;
        
        private string UrunAdiField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private string UrunResmiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunResmi
        {
            get
            {
                return this.UrunResmiField;
            }
            set
            {
                this.UrunResmiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetKargoSecenekRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetKargoSecenekRequest : object
    {
        
        private int IlceIdField;
        
        private int MahalleIdField;
        
        private string ParaBirimiField;
        
        private int SehirIdField;
        
        private int SemtIdField;
        
        private PushDashboard.SiparisServis.ServisSepet SepetField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceId
        {
            get
            {
                return this.IlceIdField;
            }
            set
            {
                this.IlceIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MahalleId
        {
            get
            {
                return this.MahalleIdField;
            }
            set
            {
                this.MahalleIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SehirId
        {
            get
            {
                return this.SehirIdField;
            }
            set
            {
                this.SehirIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SemtId
        {
            get
            {
                return this.SemtIdField;
            }
            set
            {
                this.SemtIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.ServisSepet Sepet
        {
            get
            {
                return this.SepetField;
            }
            set
            {
                this.SepetField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSepet", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSepet : object
    {
        
        private double GenelKDVToplamField;
        
        private double GenelToplamField;
        
        private string HediyeCekiKoduField;
        
        private double HediyeCekiTutariField;
        
        private int HediyeCekiZubizuKampanyaIdField;
        
        private double HediyePaketiTutariField;
        
        private double HopiIndirimiField;
        
        private double HopiParacikKullanimiField;
        
        private double IndirimlerToplamiField;
        
        private int KampanyaIDField;
        
        private double KampanyaIndirimKDVField;
        
        private double KampanyaIndirimTutariField;
        
        private double KampanyasizUrunlerToplamiField;
        
        private int OverrateSahipIdField;
        
        private string SahipIDField;
        
        private int SepetIDField;
        
        private string SepetParaBirimiDilKoduField;
        
        private double ToplamKDVField;
        
        private double ToplamTutarField;
        
        private double ToplamUrunAdediField;
        
        private double UrunOzellestirmeFiyatlariField;
        
        private PushDashboard.SiparisServis.ServisSepetUrun[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double GenelKDVToplam
        {
            get
            {
                return this.GenelKDVToplamField;
            }
            set
            {
                this.GenelKDVToplamField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double GenelToplam
        {
            get
            {
                return this.GenelToplamField;
            }
            set
            {
                this.GenelToplamField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HediyeCekiKodu
        {
            get
            {
                return this.HediyeCekiKoduField;
            }
            set
            {
                this.HediyeCekiKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyeCekiTutari
        {
            get
            {
                return this.HediyeCekiTutariField;
            }
            set
            {
                this.HediyeCekiTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HediyeCekiZubizuKampanyaId
        {
            get
            {
                return this.HediyeCekiZubizuKampanyaIdField;
            }
            set
            {
                this.HediyeCekiZubizuKampanyaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HediyePaketiTutari
        {
            get
            {
                return this.HediyePaketiTutariField;
            }
            set
            {
                this.HediyePaketiTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HopiIndirimi
        {
            get
            {
                return this.HopiIndirimiField;
            }
            set
            {
                this.HopiIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double HopiParacikKullanimi
        {
            get
            {
                return this.HopiParacikKullanimiField;
            }
            set
            {
                this.HopiParacikKullanimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimlerToplami
        {
            get
            {
                return this.IndirimlerToplamiField;
            }
            set
            {
                this.IndirimlerToplamiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyaIndirimKDV
        {
            get
            {
                return this.KampanyaIndirimKDVField;
            }
            set
            {
                this.KampanyaIndirimKDVField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyaIndirimTutari
        {
            get
            {
                return this.KampanyaIndirimTutariField;
            }
            set
            {
                this.KampanyaIndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyasizUrunlerToplami
        {
            get
            {
                return this.KampanyasizUrunlerToplamiField;
            }
            set
            {
                this.KampanyasizUrunlerToplamiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OverrateSahipId
        {
            get
            {
                return this.OverrateSahipIdField;
            }
            set
            {
                this.OverrateSahipIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SahipID
        {
            get
            {
                return this.SahipIDField;
            }
            set
            {
                this.SahipIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetID
        {
            get
            {
                return this.SepetIDField;
            }
            set
            {
                this.SepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SepetParaBirimiDilKodu
        {
            get
            {
                return this.SepetParaBirimiDilKoduField;
            }
            set
            {
                this.SepetParaBirimiDilKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamKDV
        {
            get
            {
                return this.ToplamKDVField;
            }
            set
            {
                this.ToplamKDVField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamTutar
        {
            get
            {
                return this.ToplamTutarField;
            }
            set
            {
                this.ToplamTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamUrunAdedi
        {
            get
            {
                return this.ToplamUrunAdediField;
            }
            set
            {
                this.ToplamUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunOzellestirmeFiyatlari
        {
            get
            {
                return this.UrunOzellestirmeFiyatlariField;
            }
            set
            {
                this.UrunOzellestirmeFiyatlariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.ServisSepetUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSepetUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSepetUrun : object
    {
        
        private int ASortiUrunKartiIDField;
        
        private double AdetField;
        
        private float DesiField;
        
        private string EkSecenekOzellikField;
        
        private int EtiketIdField;
        
        private double FiyatiField;
        
        private int FormIdField;
        
        private int[] FormIdListField;
        
        private int IDField;
        
        private double IndirimTutariField;
        
        private double KDVOraniField;
        
        private double KDVTutariField;
        
        private int KampanyaBagliUrunIDField;
        
        private int KampanyaIDField;
        
        private bool KampanyaIleEklendiField;
        
        private double KampanyaKaynakliIndirimTutariField;
        
        private string[] KampanyaTanimlariField;
        
        private double KargoIndirimiField;
        
        private double KargoUcretiField;
        
        private int[] KategorilerField;
        
        private int MaksTaksitSayisiField;
        
        private string MarkaField;
        
        private int MarkaIDField;
        
        private double OzellestirmeTutariField;
        
        private string ParaBirimiField;
        
        private string ParaBirimiDilKoduField;
        
        private bool SanalUrunField;
        
        private double SepetAlimMaksField;
        
        private double SepetAlimMinField;
        
        private string SpotResimField;
        
        private string StokKoduField;
        
        private double ToplamIndirimTutariField;
        
        private double ToplamKDVTutariField;
        
        private double ToplamSatisFiyatiField;
        
        private double ToplamUrunSepetFiyatiField;
        
        private double ToplamUrunSepetFiyatiKDVliField;
        
        private string UrlField;
        
        private double UrunAdediKademeDegerField;
        
        private double UrunAdediMinimumDegerField;
        
        private bool UrunAdediOndalikliSayiGirilebilirField;
        
        private string UrunAdiField;
        
        private int UrunIDField;
        
        private bool UrunKampanyaliEklendiField;
        
        private int UrunKartiIDField;
        
        private int UrunKategoriIdField;
        
        private string UrunNotuField;
        
        private double UrunSatisFiyatiField;
        
        private double UrunSatisFiyatiKDVField;
        
        private double UrunSepetFiyatiField;
        
        private double UrunSepetFiyatiKDVField;
        
        private double UrunSepetFiyatiKDVliField;
        
        private bool isSepetUcretsizKargoField;
        
        private bool isUcretsizKargoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ASortiUrunKartiID
        {
            get
            {
                return this.ASortiUrunKartiIDField;
            }
            set
            {
                this.ASortiUrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public float Desi
        {
            get
            {
                return this.DesiField;
            }
            set
            {
                this.DesiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EkSecenekOzellik
        {
            get
            {
                return this.EkSecenekOzellikField;
            }
            set
            {
                this.EkSecenekOzellikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EtiketId
        {
            get
            {
                return this.EtiketIdField;
            }
            set
            {
                this.EtiketIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Fiyati
        {
            get
            {
                return this.FiyatiField;
            }
            set
            {
                this.FiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FormId
        {
            get
            {
                return this.FormIdField;
            }
            set
            {
                this.FormIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] FormIdList
        {
            get
            {
                return this.FormIdListField;
            }
            set
            {
                this.FormIdListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimTutari
        {
            get
            {
                return this.IndirimTutariField;
            }
            set
            {
                this.IndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KDVOrani
        {
            get
            {
                return this.KDVOraniField;
            }
            set
            {
                this.KDVOraniField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KDVTutari
        {
            get
            {
                return this.KDVTutariField;
            }
            set
            {
                this.KDVTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaBagliUrunID
        {
            get
            {
                return this.KampanyaBagliUrunIDField;
            }
            set
            {
                this.KampanyaBagliUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KampanyaIleEklendi
        {
            get
            {
                return this.KampanyaIleEklendiField;
            }
            set
            {
                this.KampanyaIleEklendiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KampanyaKaynakliIndirimTutari
        {
            get
            {
                return this.KampanyaKaynakliIndirimTutariField;
            }
            set
            {
                this.KampanyaKaynakliIndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] KampanyaTanimlari
        {
            get
            {
                return this.KampanyaTanimlariField;
            }
            set
            {
                this.KampanyaTanimlariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoIndirimi
        {
            get
            {
                return this.KargoIndirimiField;
            }
            set
            {
                this.KargoIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoUcreti
        {
            get
            {
                return this.KargoUcretiField;
            }
            set
            {
                this.KargoUcretiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] Kategoriler
        {
            get
            {
                return this.KategorilerField;
            }
            set
            {
                this.KategorilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaksTaksitSayisi
        {
            get
            {
                return this.MaksTaksitSayisiField;
            }
            set
            {
                this.MaksTaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Marka
        {
            get
            {
                return this.MarkaField;
            }
            set
            {
                this.MarkaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MarkaID
        {
            get
            {
                return this.MarkaIDField;
            }
            set
            {
                this.MarkaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OzellestirmeTutari
        {
            get
            {
                return this.OzellestirmeTutariField;
            }
            set
            {
                this.OzellestirmeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimiDilKodu
        {
            get
            {
                return this.ParaBirimiDilKoduField;
            }
            set
            {
                this.ParaBirimiDilKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SanalUrun
        {
            get
            {
                return this.SanalUrunField;
            }
            set
            {
                this.SanalUrunField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SepetAlimMaks
        {
            get
            {
                return this.SepetAlimMaksField;
            }
            set
            {
                this.SepetAlimMaksField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SepetAlimMin
        {
            get
            {
                return this.SepetAlimMinField;
            }
            set
            {
                this.SepetAlimMinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SpotResim
        {
            get
            {
                return this.SpotResimField;
            }
            set
            {
                this.SpotResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamIndirimTutari
        {
            get
            {
                return this.ToplamIndirimTutariField;
            }
            set
            {
                this.ToplamIndirimTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamKDVTutari
        {
            get
            {
                return this.ToplamKDVTutariField;
            }
            set
            {
                this.ToplamKDVTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamSatisFiyati
        {
            get
            {
                return this.ToplamSatisFiyatiField;
            }
            set
            {
                this.ToplamSatisFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamUrunSepetFiyati
        {
            get
            {
                return this.ToplamUrunSepetFiyatiField;
            }
            set
            {
                this.ToplamUrunSepetFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamUrunSepetFiyatiKDVli
        {
            get
            {
                return this.ToplamUrunSepetFiyatiKDVliField;
            }
            set
            {
                this.ToplamUrunSepetFiyatiKDVliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunAdediKademeDeger
        {
            get
            {
                return this.UrunAdediKademeDegerField;
            }
            set
            {
                this.UrunAdediKademeDegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunAdediMinimumDeger
        {
            get
            {
                return this.UrunAdediMinimumDegerField;
            }
            set
            {
                this.UrunAdediMinimumDegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UrunAdediOndalikliSayiGirilebilir
        {
            get
            {
                return this.UrunAdediOndalikliSayiGirilebilirField;
            }
            set
            {
                this.UrunAdediOndalikliSayiGirilebilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UrunKampanyaliEklendi
        {
            get
            {
                return this.UrunKampanyaliEklendiField;
            }
            set
            {
                this.UrunKampanyaliEklendiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKategoriId
        {
            get
            {
                return this.UrunKategoriIdField;
            }
            set
            {
                this.UrunKategoriIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunNotu
        {
            get
            {
                return this.UrunNotuField;
            }
            set
            {
                this.UrunNotuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSatisFiyati
        {
            get
            {
                return this.UrunSatisFiyatiField;
            }
            set
            {
                this.UrunSatisFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSatisFiyatiKDV
        {
            get
            {
                return this.UrunSatisFiyatiKDVField;
            }
            set
            {
                this.UrunSatisFiyatiKDVField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSepetFiyati
        {
            get
            {
                return this.UrunSepetFiyatiField;
            }
            set
            {
                this.UrunSepetFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSepetFiyatiKDV
        {
            get
            {
                return this.UrunSepetFiyatiKDVField;
            }
            set
            {
                this.UrunSepetFiyatiKDVField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSepetFiyatiKDVli
        {
            get
            {
                return this.UrunSepetFiyatiKDVliField;
            }
            set
            {
                this.UrunSepetFiyatiKDVliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool isSepetUcretsizKargo
        {
            get
            {
                return this.isSepetUcretsizKargoField;
            }
            set
            {
                this.isSepetUcretsizKargoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool isUcretsizKargo
        {
            get
            {
                return this.isUcretsizKargoField;
            }
            set
            {
                this.isUcretsizKargoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKargoFirma", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKargoFirma : object
    {
        
        private int IDField;
        
        private bool KapidaOdemeField;
        
        private double KapidaOdemeFiyatiField;
        
        private bool KapidaOdemeKKField;
        
        private double KapidaOdemeKKFiyatiField;
        
        private double KargoTutariField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KapidaOdeme
        {
            get
            {
                return this.KapidaOdemeField;
            }
            set
            {
                this.KapidaOdemeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KapidaOdemeFiyati
        {
            get
            {
                return this.KapidaOdemeFiyatiField;
            }
            set
            {
                this.KapidaOdemeFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KapidaOdemeKK
        {
            get
            {
                return this.KapidaOdemeKKField;
            }
            set
            {
                this.KapidaOdemeKKField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KapidaOdemeKKFiyati
        {
            get
            {
                return this.KapidaOdemeKKFiyatiField;
            }
            set
            {
                this.KapidaOdemeKKFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoTutari
        {
            get
            {
                return this.KargoTutariField;
            }
            set
            {
                this.KargoTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectWebSepetRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectWebSepetRequest : object
    {
        
        private string DilField;
        
        private string ParaBirimiField;
        
        private int SayfaSayisiField;
        
        private int SepetIdField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Dil
        {
            get
            {
                return this.DilField;
            }
            set
            {
                this.DilField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SayfaSayisi
        {
            get
            {
                return this.SayfaSayisiField;
            }
            set
            {
                this.SayfaSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetId
        {
            get
            {
                return this.SepetIdField;
            }
            set
            {
                this.SepetIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CreateSepetRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class CreateSepetRequest : object
    {
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebUpdateSepetRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebUpdateSepetRequest : object
    {
        
        private double AdetField;
        
        private bool AdetGuncelleField;
        
        private int KampanyaIDField;
        
        private int SepetIDField;
        
        private int SepetUrunIDField;
        
        private bool SepettenCikarField;
        
        private int UrunIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AdetGuncelle
        {
            get
            {
                return this.AdetGuncelleField;
            }
            set
            {
                this.AdetGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetID
        {
            get
            {
                return this.SepetIDField;
            }
            set
            {
                this.SepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetUrunID
        {
            get
            {
                return this.SepetUrunIDField;
            }
            set
            {
                this.SepetUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SepettenCikar
        {
            get
            {
                return this.SepettenCikarField;
            }
            set
            {
                this.SepettenCikarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetSepetRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetSepetRequest : object
    {
        
        private int KampanyaIDField;
        
        private int SepetIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaID
        {
            get
            {
                return this.KampanyaIDField;
            }
            set
            {
                this.KampanyaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetID
        {
            get
            {
                return this.SepetIDField;
            }
            set
            {
                this.SepetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisOdemeTipleri", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisOdemeTipleri : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSepetKampanyaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSepetKampanyaRequest : object
    {
        
        private int KampanyaIdField;
        
        private int SepetIdField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaId
        {
            get
            {
                return this.KampanyaIdField;
            }
            set
            {
                this.KampanyaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SepetId
        {
            get
            {
                return this.SepetIdField;
            }
            set
            {
                this.SepetIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLPaketlemeDurum", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public partial class BLPaketlemeDurum : object
    {
        
        private bool AktifField;
        
        private int IdField;
        
        private int IslemField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMagazaFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebMagazaFiltre : object
    {
        
        private int AktifField;
        
        private string ApiSifreField;
        
        private int IlIDField;
        
        private int IletisimdeGosterField;
        
        private int MagazaIDField;
        
        private string MagazaKoduField;
        
        private int SiparisAdimindaGosterField;
        
        private string TanimField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ApiSifre
        {
            get
            {
                return this.ApiSifreField;
            }
            set
            {
                this.ApiSifreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IletisimdeGoster
        {
            get
            {
                return this.IletisimdeGosterField;
            }
            set
            {
                this.IletisimdeGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaID
        {
            get
            {
                return this.MagazaIDField;
            }
            set
            {
                this.MagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaKodu
        {
            get
            {
                return this.MagazaKoduField;
            }
            set
            {
                this.MagazaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisAdimindaGoster
        {
            get
            {
                return this.SiparisAdimindaGosterField;
            }
            set
            {
                this.SiparisAdimindaGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMagazaAyar", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebMagazaAyar : object
    {
        
        private bool AciklamaGuncelleField;
        
        private bool AdresGuncelleField;
        
        private bool AktifGuncelleField;
        
        private bool ApiSifreGuncelleField;
        
        private bool FaksGuncelleField;
        
        private bool GsmGuncelleField;
        
        private bool IlGuncelleField;
        
        private bool IlceGuncelleField;
        
        private bool IletisimdeGosterGuncelleField;
        
        private bool KargoGonderimLimitiGuncelleField;
        
        private bool LatitudeGuncelleField;
        
        private bool LongitudeGuncelleField;
        
        private bool MagazaKoduGuncelleField;
        
        private bool MagazaResimGuncelleField;
        
        private bool MailGuncelleField;
        
        private bool SiparisAdimindaGosterGuncelleField;
        
        private bool SiraGuncelleField;
        
        private bool TanimGuncelleField;
        
        private bool TelefonGuncelleField;
        
        private bool TeslimatSaatiAktifGuncelleField;
        
        private bool UlkeGuncelleField;
        
        private string UserAgentField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AciklamaGuncelle
        {
            get
            {
                return this.AciklamaGuncelleField;
            }
            set
            {
                this.AciklamaGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AdresGuncelle
        {
            get
            {
                return this.AdresGuncelleField;
            }
            set
            {
                this.AdresGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AktifGuncelle
        {
            get
            {
                return this.AktifGuncelleField;
            }
            set
            {
                this.AktifGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ApiSifreGuncelle
        {
            get
            {
                return this.ApiSifreGuncelleField;
            }
            set
            {
                this.ApiSifreGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool FaksGuncelle
        {
            get
            {
                return this.FaksGuncelleField;
            }
            set
            {
                this.FaksGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool GsmGuncelle
        {
            get
            {
                return this.GsmGuncelleField;
            }
            set
            {
                this.GsmGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IlGuncelle
        {
            get
            {
                return this.IlGuncelleField;
            }
            set
            {
                this.IlGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IlceGuncelle
        {
            get
            {
                return this.IlceGuncelleField;
            }
            set
            {
                this.IlceGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IletisimdeGosterGuncelle
        {
            get
            {
                return this.IletisimdeGosterGuncelleField;
            }
            set
            {
                this.IletisimdeGosterGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoGonderimLimitiGuncelle
        {
            get
            {
                return this.KargoGonderimLimitiGuncelleField;
            }
            set
            {
                this.KargoGonderimLimitiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LatitudeGuncelle
        {
            get
            {
                return this.LatitudeGuncelleField;
            }
            set
            {
                this.LatitudeGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool LongitudeGuncelle
        {
            get
            {
                return this.LongitudeGuncelleField;
            }
            set
            {
                this.LongitudeGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MagazaKoduGuncelle
        {
            get
            {
                return this.MagazaKoduGuncelleField;
            }
            set
            {
                this.MagazaKoduGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MagazaResimGuncelle
        {
            get
            {
                return this.MagazaResimGuncelleField;
            }
            set
            {
                this.MagazaResimGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGuncelle
        {
            get
            {
                return this.MailGuncelleField;
            }
            set
            {
                this.MailGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisAdimindaGosterGuncelle
        {
            get
            {
                return this.SiparisAdimindaGosterGuncelleField;
            }
            set
            {
                this.SiparisAdimindaGosterGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiraGuncelle
        {
            get
            {
                return this.SiraGuncelleField;
            }
            set
            {
                this.SiraGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TanimGuncelle
        {
            get
            {
                return this.TanimGuncelleField;
            }
            set
            {
                this.TanimGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TelefonGuncelle
        {
            get
            {
                return this.TelefonGuncelleField;
            }
            set
            {
                this.TelefonGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TeslimatSaatiAktifGuncelle
        {
            get
            {
                return this.TeslimatSaatiAktifGuncelleField;
            }
            set
            {
                this.TeslimatSaatiAktifGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UlkeGuncelle
        {
            get
            {
                return this.UlkeGuncelleField;
            }
            set
            {
                this.UlkeGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UserAgent
        {
            get
            {
                return this.UserAgentField;
            }
            set
            {
                this.UserAgentField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisOdemeDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisOdemeDurumRequest : object
    {
        
        private bool BilgiMailiGondermeField;
        
        private PushDashboard.SiparisServis.WebOdemeDurumlari OdemeDurumField;
        
        private int OdemeIdField;
        
        private int SiparisIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BilgiMailiGonderme
        {
            get
            {
                return this.BilgiMailiGondermeField;
            }
            set
            {
                this.BilgiMailiGondermeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebOdemeDurumlari OdemeDurum
        {
            get
            {
                return this.OdemeDurumField;
            }
            set
            {
                this.OdemeDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeId
        {
            get
            {
                return this.OdemeIdField;
            }
            set
            {
                this.OdemeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebOdemeDurumlari", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebOdemeDurumlari : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnayBekliyor = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Onaylandi = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hatali = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeEdilmis = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalEdilmis = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectEFaturaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectEFaturaRequest : object
    {
        
        private System.Nullable<int> BasariliField;
        
        private System.Nullable<int> EFaturaTuruField;
        
        private System.Nullable<int> EntegratorField;
        
        private System.Nullable<int> FaturaTipiField;
        
        private int IdField;
        
        private System.Nullable<int> IptalEdildiField;
        
        private int KargoFirmaIdField;
        
        private System.Nullable<System.DateTime> OlusturmaTarihiBaslangicField;
        
        private System.Nullable<System.DateTime> OlusturmaTarihiBitisField;
        
        private int SiparisIdField;
        
        private System.Nullable<System.DateTime> SiparisTarihiBaslangicField;
        
        private System.Nullable<System.DateTime> SiparisTarihiBitisField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> Basarili
        {
            get
            {
                return this.BasariliField;
            }
            set
            {
                this.BasariliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> EFaturaTuru
        {
            get
            {
                return this.EFaturaTuruField;
            }
            set
            {
                this.EFaturaTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> Entegrator
        {
            get
            {
                return this.EntegratorField;
            }
            set
            {
                this.EntegratorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> FaturaTipi
        {
            get
            {
                return this.FaturaTipiField;
            }
            set
            {
                this.FaturaTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IptalEdildi
        {
            get
            {
                return this.IptalEdildiField;
            }
            set
            {
                this.IptalEdildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoFirmaId
        {
            get
            {
                return this.KargoFirmaIdField;
            }
            set
            {
                this.KargoFirmaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OlusturmaTarihiBaslangic
        {
            get
            {
                return this.OlusturmaTarihiBaslangicField;
            }
            set
            {
                this.OlusturmaTarihiBaslangicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OlusturmaTarihiBitis
        {
            get
            {
                return this.OlusturmaTarihiBitisField;
            }
            set
            {
                this.OlusturmaTarihiBitisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiBaslangic
        {
            get
            {
                return this.SiparisTarihiBaslangicField;
            }
            set
            {
                this.SiparisTarihiBaslangicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiBitis
        {
            get
            {
                return this.SiparisTarihiBitisField;
            }
            set
            {
                this.SiparisTarihiBitisField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveEFaturaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveEFaturaRequest : object
    {
        
        private PushDashboard.SiparisServis.SaveEFatura EFaturaField;
        
        private bool SiparisFaturaNoGuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.SaveEFatura EFatura
        {
            get
            {
                return this.EFaturaField;
            }
            set
            {
                this.EFaturaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisFaturaNoGuncelle
        {
            get
            {
                return this.SiparisFaturaNoGuncelleField;
            }
            set
            {
                this.SiparisFaturaNoGuncelleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EFaturaIptalRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class EFaturaIptalRequest : object
    {
        
        private int EFaturaIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EFaturaId
        {
            get
            {
                return this.EFaturaIdField;
            }
            set
            {
                this.EFaturaIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIadeOdemeFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIadeOdemeFiltre : object
    {
        
        private int BankaIdField;
        
        private System.Nullable<System.DateTime> IadeOdemeTarihiBasField;
        
        private System.Nullable<System.DateTime> IadeOdemeTarihiSonField;
        
        private PushDashboard.SiparisServis.OdemeIadeIslemTip IslemTipField;
        
        private System.Nullable<PushDashboard.SiparisServis.BLEnumsOdemeDurumlari> OdemeDurumField;
        
        private int SiparisIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaId
        {
            get
            {
                return this.BankaIdField;
            }
            set
            {
                this.BankaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IadeOdemeTarihiBas
        {
            get
            {
                return this.IadeOdemeTarihiBasField;
            }
            set
            {
                this.IadeOdemeTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IadeOdemeTarihiSon
        {
            get
            {
                return this.IadeOdemeTarihiSonField;
            }
            set
            {
                this.IadeOdemeTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.OdemeIadeIslemTip IslemTip
        {
            get
            {
                return this.IslemTipField;
            }
            set
            {
                this.IslemTipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<PushDashboard.SiparisServis.BLEnumsOdemeDurumlari> OdemeDurum
        {
            get
            {
                return this.OdemeDurumField;
            }
            set
            {
                this.OdemeDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeIadeIslemTip", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum OdemeIadeIslemTip : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hepsi = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalEdilmis = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeEdilmis = 2,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveBekleyenAramaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveBekleyenAramaRequest : object
    {
        
        private string AranacakTelefonNoField;
        
        private string AranacakUyeAdiField;
        
        private int AranacakUyeIdField;
        
        private PushDashboard.SiparisServis.WebMusteriHizmetleriDurum DurumField;
        
        private double IadeTutarField;
        
        private int IdField;
        
        private int OdemeTipiField;
        
        private int SiparisIdField;
        
        private string SiparisNoField;
        
        private string SiparisTeslimatTelefonField;
        
        private double SiparisToplamTutarField;
        
        private PushDashboard.SiparisServis.WebMusteriHizmetleriTip TipField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AranacakTelefonNo
        {
            get
            {
                return this.AranacakTelefonNoField;
            }
            set
            {
                this.AranacakTelefonNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AranacakUyeAdi
        {
            get
            {
                return this.AranacakUyeAdiField;
            }
            set
            {
                this.AranacakUyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int AranacakUyeId
        {
            get
            {
                return this.AranacakUyeIdField;
            }
            set
            {
                this.AranacakUyeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebMusteriHizmetleriDurum Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IadeTutar
        {
            get
            {
                return this.IadeTutarField;
            }
            set
            {
                this.IadeTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisTeslimatTelefon
        {
            get
            {
                return this.SiparisTeslimatTelefonField;
            }
            set
            {
                this.SiparisTeslimatTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SiparisToplamTutar
        {
            get
            {
                return this.SiparisToplamTutarField;
            }
            set
            {
                this.SiparisToplamTutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.WebMusteriHizmetleriTip Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMusteriHizmetleriDurum", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebMusteriHizmetleriDurum : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Cozuldu = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Bekliyor = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TekrarAranacak = 3,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMusteriHizmetleriTip", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebMusteriHizmetleriTip : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KapidaOdemeOnayiBekleyenSiparisler = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        EksikUrunBekleyenSiparisler = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IbanBekleyenSiparisler = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeAramasiBekleyen = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTelefonSiparisFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTelefonSiparisFiltre : object
    {
        
        private int ArandiField;
        
        private int IDField;
        
        private string IsimField;
        
        private int SatildiField;
        
        private System.Nullable<System.DateTime> Tarih1Field;
        
        private System.Nullable<System.DateTime> Tarih2Field;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Arandi
        {
            get
            {
                return this.ArandiField;
            }
            set
            {
                this.ArandiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Satildi
        {
            get
            {
                return this.SatildiField;
            }
            set
            {
                this.SatildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> Tarih1
        {
            get
            {
                return this.Tarih1Field;
            }
            set
            {
                this.Tarih1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> Tarih2
        {
            get
            {
                return this.Tarih2Field;
            }
            set
            {
                this.Tarih2Field = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTelefonSiparisSayfalama", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTelefonSiparisSayfalama : object
    {
        
        private int KayitSayisiField;
        
        private int SayfaNoField;
        
        private string SiralamaDegeriField;
        
        private string SiralamaYonuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KayitSayisi
        {
            get
            {
                return this.KayitSayisiField;
            }
            set
            {
                this.KayitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SayfaNo
        {
            get
            {
                return this.SayfaNoField;
            }
            set
            {
                this.SayfaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaDegeri
        {
            get
            {
                return this.SiralamaDegeriField;
            }
            set
            {
                this.SiralamaDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaYonu
        {
            get
            {
                return this.SiralamaYonuField;
            }
            set
            {
                this.SiralamaYonuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLTelefonSiparis", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public partial class BLTelefonSiparis : object
    {
        
        private bool ArandiField;
        
        private System.DateTime EklemeTarihiField;
        
        private System.DateTime GuncellemeTarihiField;
        
        private int IDField;
        
        private string IsimField;
        
        private string MailField;
        
        private bool MailIzinField;
        
        private string NotlarField;
        
        private string SaticiNotlarField;
        
        private double SatilanFiyatField;
        
        private bool SatildiField;
        
        private bool SmsIzinField;
        
        private string TelefonField;
        
        private string UrunAdiField;
        
        private int UrunKartiIDField;
        
        private string UrunUrlField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Arandi
        {
            get
            {
                return this.ArandiField;
            }
            set
            {
                this.ArandiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime GuncellemeTarihi
        {
            get
            {
                return this.GuncellemeTarihiField;
            }
            set
            {
                this.GuncellemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notlar
        {
            get
            {
                return this.NotlarField;
            }
            set
            {
                this.NotlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SaticiNotlar
        {
            get
            {
                return this.SaticiNotlarField;
            }
            set
            {
                this.SaticiNotlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SatilanFiyat
        {
            get
            {
                return this.SatilanFiyatField;
            }
            set
            {
                this.SatilanFiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Satildi
        {
            get
            {
                return this.SatildiField;
            }
            set
            {
                this.SatildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsIzin
        {
            get
            {
                return this.SmsIzinField;
            }
            set
            {
                this.SmsIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunUrl
        {
            get
            {
                return this.UrunUrlField;
            }
            set
            {
                this.UrunUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateTelefonlaSiparisDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateTelefonlaSiparisDurumRequest : object
    {
        
        private int ArandiField;
        
        private int IdField;
        
        private int SatildiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Arandi
        {
            get
            {
                return this.ArandiField;
            }
            set
            {
                this.ArandiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Satildi
        {
            get
            {
                return this.SatildiField;
            }
            set
            {
                this.SatildiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSiparisDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSiparisDurumRequest : object
    {
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisDurumLogFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebSiparisDurumLogFiltre : object
    {
        
        private int SiparisIDField;
        
        private System.Nullable<System.DateTime> TarihBasField;
        
        private System.Nullable<System.DateTime> TarihSonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TarihBas
        {
            get
            {
                return this.TarihBasField;
            }
            set
            {
                this.TarihBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TarihSon
        {
            get
            {
                return this.TarihSonField;
            }
            set
            {
                this.TarihSonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateEfaturaRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateEfaturaRequest : object
    {
        
        private string FaturaNumarasiField;
        
        private int FaturaTipiField;
        
        private int IDField;
        
        private System.Nullable<System.DateTime> OlusturmaTarihiField;
        
        private int SiparisIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaNumarasi
        {
            get
            {
                return this.FaturaNumarasiField;
            }
            set
            {
                this.FaturaNumarasiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FaturaTipi
        {
            get
            {
                return this.FaturaTipiField;
            }
            set
            {
                this.FaturaTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OlusturmaTarihi
        {
            get
            {
                return this.OlusturmaTarihiField;
            }
            set
            {
                this.OlusturmaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeOdemeRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeOdemeRequest : object
    {
        
        private string NotField;
        
        private int OdemeIdField;
        
        private bool PosIadeField;
        
        private int SiparisIdField;
        
        private double TutarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Not
        {
            get
            {
                return this.NotField;
            }
            set
            {
                this.NotField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeId
        {
            get
            {
                return this.OdemeIdField;
            }
            set
            {
                this.OdemeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PosIade
        {
            get
            {
                return this.PosIadeField;
            }
            set
            {
                this.PosIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisAnonimlestirRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisAnonimlestirRequest : object
    {
        
        private string SiparisKaynagiField;
        
        private string SiparisKaynagiInField;
        
        private System.Nullable<System.DateTime> SiparisTarihBasField;
        
        private System.Nullable<System.DateTime> SiparisTarihBitField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagiIn
        {
            get
            {
                return this.SiparisKaynagiInField;
            }
            set
            {
                this.SiparisKaynagiInField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihBas
        {
            get
            {
                return this.SiparisTarihBasField;
            }
            set
            {
                this.SiparisTarihBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihBit
        {
            get
            {
                return this.SiparisTarihBitField;
            }
            set
            {
                this.SiparisTarihBitField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateMarketPlaceAreasRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateMarketPlaceAreasRequest : object
    {
        
        private string DigerFaturaNoField;
        
        private System.Nullable<bool> DigerFaturaNoGuncelleField;
        
        private string IadeKomisyonFaturaNoField;
        
        private System.Nullable<bool> IadeKomisyonFaturaNoGuncelleField;
        
        private double IadeKomisyonTutariField;
        
        private System.Nullable<bool> IadeKomisyonTutariGuncelleField;
        
        private double KargoKatkiPayiField;
        
        private string KargoKatkiPayiFaturaNoField;
        
        private System.Nullable<bool> KargoKatkiPayiFaturaNoGuncelleField;
        
        private System.Nullable<bool> KargoKatkiPayiGuncelleField;
        
        private System.Nullable<System.DateTime> KargoyaSonVerilmeTarihiField;
        
        private System.Nullable<bool> KargoyaSonVerilmeTarihiGuncelleField;
        
        private string KomisyonFaturaNoField;
        
        private System.Nullable<bool> KomisyonFaturaNoGuncelleField;
        
        private bool MarketPlaceOdemeAlindiField;
        
        private System.Nullable<bool> MarketPlaceOdemeAlindiGuncelleField;
        
        private string MarketplaceKampanyaKoduField;
        
        private System.Nullable<bool> MarketplaceKampanyaKoduGuncelleField;
        
        private System.Nullable<System.DateTime> MusteriKargoTeslimTarihiField;
        
        private System.Nullable<bool> MusteriKargoTeslimTarihiGuncelleField;
        
        private System.Nullable<System.DateTime> OdemeVadeTarihiField;
        
        private double PazaryeriKomisyonTutariField;
        
        private System.Nullable<bool> PazaryeriKomisyonTutariGuncelleField;
        
        private double PazaryeriOdemeTutariField;
        
        private System.Nullable<bool> PazaryeriOdemeTutariGuncelleField;
        
        private int SiparisIdField;
        
        private System.Nullable<bool> VadeTarihiGuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DigerFaturaNo
        {
            get
            {
                return this.DigerFaturaNoField;
            }
            set
            {
                this.DigerFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> DigerFaturaNoGuncelle
        {
            get
            {
                return this.DigerFaturaNoGuncelleField;
            }
            set
            {
                this.DigerFaturaNoGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IadeKomisyonFaturaNo
        {
            get
            {
                return this.IadeKomisyonFaturaNoField;
            }
            set
            {
                this.IadeKomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IadeKomisyonFaturaNoGuncelle
        {
            get
            {
                return this.IadeKomisyonFaturaNoGuncelleField;
            }
            set
            {
                this.IadeKomisyonFaturaNoGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IadeKomisyonTutari
        {
            get
            {
                return this.IadeKomisyonTutariField;
            }
            set
            {
                this.IadeKomisyonTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IadeKomisyonTutariGuncelle
        {
            get
            {
                return this.IadeKomisyonTutariGuncelleField;
            }
            set
            {
                this.IadeKomisyonTutariGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoKatkiPayi
        {
            get
            {
                return this.KargoKatkiPayiField;
            }
            set
            {
                this.KargoKatkiPayiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoKatkiPayiFaturaNo
        {
            get
            {
                return this.KargoKatkiPayiFaturaNoField;
            }
            set
            {
                this.KargoKatkiPayiFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KargoKatkiPayiFaturaNoGuncelle
        {
            get
            {
                return this.KargoKatkiPayiFaturaNoGuncelleField;
            }
            set
            {
                this.KargoKatkiPayiFaturaNoGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KargoKatkiPayiGuncelle
        {
            get
            {
                return this.KargoKatkiPayiGuncelleField;
            }
            set
            {
                this.KargoKatkiPayiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> KargoyaSonVerilmeTarihi
        {
            get
            {
                return this.KargoyaSonVerilmeTarihiField;
            }
            set
            {
                this.KargoyaSonVerilmeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KargoyaSonVerilmeTarihiGuncelle
        {
            get
            {
                return this.KargoyaSonVerilmeTarihiGuncelleField;
            }
            set
            {
                this.KargoyaSonVerilmeTarihiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KomisyonFaturaNo
        {
            get
            {
                return this.KomisyonFaturaNoField;
            }
            set
            {
                this.KomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KomisyonFaturaNoGuncelle
        {
            get
            {
                return this.KomisyonFaturaNoGuncelleField;
            }
            set
            {
                this.KomisyonFaturaNoGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MarketPlaceOdemeAlindi
        {
            get
            {
                return this.MarketPlaceOdemeAlindiField;
            }
            set
            {
                this.MarketPlaceOdemeAlindiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> MarketPlaceOdemeAlindiGuncelle
        {
            get
            {
                return this.MarketPlaceOdemeAlindiGuncelleField;
            }
            set
            {
                this.MarketPlaceOdemeAlindiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketplaceKampanyaKodu
        {
            get
            {
                return this.MarketplaceKampanyaKoduField;
            }
            set
            {
                this.MarketplaceKampanyaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> MarketplaceKampanyaKoduGuncelle
        {
            get
            {
                return this.MarketplaceKampanyaKoduGuncelleField;
            }
            set
            {
                this.MarketplaceKampanyaKoduGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> MusteriKargoTeslimTarihi
        {
            get
            {
                return this.MusteriKargoTeslimTarihiField;
            }
            set
            {
                this.MusteriKargoTeslimTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> MusteriKargoTeslimTarihiGuncelle
        {
            get
            {
                return this.MusteriKargoTeslimTarihiGuncelleField;
            }
            set
            {
                this.MusteriKargoTeslimTarihiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeVadeTarihi
        {
            get
            {
                return this.OdemeVadeTarihiField;
            }
            set
            {
                this.OdemeVadeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriKomisyonTutari
        {
            get
            {
                return this.PazaryeriKomisyonTutariField;
            }
            set
            {
                this.PazaryeriKomisyonTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> PazaryeriKomisyonTutariGuncelle
        {
            get
            {
                return this.PazaryeriKomisyonTutariGuncelleField;
            }
            set
            {
                this.PazaryeriKomisyonTutariGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriOdemeTutari
        {
            get
            {
                return this.PazaryeriOdemeTutariField;
            }
            set
            {
                this.PazaryeriOdemeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> PazaryeriOdemeTutariGuncelle
        {
            get
            {
                return this.PazaryeriOdemeTutariGuncelleField;
            }
            set
            {
                this.PazaryeriOdemeTutariGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> VadeTarihiGuncelle
        {
            get
            {
                return this.VadeTarihiGuncelleField;
            }
            set
            {
                this.VadeTarihiGuncelleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SetSiparisFaturaUrlRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SetSiparisFaturaUrlRequest : object
    {
        
        private string FaturaNoField;
        
        private System.Nullable<System.DateTime> FaturaTarihiField;
        
        private string FaturaUrlField;
        
        private int SiparisIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaNo
        {
            get
            {
                return this.FaturaNoField;
            }
            set
            {
                this.FaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> FaturaTarihi
        {
            get
            {
                return this.FaturaTarihiField;
            }
            set
            {
                this.FaturaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FaturaUrl
        {
            get
            {
                return this.FaturaUrlField;
            }
            set
            {
                this.FaturaUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MarketPlaceParamsRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class MarketPlaceParamsRequest : object
    {
        
        private int IdField;
        
        private int IslemField;
        
        private string MarketPlaceParamsField;
        
        private int SiparisIdField;
        
        private string SiparisNoField;
        
        private int TabloField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MarketPlaceParams
        {
            get
            {
                return this.MarketPlaceParamsField;
            }
            set
            {
                this.MarketPlaceParamsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tablo
        {
            get
            {
                return this.TabloField;
            }
            set
            {
                this.TabloField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLHataliOdeme", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public partial class BLHataliOdeme : object
    {
        
        private string BankaField;
        
        private string HataField;
        
        private int SiparisIDField;
        
        private int TaksitSayisiField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        private string UyeAdiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Banka
        {
            get
            {
                return this.BankaField;
            }
            set
            {
                this.BankaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Hata
        {
            get
            {
                return this.HataField;
            }
            set
            {
                this.HataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaksitSayisi
        {
            get
            {
                return this.TaksitSayisiField;
            }
            set
            {
                this.TaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisUrunIptalIadeRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisUrunIptalIadeRequest : object
    {
        
        private double IadeTutariField;
        
        private bool KargoTutariIadeField;
        
        private bool KartaIadeField;
        
        private int SiparisIdField;
        
        private PushDashboard.SiparisServis.IptalIadeSiparisUrun[] SiparisUrunListeField;
        
        private int TumUrunlerDurumIDField;
        
        private int TumUrunlerIadeNedenIDField;
        
        private int TumUrunlerIslemField;
        
        private bool TumuIptalField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IadeTutari
        {
            get
            {
                return this.IadeTutariField;
            }
            set
            {
                this.IadeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoTutariIade
        {
            get
            {
                return this.KargoTutariIadeField;
            }
            set
            {
                this.KargoTutariIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KartaIade
        {
            get
            {
                return this.KartaIadeField;
            }
            set
            {
                this.KartaIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.SiparisServis.IptalIadeSiparisUrun[] SiparisUrunListe
        {
            get
            {
                return this.SiparisUrunListeField;
            }
            set
            {
                this.SiparisUrunListeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunlerDurumID
        {
            get
            {
                return this.TumUrunlerDurumIDField;
            }
            set
            {
                this.TumUrunlerDurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunlerIadeNedenID
        {
            get
            {
                return this.TumUrunlerIadeNedenIDField;
            }
            set
            {
                this.TumUrunlerIadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunlerIslem
        {
            get
            {
                return this.TumUrunlerIslemField;
            }
            set
            {
                this.TumUrunlerIslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TumuIptal
        {
            get
            {
                return this.TumuIptalField;
            }
            set
            {
                this.TumuIptalField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IptalIadeSiparisUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IptalIadeSiparisUrun : object
    {
        
        private double AdetField;
        
        private int DurumIDField;
        
        private int IadeNedenIDField;
        
        private int IslemField;
        
        private int SiparisUrunIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeNedenID
        {
            get
            {
                return this.IadeNedenIDField;
            }
            set
            {
                this.IadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Islem
        {
            get
            {
                return this.IslemField;
            }
            set
            {
                this.IslemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeOnProvizyonKapatRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class OdemeOnProvizyonKapatRequest : object
    {
        
        private int OdemeIdField;
        
        private double TutarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeId
        {
            get
            {
                return this.OdemeIdField;
            }
            set
            {
                this.OdemeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveSiparisOdemeRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveSiparisOdemeRequest : object
    {
        
        private System.Nullable<int> HavaleHesapIdField;
        
        private System.Nullable<int> OdemeSecenekIdField;
        
        private string OrderNumberField;
        
        private string RefNoField;
        
        private int SiparisIdField;
        
        private bool SiparisMailGonderField;
        
        private bool SiparisSmsGonderField;
        
        private int TipField;
        
        private double TutarField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> HavaleHesapId
        {
            get
            {
                return this.HavaleHesapIdField;
            }
            set
            {
                this.HavaleHesapIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OdemeSecenekId
        {
            get
            {
                return this.OdemeSecenekIdField;
            }
            set
            {
                this.OdemeSecenekIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OrderNumber
        {
            get
            {
                return this.OrderNumberField;
            }
            set
            {
                this.OrderNumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string RefNo
        {
            get
            {
                return this.RefNoField;
            }
            set
            {
                this.RefNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisId
        {
            get
            {
                return this.SiparisIdField;
            }
            set
            {
                this.SiparisIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisMailGonder
        {
            get
            {
                return this.SiparisMailGonderField;
            }
            set
            {
                this.SiparisMailGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisSmsGonder
        {
            get
            {
                return this.SiparisSmsGonderField;
            }
            set
            {
                this.SiparisSmsGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="PushDashboard.SiparisServis.ISiparisServis")]
    public interface ISiparisServis
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparis", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparis[]> SelectSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisFiltre f, PushDashboard.SiparisServis.WebSiparisSayfalama s);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisOdeme", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisOdemeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisOdeme[]> SelectSiparisOdemeAsync(string UyeKodu, int siparisId, int odemeId, System.Nullable<bool> isAktarildi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectCariOdeme", ReplyAction="http://tempuri.org/ISiparisServis/SelectCariOdemeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisOdeme[]> SelectCariOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisOdemeFiltre filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisUrun", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisUrun[]> SelectSiparisUrunAsync(string UyeKodu, int siparisId, bool iptalEdilmisUrunler);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisKampanya", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisKampanyaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisKampanya[]> SelectSiparisKampanyaAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveSiparis", ReplyAction="http://tempuri.org/ISiparisServis/SaveSiparisResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisSaveResponse> SaveSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisSaveRequest siparis);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisAktarildi", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisAktarildiResponse")]
        System.Threading.Tasks.Task SetSiparisAktarildiAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisOdemeAktarildi", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisOdemeAktarildiResponse")]
        System.Threading.Tasks.Task SetSiparisOdemeAktarildiAsync(string UyeKodu, int siparisId, int odemeId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisAktarildiV2", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisAktarildiV2Response")]
        System.Threading.Tasks.Task<int> SetSiparisAktarildiV2Async(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisAktarildiIptal", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisAktarildiIptalResponse")]
        System.Threading.Tasks.Task SetSiparisAktarildiIptalAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisKargoyaVerildi", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisKargoyaVerildiResponse")]
        System.Threading.Tasks.Task SetSiparisKargoyaVerildiAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisTeslimEdildi", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisTeslimEdildiResponse")]
        System.Threading.Tasks.Task SetSiparisTeslimEdildiAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetFaturaNo", ReplyAction="http://tempuri.org/ISiparisServis/SetFaturaNoResponse")]
        System.Threading.Tasks.Task SetFaturaNoAsync(string UyeKodu, int SiparisID, string FaturaNo, System.Nullable<System.DateTime> FaturaTarihi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/UpdateSiparisOzelAlanlar", ReplyAction="http://tempuri.org/ISiparisServis/UpdateSiparisOzelAlanlarResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisOzelAlanlarResponse> UpdateSiparisOzelAlanlarAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisOzelAlanlarRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveKargoTakipNo", ReplyAction="http://tempuri.org/ISiparisServis/SaveKargoTakipNoResponse")]
        System.Threading.Tasks.Task<string> SaveKargoTakipNoAsync(string UyeKodu, int siparisId, string kargoKodu, string kargoTakipNo, string kargoTakipLink, string BarkodBilgisi, bool KargoTakipLinkGoster);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSepet", ReplyAction="http://tempuri.org/ISiparisServis/SelectSepetResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSepetResponse> SelectSepetAsync(string UyeKodu, int sepetId, int uyeId, System.Nullable<System.DateTime> BaslangicTarihi, System.Nullable<System.DateTime> BitisTarihi, int sayfaSayisi, string guidSepetId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/HediyeCekiUygula", ReplyAction="http://tempuri.org/ISiparisServis/HediyeCekiUygulaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiUygulaResponse> HediyeCekiUygulaAsync(string UyeKodu, PushDashboard.SiparisServis.WebHediyeCekiUygulaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/HediyeCekiOlustur", ReplyAction="http://tempuri.org/ISiparisServis/HediyeCekiOlusturResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiOlusturResponse> HediyeCekiOlusturAsync(string UyeKodu, PushDashboard.SiparisServis.WebHediyeCekiOlusturRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetHediyeCekiKullanildi", ReplyAction="http://tempuri.org/ISiparisServis/SetHediyeCekiKullanildiResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetHediyeCekiKullanildiResponse> SetHediyeCekiKullanildiAsync(string UyeKodu, PushDashboard.SiparisServis.SetHediyeCekiKullanildiRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectHediyeCeki", ReplyAction="http://tempuri.org/ISiparisServis/SelectHediyeCekiResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiResponse> SelectHediyeCekiAsync(string memberCode, string giftVoucherCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisDurum", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisDurumResponse> SetSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisDurumRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisUrunDurum", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisUrunDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisUrunDurumResponse> SetSiparisUrunDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisUrunDurumRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SiparisKargoTakipNoKontrol", ReplyAction="http://tempuri.org/ISiparisServis/SiparisKargoTakipNoKontrolResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisKargoTakipNoKontrolResponse> SiparisKargoTakipNoKontrolAsync(string UyeKodu, int siparisId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveSiparisKargoPaket", ReplyAction="http://tempuri.org/ISiparisServis/SaveSiparisKargoPaketResponse")]
        System.Threading.Tasks.Task<string> SaveSiparisKargoPaketAsync(string UyeKodu, int SiparisID, int SiparisUrunDurumID, int KargoEntegrasyonID, int[] Urunler, string KargoTakipNo, bool KontrolEtme, bool MailGonder, bool SmsGonder, string BarkodBilgisi, int PaketID, string KargoTakipLink, bool KargoTakipLinkGoster, string marketPlaceParams);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SiparisPaketleKargoyaGonder", ReplyAction="http://tempuri.org/ISiparisServis/SiparisPaketleKargoyaGonderResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisiKargoyaGonderResponse> SiparisPaketleKargoyaGonderAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisiKargoyaGonderRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisUrunDurumlari", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisUrunDurumlariResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisUrunDurumlari[]> SelectSiparisUrunDurumlariAsync(string UyeKodu, int SiparisUrunDurumID, int Islem, int Aktif);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectUrunIadeNedenleri", ReplyAction="http://tempuri.org/ISiparisServis/SelectUrunIadeNedenleriResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.UrunIdaneNedenleriResponse> SelectUrunIadeNedenleriAsync(string UyeKodu, int Aktif);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisKargoPaket", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisKargoPaketResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebKargoPaket[]> SelectSiparisKargoPaketAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisKargoPaketFiltre filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveSiparisKargoPaketKargoTakipNo", ReplyAction="http://tempuri.org/ISiparisServis/SaveSiparisKargoPaketKargoTakipNoResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveSiparisKargoPaketKargoTakipNoResponse> SaveSiparisKargoPaketKargoTakipNoAsync(string UyeKodu, int siparisId, int paketId, string kargoTakipNo, string kargoTakipLink, bool KargoTakipLinkGoster);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/GetKargoSecenek", ReplyAction="http://tempuri.org/ISiparisServis/GetKargoSecenekResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebKargoFirma[]> GetKargoSecenekAsync(string UyeKodu, PushDashboard.SiparisServis.GetKargoSecenekRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectWebSepet", ReplyAction="http://tempuri.org/ISiparisServis/SelectWebSepetResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSepetResponse> SelectWebSepetAsync(string UyeKodu, PushDashboard.SiparisServis.SelectWebSepetRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/CreateSepet", ReplyAction="http://tempuri.org/ISiparisServis/CreateSepetResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.ServisSepet> CreateSepetAsync(string UyeKodu, PushDashboard.SiparisServis.CreateSepetRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/UpdateSepet", ReplyAction="http://tempuri.org/ISiparisServis/UpdateSepetResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebUpdateSepetResponse> UpdateSepetAsync(string UyeKodu, PushDashboard.SiparisServis.WebUpdateSepetRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/GetSepet", ReplyAction="http://tempuri.org/ISiparisServis/GetSepetResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.ServisSepet> GetSepetAsync(string UyeKodu, PushDashboard.SiparisServis.GetSepetRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/GetOdemeTipleri", ReplyAction="http://tempuri.org/ISiparisServis/GetOdemeTipleriResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisOdemeTipleri[]> GetOdemeTipleriAsync(string UyeKodu);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSepetKampanya", ReplyAction="http://tempuri.org/ISiparisServis/SetSepetKampanyaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSepetKampanyaResponse> SetSepetKampanyaAsync(string UyeKodu, PushDashboard.SiparisServis.SetSepetKampanyaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/GetPaketlemeDurumlari", ReplyAction="http://tempuri.org/ISiparisServis/GetPaketlemeDurumlariResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLPaketlemeDurum[]> GetPaketlemeDurumlariAsync(string UyeKodu, int id);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/GetMagazalar", ReplyAction="http://tempuri.org/ISiparisServis/GetMagazalarResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebMagaza[]> GetMagazalarAsync(string UyeKodu, PushDashboard.SiparisServis.WebMagazaFiltre Filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveMagaza", ReplyAction="http://tempuri.org/ISiparisServis/SaveMagazaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSaveMagazaResponse> SaveMagazaAsync(string UyeKodu, PushDashboard.SiparisServis.WebMagaza[] magazaList, PushDashboard.SiparisServis.WebMagazaAyar ayar);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisPaketlemeDurum", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisPaketlemeDurumResponse")]
        System.Threading.Tasks.Task<string> SetSiparisPaketlemeDurumAsync(string UyeKodu, int SiparisId, int PaketlemeDurumId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisKargoFirmaId", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisKargoFirmaIdResponse")]
        System.Threading.Tasks.Task<int> SetSiparisKargoFirmaIdAsync(string UyeKodu, int siparisId, int kargoFirmaId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisOdemeDurum", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisOdemeDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisOdemeDurumResponse> SetSiparisOdemeDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisOdemeDurumRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectEFatura", ReplyAction="http://tempuri.org/ISiparisServis/SelectEFaturaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectEFaturaResponse> SelectEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.SelectEFaturaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveEFatura", ReplyAction="http://tempuri.org/ISiparisServis/SaveEFaturaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveEFaturaResponse> SaveEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.SaveEFaturaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetEFaturaIptal", ReplyAction="http://tempuri.org/ISiparisServis/SetEFaturaIptalResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.EFaturaIptalResponse> SetEFaturaIptalAsync(string UyeKodu, PushDashboard.SiparisServis.EFaturaIptalRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectIadeOdeme", ReplyAction="http://tempuri.org/ISiparisServis/SelectIadeOdemeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectIadeOdemeResponse> SelectIadeOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.SelectIadeOdemeFiltre filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveBekleyenArama", ReplyAction="http://tempuri.org/ISiparisServis/SaveBekleyenAramaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveBekleyenAramaResponse> SaveBekleyenAramaAsync(string UyeKodu, PushDashboard.SiparisServis.SaveBekleyenAramaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectTelefonlaSiparis", ReplyAction="http://tempuri.org/ISiparisServis/SelectTelefonlaSiparisResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLTelefonSiparis[]> SelectTelefonlaSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebTelefonSiparisFiltre filtre, PushDashboard.SiparisServis.WebTelefonSiparisSayfalama sayfalama);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/UpdateTelefonlaSiparisDurum", ReplyAction="http://tempuri.org/ISiparisServis/UpdateTelefonlaSiparisDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateTelefonlaSiparisDurumResponse> UpdateTelefonlaSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateTelefonlaSiparisDurumRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisDurumlari", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisDurumlariResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumlariResponse> SelectSiparisDurumlariAsync(string UyeKodu);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectOdemeTipleri", ReplyAction="http://tempuri.org/ISiparisServis/SelectOdemeTipleriResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectOdemeTipleriResponse> SelectOdemeTipleriAsync(string UyeKodu);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetReferansNo", ReplyAction="http://tempuri.org/ISiparisServis/SetReferansNoResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetReferansNoResponse> SetReferansNoAsync(string UyeKodu, int SiparisID, string ReferansNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisDurumListe", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisDurumListeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisDurumListeResponse> SetSiparisDurumListeAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisDurumRequest[] request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisDurum", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumResponse> SelectSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SelectSiparisDurumRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectSiparisDurumLog", ReplyAction="http://tempuri.org/ISiparisServis/SelectSiparisDurumLogResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumLogListResponse> SelectSiparisDurumLogAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisDurumLogFiltre filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/UpdateEFatura", ReplyAction="http://tempuri.org/ISiparisServis/UpdateEFaturaResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateEfaturaResponse> UpdateEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateEfaturaRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveIadeOdeme", ReplyAction="http://tempuri.org/ISiparisServis/SaveIadeOdemeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.IadeOdemeResponse> SaveIadeOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.IadeOdemeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SiparisAnonimlestir", ReplyAction="http://tempuri.org/ISiparisServis/SiparisAnonimlestirResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisAnonimlestirResponse> SiparisAnonimlestirAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisAnonimlestirRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/UpdateMarketplaceAreas", ReplyAction="http://tempuri.org/ISiparisServis/UpdateMarketplaceAreasResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateMarketPlaceAreasResponse> UpdateMarketplaceAreasAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateMarketPlaceAreasRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SetSiparisFaturaUrl", ReplyAction="http://tempuri.org/ISiparisServis/SetSiparisFaturaUrlResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisFaturaUrlResponse> SetSiparisFaturaUrlAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisFaturaUrlRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/MarketplaceParamsIslem", ReplyAction="http://tempuri.org/ISiparisServis/MarketplaceParamsIslemResponse")]
        System.Threading.Tasks.Task<string> MarketplaceParamsIslemAsync(string UyeKodu, PushDashboard.SiparisServis.MarketPlaceParamsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SelectHataliOdemeler", ReplyAction="http://tempuri.org/ISiparisServis/SelectHataliOdemelerResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLHataliOdeme[]> SelectHataliOdemelerAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisSayfalama si);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SiparisUrunIptalIade", ReplyAction="http://tempuri.org/ISiparisServis/SiparisUrunIptalIadeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisUrunIptalIadeResponse> SiparisUrunIptalIadeAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisUrunIptalIadeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/OdemeOnProvizyonKapat", ReplyAction="http://tempuri.org/ISiparisServis/OdemeOnProvizyonKapatResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.OdemeOnProvizyonKapatResponse> OdemeOnProvizyonKapatAsync(string UyeKodu, PushDashboard.SiparisServis.OdemeOnProvizyonKapatRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISiparisServis/SaveSiparisOdeme", ReplyAction="http://tempuri.org/ISiparisServis/SaveSiparisOdemeResponse")]
        System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveSiparisOdemeResponse> SaveSiparisOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.SaveSiparisOdemeRequest request);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ISiparisServisChannel : PushDashboard.SiparisServis.ISiparisServis, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class SiparisServisClient : System.ServiceModel.ClientBase<PushDashboard.SiparisServis.ISiparisServis>, PushDashboard.SiparisServis.ISiparisServis
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public SiparisServisClient() : 
                base(SiparisServisClient.GetDefaultBinding(), SiparisServisClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_ISiparisServis.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SiparisServisClient(EndpointConfiguration endpointConfiguration) : 
                base(SiparisServisClient.GetBindingForEndpoint(endpointConfiguration), SiparisServisClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SiparisServisClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(SiparisServisClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SiparisServisClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(SiparisServisClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public SiparisServisClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparis[]> SelectSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisFiltre f, PushDashboard.SiparisServis.WebSiparisSayfalama s)
        {
            return base.Channel.SelectSiparisAsync(UyeKodu, f, s);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisOdeme[]> SelectSiparisOdemeAsync(string UyeKodu, int siparisId, int odemeId, System.Nullable<bool> isAktarildi)
        {
            return base.Channel.SelectSiparisOdemeAsync(UyeKodu, siparisId, odemeId, isAktarildi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisOdeme[]> SelectCariOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisOdemeFiltre filtre)
        {
            return base.Channel.SelectCariOdemeAsync(UyeKodu, filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisUrun[]> SelectSiparisUrunAsync(string UyeKodu, int siparisId, bool iptalEdilmisUrunler)
        {
            return base.Channel.SelectSiparisUrunAsync(UyeKodu, siparisId, iptalEdilmisUrunler);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisKampanya[]> SelectSiparisKampanyaAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SelectSiparisKampanyaAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSiparisSaveResponse> SaveSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisSaveRequest siparis)
        {
            return base.Channel.SaveSiparisAsync(UyeKodu, siparis);
        }
        
        public System.Threading.Tasks.Task SetSiparisAktarildiAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SetSiparisAktarildiAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task SetSiparisOdemeAktarildiAsync(string UyeKodu, int siparisId, int odemeId)
        {
            return base.Channel.SetSiparisOdemeAktarildiAsync(UyeKodu, siparisId, odemeId);
        }
        
        public System.Threading.Tasks.Task<int> SetSiparisAktarildiV2Async(string UyeKodu, int siparisId)
        {
            return base.Channel.SetSiparisAktarildiV2Async(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task SetSiparisAktarildiIptalAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SetSiparisAktarildiIptalAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task SetSiparisKargoyaVerildiAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SetSiparisKargoyaVerildiAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task SetSiparisTeslimEdildiAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SetSiparisTeslimEdildiAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task SetFaturaNoAsync(string UyeKodu, int SiparisID, string FaturaNo, System.Nullable<System.DateTime> FaturaTarihi)
        {
            return base.Channel.SetFaturaNoAsync(UyeKodu, SiparisID, FaturaNo, FaturaTarihi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisOzelAlanlarResponse> UpdateSiparisOzelAlanlarAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisOzelAlanlarRequest request)
        {
            return base.Channel.UpdateSiparisOzelAlanlarAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<string> SaveKargoTakipNoAsync(string UyeKodu, int siparisId, string kargoKodu, string kargoTakipNo, string kargoTakipLink, string BarkodBilgisi, bool KargoTakipLinkGoster)
        {
            return base.Channel.SaveKargoTakipNoAsync(UyeKodu, siparisId, kargoKodu, kargoTakipNo, kargoTakipLink, BarkodBilgisi, KargoTakipLinkGoster);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSepetResponse> SelectSepetAsync(string UyeKodu, int sepetId, int uyeId, System.Nullable<System.DateTime> BaslangicTarihi, System.Nullable<System.DateTime> BitisTarihi, int sayfaSayisi, string guidSepetId)
        {
            return base.Channel.SelectSepetAsync(UyeKodu, sepetId, uyeId, BaslangicTarihi, BitisTarihi, sayfaSayisi, guidSepetId);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiUygulaResponse> HediyeCekiUygulaAsync(string UyeKodu, PushDashboard.SiparisServis.WebHediyeCekiUygulaRequest request)
        {
            return base.Channel.HediyeCekiUygulaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiOlusturResponse> HediyeCekiOlusturAsync(string UyeKodu, PushDashboard.SiparisServis.WebHediyeCekiOlusturRequest request)
        {
            return base.Channel.HediyeCekiOlusturAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetHediyeCekiKullanildiResponse> SetHediyeCekiKullanildiAsync(string UyeKodu, PushDashboard.SiparisServis.SetHediyeCekiKullanildiRequest request)
        {
            return base.Channel.SetHediyeCekiKullanildiAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebHediyeCekiResponse> SelectHediyeCekiAsync(string memberCode, string giftVoucherCode)
        {
            return base.Channel.SelectHediyeCekiAsync(memberCode, giftVoucherCode);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisDurumResponse> SetSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisDurumRequest request)
        {
            return base.Channel.SetSiparisDurumAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisUrunDurumResponse> SetSiparisUrunDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisUrunDurumRequest request)
        {
            return base.Channel.SetSiparisUrunDurumAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisKargoTakipNoKontrolResponse> SiparisKargoTakipNoKontrolAsync(string UyeKodu, int siparisId)
        {
            return base.Channel.SiparisKargoTakipNoKontrolAsync(UyeKodu, siparisId);
        }
        
        public System.Threading.Tasks.Task<string> SaveSiparisKargoPaketAsync(string UyeKodu, int SiparisID, int SiparisUrunDurumID, int KargoEntegrasyonID, int[] Urunler, string KargoTakipNo, bool KontrolEtme, bool MailGonder, bool SmsGonder, string BarkodBilgisi, int PaketID, string KargoTakipLink, bool KargoTakipLinkGoster, string marketPlaceParams)
        {
            return base.Channel.SaveSiparisKargoPaketAsync(UyeKodu, SiparisID, SiparisUrunDurumID, KargoEntegrasyonID, Urunler, KargoTakipNo, KontrolEtme, MailGonder, SmsGonder, BarkodBilgisi, PaketID, KargoTakipLink, KargoTakipLinkGoster, marketPlaceParams);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisiKargoyaGonderResponse> SiparisPaketleKargoyaGonderAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisiKargoyaGonderRequest request)
        {
            return base.Channel.SiparisPaketleKargoyaGonderAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisUrunDurumlari[]> SelectSiparisUrunDurumlariAsync(string UyeKodu, int SiparisUrunDurumID, int Islem, int Aktif)
        {
            return base.Channel.SelectSiparisUrunDurumlariAsync(UyeKodu, SiparisUrunDurumID, Islem, Aktif);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.UrunIdaneNedenleriResponse> SelectUrunIadeNedenleriAsync(string UyeKodu, int Aktif)
        {
            return base.Channel.SelectUrunIadeNedenleriAsync(UyeKodu, Aktif);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebKargoPaket[]> SelectSiparisKargoPaketAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisKargoPaketFiltre filtre)
        {
            return base.Channel.SelectSiparisKargoPaketAsync(UyeKodu, filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveSiparisKargoPaketKargoTakipNoResponse> SaveSiparisKargoPaketKargoTakipNoAsync(string UyeKodu, int siparisId, int paketId, string kargoTakipNo, string kargoTakipLink, bool KargoTakipLinkGoster)
        {
            return base.Channel.SaveSiparisKargoPaketKargoTakipNoAsync(UyeKodu, siparisId, paketId, kargoTakipNo, kargoTakipLink, KargoTakipLinkGoster);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebKargoFirma[]> GetKargoSecenekAsync(string UyeKodu, PushDashboard.SiparisServis.GetKargoSecenekRequest request)
        {
            return base.Channel.GetKargoSecenekAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSepetResponse> SelectWebSepetAsync(string UyeKodu, PushDashboard.SiparisServis.SelectWebSepetRequest request)
        {
            return base.Channel.SelectWebSepetAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.ServisSepet> CreateSepetAsync(string UyeKodu, PushDashboard.SiparisServis.CreateSepetRequest request)
        {
            return base.Channel.CreateSepetAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebUpdateSepetResponse> UpdateSepetAsync(string UyeKodu, PushDashboard.SiparisServis.WebUpdateSepetRequest request)
        {
            return base.Channel.UpdateSepetAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.ServisSepet> GetSepetAsync(string UyeKodu, PushDashboard.SiparisServis.GetSepetRequest request)
        {
            return base.Channel.GetSepetAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisOdemeTipleri[]> GetOdemeTipleriAsync(string UyeKodu)
        {
            return base.Channel.GetOdemeTipleriAsync(UyeKodu);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSepetKampanyaResponse> SetSepetKampanyaAsync(string UyeKodu, PushDashboard.SiparisServis.SetSepetKampanyaRequest request)
        {
            return base.Channel.SetSepetKampanyaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLPaketlemeDurum[]> GetPaketlemeDurumlariAsync(string UyeKodu, int id)
        {
            return base.Channel.GetPaketlemeDurumlariAsync(UyeKodu, id);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebMagaza[]> GetMagazalarAsync(string UyeKodu, PushDashboard.SiparisServis.WebMagazaFiltre Filtre)
        {
            return base.Channel.GetMagazalarAsync(UyeKodu, Filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.WebSaveMagazaResponse> SaveMagazaAsync(string UyeKodu, PushDashboard.SiparisServis.WebMagaza[] magazaList, PushDashboard.SiparisServis.WebMagazaAyar ayar)
        {
            return base.Channel.SaveMagazaAsync(UyeKodu, magazaList, ayar);
        }
        
        public System.Threading.Tasks.Task<string> SetSiparisPaketlemeDurumAsync(string UyeKodu, int SiparisId, int PaketlemeDurumId)
        {
            return base.Channel.SetSiparisPaketlemeDurumAsync(UyeKodu, SiparisId, PaketlemeDurumId);
        }
        
        public System.Threading.Tasks.Task<int> SetSiparisKargoFirmaIdAsync(string UyeKodu, int siparisId, int kargoFirmaId)
        {
            return base.Channel.SetSiparisKargoFirmaIdAsync(UyeKodu, siparisId, kargoFirmaId);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisOdemeDurumResponse> SetSiparisOdemeDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisOdemeDurumRequest request)
        {
            return base.Channel.SetSiparisOdemeDurumAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectEFaturaResponse> SelectEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.SelectEFaturaRequest request)
        {
            return base.Channel.SelectEFaturaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveEFaturaResponse> SaveEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.SaveEFaturaRequest request)
        {
            return base.Channel.SaveEFaturaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.EFaturaIptalResponse> SetEFaturaIptalAsync(string UyeKodu, PushDashboard.SiparisServis.EFaturaIptalRequest request)
        {
            return base.Channel.SetEFaturaIptalAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectIadeOdemeResponse> SelectIadeOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.SelectIadeOdemeFiltre filtre)
        {
            return base.Channel.SelectIadeOdemeAsync(UyeKodu, filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveBekleyenAramaResponse> SaveBekleyenAramaAsync(string UyeKodu, PushDashboard.SiparisServis.SaveBekleyenAramaRequest request)
        {
            return base.Channel.SaveBekleyenAramaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLTelefonSiparis[]> SelectTelefonlaSiparisAsync(string UyeKodu, PushDashboard.SiparisServis.WebTelefonSiparisFiltre filtre, PushDashboard.SiparisServis.WebTelefonSiparisSayfalama sayfalama)
        {
            return base.Channel.SelectTelefonlaSiparisAsync(UyeKodu, filtre, sayfalama);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateTelefonlaSiparisDurumResponse> UpdateTelefonlaSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateTelefonlaSiparisDurumRequest request)
        {
            return base.Channel.UpdateTelefonlaSiparisDurumAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumlariResponse> SelectSiparisDurumlariAsync(string UyeKodu)
        {
            return base.Channel.SelectSiparisDurumlariAsync(UyeKodu);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectOdemeTipleriResponse> SelectOdemeTipleriAsync(string UyeKodu)
        {
            return base.Channel.SelectOdemeTipleriAsync(UyeKodu);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetReferansNoResponse> SetReferansNoAsync(string UyeKodu, int SiparisID, string ReferansNo)
        {
            return base.Channel.SetReferansNoAsync(UyeKodu, SiparisID, ReferansNo);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisDurumListeResponse> SetSiparisDurumListeAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisDurumRequest[] request)
        {
            return base.Channel.SetSiparisDurumListeAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumResponse> SelectSiparisDurumAsync(string UyeKodu, PushDashboard.SiparisServis.SelectSiparisDurumRequest request)
        {
            return base.Channel.SelectSiparisDurumAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SelectSiparisDurumLogListResponse> SelectSiparisDurumLogAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisDurumLogFiltre filtre)
        {
            return base.Channel.SelectSiparisDurumLogAsync(UyeKodu, filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateEfaturaResponse> UpdateEFaturaAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateEfaturaRequest request)
        {
            return base.Channel.UpdateEFaturaAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.IadeOdemeResponse> SaveIadeOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.IadeOdemeRequest request)
        {
            return base.Channel.SaveIadeOdemeAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisAnonimlestirResponse> SiparisAnonimlestirAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisAnonimlestirRequest request)
        {
            return base.Channel.SiparisAnonimlestirAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.UpdateMarketPlaceAreasResponse> UpdateMarketplaceAreasAsync(string UyeKodu, PushDashboard.SiparisServis.UpdateMarketPlaceAreasRequest request)
        {
            return base.Channel.UpdateMarketplaceAreasAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SetSiparisFaturaUrlResponse> SetSiparisFaturaUrlAsync(string UyeKodu, PushDashboard.SiparisServis.SetSiparisFaturaUrlRequest request)
        {
            return base.Channel.SetSiparisFaturaUrlAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<string> MarketplaceParamsIslemAsync(string UyeKodu, PushDashboard.SiparisServis.MarketPlaceParamsRequest request)
        {
            return base.Channel.MarketplaceParamsIslemAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.BLHataliOdeme[]> SelectHataliOdemelerAsync(string UyeKodu, PushDashboard.SiparisServis.WebSiparisSayfalama si)
        {
            return base.Channel.SelectHataliOdemelerAsync(UyeKodu, si);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SiparisUrunIptalIadeResponse> SiparisUrunIptalIadeAsync(string UyeKodu, PushDashboard.SiparisServis.SiparisUrunIptalIadeRequest request)
        {
            return base.Channel.SiparisUrunIptalIadeAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.OdemeOnProvizyonKapatResponse> OdemeOnProvizyonKapatAsync(string UyeKodu, PushDashboard.SiparisServis.OdemeOnProvizyonKapatRequest request)
        {
            return base.Channel.OdemeOnProvizyonKapatAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.SiparisServis.SaveSiparisOdemeResponse> SaveSiparisOdemeAsync(string UyeKodu, PushDashboard.SiparisServis.SaveSiparisOdemeRequest request)
        {
            return base.Channel.SaveSiparisOdemeAsync(UyeKodu, request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ISiparisServis))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ISiparisServis))
            {
                return new System.ServiceModel.EndpointAddress("http://perlucia.ticimaxtest.com/Servis/SiparisServis.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return SiparisServisClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_ISiparisServis);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return SiparisServisClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_ISiparisServis);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_ISiparisServis,
        }
    }
}
