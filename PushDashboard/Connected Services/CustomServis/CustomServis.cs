//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PushDashboard.CustomServis
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Entegrasyon", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class Entegrasyon : object
    {
        
        private string AlanDegerField;
        
        private string DegerField;
        
        private System.DateTime EklemeTarihiField;
        
        private string EntegrasyonKoduField;
        
        private string TabloAlanField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AlanDeger
        {
            get
            {
                return this.AlanDegerField;
            }
            set
            {
                this.AlanDegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Deger
        {
            get
            {
                return this.DegerField;
            }
            set
            {
                this.DegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EntegrasyonKodu
        {
            get
            {
                return this.EntegrasyonKoduField;
            }
            set
            {
                this.EntegrasyonKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TabloAlan
        {
            get
            {
                return this.TabloAlanField;
            }
            set
            {
                this.TabloAlanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebServisResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.GetMenuResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SaveMenuResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.DeleteMenuResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectBankaResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectSilinenUyelerResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.GetMailListResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SaveMailListResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SaveWebhookResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectWebhookResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.DeleteWebhookResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectIadeOdemeListesiResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.DeleteMailListResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectOdemeBildirimiResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SaveOdemeBildirimDurumResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.SelectMarketPlaceBilgiRespone))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.GetDinamikFormDataResponse))]
    public partial class WebServisResponse : object
    {
        
        private int ErrorCodeField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMenuResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetMenuResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.WebMenu[] MenulerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebMenu[] Menuler
        {
            get
            {
                return this.MenulerField;
            }
            set
            {
                this.MenulerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveMenuResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveMenuResponse : PushDashboard.CustomServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeleteMenuResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DeleteMenuResponse : PushDashboard.CustomServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectBankaResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectBankaResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.WebBanka[] BankaListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebBanka[] BankaList
        {
            get
            {
                return this.BankaListField;
            }
            set
            {
                this.BankaListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSilinenUyelerResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSilinenUyelerResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.SilinenUye[] SilinenUyeListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.SilinenUye[] SilinenUyeList
        {
            get
            {
                return this.SilinenUyeListField;
            }
            set
            {
                this.SilinenUyeListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMailListResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetMailListResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.GetMailListModel[] MailListesiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.GetMailListModel[] MailListesi
        {
            get
            {
                return this.MailListesiField;
            }
            set
            {
                this.MailListesiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveMailListResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveMailListResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private double IdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveWebhookResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveWebhookResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private int IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectWebhookResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectWebhookResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.Webhook[] WebhookListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.Webhook[] WebhookList
        {
            get
            {
                return this.WebhookListField;
            }
            set
            {
                this.WebhookListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeleteWebhookResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DeleteWebhookResponse : PushDashboard.CustomServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIadeOdemeListesiResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIadeOdemeListesiResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.WebIdadeOdeme[] IadeOdemeListesiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebIdadeOdeme[] IadeOdemeListesi
        {
            get
            {
                return this.IadeOdemeListesiField;
            }
            set
            {
                this.IadeOdemeListesiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeleteMailListResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DeleteMailListResponse : PushDashboard.CustomServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectOdemeBildirimiResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectOdemeBildirimiResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.OdemeBildirim[] OdemeBildirimiListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.OdemeBildirim[] OdemeBildirimiList
        {
            get
            {
                return this.OdemeBildirimiListField;
            }
            set
            {
                this.OdemeBildirimiListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveOdemeBildirimDurumResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveOdemeBildirimDurumResponse : PushDashboard.CustomServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectMarketPlaceBilgiRespone", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectMarketPlaceBilgiRespone : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.SiparisMarketplaceBilgi[] MarketPlaceBilgiListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.SiparisMarketplaceBilgi[] MarketPlaceBilgiList
        {
            get
            {
                return this.MarketPlaceBilgiListField;
            }
            set
            {
                this.MarketPlaceBilgiListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetDinamikFormDataResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetDinamikFormDataResponse : PushDashboard.CustomServis.WebServisResponse
    {
        
        private PushDashboard.CustomServis.DinamikFormData[] FormDataListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DinamikFormData[] FormDataList
        {
            get
            {
                return this.FormDataListField;
            }
            set
            {
                this.FormDataListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMenu", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebMenu : object
    {
        
        private bool AktifField;
        
        private string BaslikField;
        
        private System.Nullable<System.DateTime> BitisTarihiField;
        
        private int IDField;
        
        private string IcerikField;
        
        private int PIDField;
        
        private string ResimField;
        
        private string ResimTargetUrlField;
        
        private string ResimYoluField;
        
        private int SiraField;
        
        private string TargetField;
        
        private int TipField;
        
        private string UrlField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Baslik
        {
            get
            {
                return this.BaslikField;
            }
            set
            {
                this.BaslikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> BitisTarihi
        {
            get
            {
                return this.BitisTarihiField;
            }
            set
            {
                this.BitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Icerik
        {
            get
            {
                return this.IcerikField;
            }
            set
            {
                this.IcerikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PID
        {
            get
            {
                return this.PIDField;
            }
            set
            {
                this.PIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Resim
        {
            get
            {
                return this.ResimField;
            }
            set
            {
                this.ResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimTargetUrl
        {
            get
            {
                return this.ResimTargetUrlField;
            }
            set
            {
                this.ResimTargetUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimYolu
        {
            get
            {
                return this.ResimYoluField;
            }
            set
            {
                this.ResimYoluField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Sira
        {
            get
            {
                return this.SiraField;
            }
            set
            {
                this.SiraField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Target
        {
            get
            {
                return this.TargetField;
            }
            set
            {
                this.TargetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebBanka", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebBanka : object
    {
        
        private string BankaKartTuruField;
        
        private string BankaKoduField;
        
        private string BkmBankaKoduField;
        
        private string BkmBinField;
        
        private bool BkmVarsayilanField;
        
        private bool IadeAktifField;
        
        private int IdField;
        
        private int KartTipiField;
        
        private string KodField;
        
        private string KrediKartiField;
        
        private bool MailOrderVarField;
        
        private int ServisField;
        
        private int SiraField;
        
        private string TanimField;
        
        private bool TekCekimField;
        
        private bool is3DVarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BankaKartTuru
        {
            get
            {
                return this.BankaKartTuruField;
            }
            set
            {
                this.BankaKartTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BankaKodu
        {
            get
            {
                return this.BankaKoduField;
            }
            set
            {
                this.BankaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BkmBankaKodu
        {
            get
            {
                return this.BkmBankaKoduField;
            }
            set
            {
                this.BkmBankaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BkmBin
        {
            get
            {
                return this.BkmBinField;
            }
            set
            {
                this.BkmBinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BkmVarsayilan
        {
            get
            {
                return this.BkmVarsayilanField;
            }
            set
            {
                this.BkmVarsayilanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IadeAktif
        {
            get
            {
                return this.IadeAktifField;
            }
            set
            {
                this.IadeAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KartTipi
        {
            get
            {
                return this.KartTipiField;
            }
            set
            {
                this.KartTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kod
        {
            get
            {
                return this.KodField;
            }
            set
            {
                this.KodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KrediKarti
        {
            get
            {
                return this.KrediKartiField;
            }
            set
            {
                this.KrediKartiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailOrderVar
        {
            get
            {
                return this.MailOrderVarField;
            }
            set
            {
                this.MailOrderVarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Servis
        {
            get
            {
                return this.ServisField;
            }
            set
            {
                this.ServisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Sira
        {
            get
            {
                return this.SiraField;
            }
            set
            {
                this.SiraField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TekCekim
        {
            get
            {
                return this.TekCekimField;
            }
            set
            {
                this.TekCekimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool is3DVar
        {
            get
            {
                return this.is3DVarField;
            }
            set
            {
                this.is3DVarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SilinenUye", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SilinenUye : object
    {
        
        private int IdField;
        
        private string MusteriKoduField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MusteriKodu
        {
            get
            {
                return this.MusteriKoduField;
            }
            set
            {
                this.MusteriKoduField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMailListModel", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetMailListModel : object
    {
        
        private System.DateTime EklemeTarihiField;
        
        private int IDField;
        
        private string IpAdresiField;
        
        private string IsimField;
        
        private string MailField;
        
        private bool MailIzinField;
        
        private string MailKodField;
        
        private bool SmsIzinField;
        
        private string SoyisimField;
        
        private string TelefonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IpAdresi
        {
            get
            {
                return this.IpAdresiField;
            }
            set
            {
                this.IpAdresiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MailKod
        {
            get
            {
                return this.MailKodField;
            }
            set
            {
                this.MailKodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsIzin
        {
            get
            {
                return this.SmsIzinField;
            }
            set
            {
                this.SmsIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Soyisim
        {
            get
            {
                return this.SoyisimField;
            }
            set
            {
                this.SoyisimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Webhook", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class Webhook : object
    {
        
        private int IDField;
        
        private PushDashboard.CustomServis.WebhookIslem IslemTipiField;
        
        private string KullaniciAdiField;
        
        private string SifreField;
        
        private string UrlField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebhookIslem IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KullaniciAdi
        {
            get
            {
                return this.KullaniciAdiField;
            }
            set
            {
                this.KullaniciAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sifre
        {
            get
            {
                return this.SifreField;
            }
            set
            {
                this.SifreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebhookIslem", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebhookIslem : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisOlustu = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KapidaOdemeTelefonlaOnay = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeKayitOldu = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeBilgileriGuncellendi = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeAdresiEklendi = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeAdresiGuncellendi = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeAdresiSilindi = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeIptalTalebindeBulundu = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeFavoriUrunEkledi = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeFavoriUrunSildi = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeFiyatAlarmEkledi = 11,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeFiyatAlarmSildi = 12,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeDestekTalebiEkledi = 13,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeDestekTalebiCozuldu = 14,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeDestekTalebiCevaplandi = 15,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeHavaleBildirimiEkledi = 16,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CariOdemeEklendi = 17,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisKargoyaVerildi = 18,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UrunEklendi = 19,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UrunGuncellendi = 20,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisDurumuDegistirildi = 21,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UrunStokGuncellendi = 22,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SepeteUrunEklendi = 23,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SepettenUrunCikartildi = 24,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        VaryasyonEklendi = 25,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        VaryasyonGuncellendi = 26,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TopluUrunIslemiKartAktifGuncellendi = 27,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TopluUrunIslemiVaryasyonAktifGuncellendi = 28,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TopluUrunIslemiFiyatGuncellendi = 29,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TopluUrunIslemiStokGuncellendi = 30,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisUrunDurumuDegistirildi = 31,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisTeslimatAdresiDegistirildi = 32,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisFaturaAdresiDegistirildi = 33,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisIptalDurum = 34,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisIadeDurum = 35,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SiparisUrunIptalIadeDurum = 36,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIdadeOdeme", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIdadeOdeme : object
    {
        
        private string IBANField;
        
        private int IDField;
        
        private string IbanAdiSoyadiField;
        
        private string KullaniciAdiField;
        
        private int KullaniciIDField;
        
        private PushDashboard.CustomServis.WebIadeOdemeDurum OdemeDurumuField;
        
        private int OdemeTipiField;
        
        private int SiparisIDField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        private string UyeAdiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IBAN
        {
            get
            {
                return this.IBANField;
            }
            set
            {
                this.IBANField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IbanAdiSoyadi
        {
            get
            {
                return this.IbanAdiSoyadiField;
            }
            set
            {
                this.IbanAdiSoyadiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KullaniciAdi
        {
            get
            {
                return this.KullaniciAdiField;
            }
            set
            {
                this.KullaniciAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KullaniciID
        {
            get
            {
                return this.KullaniciIDField;
            }
            set
            {
                this.KullaniciIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebIadeOdemeDurum OdemeDurumu
        {
            get
            {
                return this.OdemeDurumuField;
            }
            set
            {
                this.OdemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeOdemeDurum", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebIadeOdemeDurum : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Tumu = -1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Odenmedi = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Odendi = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="OdemeBildirim", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class OdemeBildirim : object
    {
        
        private string BankaField;
        
        private System.Collections.Generic.Dictionary<int, string> DurumlarField;
        
        private int DurumuField;
        
        private int IDField;
        
        private string NotlarField;
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        private System.DateTime TarihField;
        
        private double TutarField;
        
        private string UyeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Banka
        {
            get
            {
                return this.BankaField;
            }
            set
            {
                this.BankaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.Dictionary<int, string> Durumlar
        {
            get
            {
                return this.DurumlarField;
            }
            set
            {
                this.DurumlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Durumu
        {
            get
            {
                return this.DurumuField;
            }
            set
            {
                this.DurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notlar
        {
            get
            {
                return this.NotlarField;
            }
            set
            {
                this.NotlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Uye
        {
            get
            {
                return this.UyeField;
            }
            set
            {
                this.UyeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SiparisMarketplaceBilgi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SiparisMarketplaceBilgi : object
    {
        
        private string DigerFaturaNoField;
        
        private string IadeKomisyonFaturaNoField;
        
        private double KargoKatkiPayiField;
        
        private string KargoKatkiPayiFaturaNoField;
        
        private string KomisyonFaturaNoField;
        
        private bool MarketPlaceOdemeAlindiField;
        
        private System.Nullable<System.DateTime> OdemeVadeTarihiField;
        
        private double PazaryeriKomisyonTutariField;
        
        private double PazaryeriOdemeTutariField;
        
        private int SiparisDurumField;
        
        private int SiparisIDField;
        
        private string SiparisKaynagiField;
        
        private string SiparisNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DigerFaturaNo
        {
            get
            {
                return this.DigerFaturaNoField;
            }
            set
            {
                this.DigerFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IadeKomisyonFaturaNo
        {
            get
            {
                return this.IadeKomisyonFaturaNoField;
            }
            set
            {
                this.IadeKomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KargoKatkiPayi
        {
            get
            {
                return this.KargoKatkiPayiField;
            }
            set
            {
                this.KargoKatkiPayiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoKatkiPayiFaturaNo
        {
            get
            {
                return this.KargoKatkiPayiFaturaNoField;
            }
            set
            {
                this.KargoKatkiPayiFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KomisyonFaturaNo
        {
            get
            {
                return this.KomisyonFaturaNoField;
            }
            set
            {
                this.KomisyonFaturaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MarketPlaceOdemeAlindi
        {
            get
            {
                return this.MarketPlaceOdemeAlindiField;
            }
            set
            {
                this.MarketPlaceOdemeAlindiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OdemeVadeTarihi
        {
            get
            {
                return this.OdemeVadeTarihiField;
            }
            set
            {
                this.OdemeVadeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriKomisyonTutari
        {
            get
            {
                return this.PazaryeriKomisyonTutariField;
            }
            set
            {
                this.PazaryeriKomisyonTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double PazaryeriOdemeTutari
        {
            get
            {
                return this.PazaryeriOdemeTutariField;
            }
            set
            {
                this.PazaryeriOdemeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisDurum
        {
            get
            {
                return this.SiparisDurumField;
            }
            set
            {
                this.SiparisDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DinamikFormData", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DinamikFormData : object
    {
        
        private PushDashboard.CustomServis.FormDataModel[] DataListField;
        
        private System.DateTime EklenmeTarihiField;
        
        private int FormIdField;
        
        private int IDField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.FormDataModel[] DataList
        {
            get
            {
                return this.DataListField;
            }
            set
            {
                this.DataListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklenmeTarihi
        {
            get
            {
                return this.EklenmeTarihiField;
            }
            set
            {
                this.EklenmeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FormId
        {
            get
            {
                return this.FormIdField;
            }
            set
            {
                this.FormIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FormDataModel", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class FormDataModel : object
    {
        
        private string AciklamaField;
        
        private string DegerField;
        
        private int SiraField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Aciklama
        {
            get
            {
                return this.AciklamaField;
            }
            set
            {
                this.AciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Deger
        {
            get
            {
                return this.DegerField;
            }
            set
            {
                this.DegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Sira
        {
            get
            {
                return this.SiraField;
            }
            set
            {
                this.SiraField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MailList", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class MailList : object
    {
        
        private System.Nullable<System.DateTime> EklemeTarihiField;
        
        private int IDField;
        
        private string IsimField;
        
        private string MailField;
        
        private bool MailIzinField;
        
        private bool SmsIzınField;
        
        private string SoyisimField;
        
        private string TelefonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsIzın
        {
            get
            {
                return this.SmsIzınField;
            }
            set
            {
                this.SmsIzınField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Soyisim
        {
            get
            {
                return this.SoyisimField;
            }
            set
            {
                this.SoyisimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoDesiFiyatSelectRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoDesiFiyatSelectRequest : object
    {
        
        private int FiltreKargoIDField;
        
        private int FiltreSehirIDField;
        
        private int FiltreUlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreKargoID
        {
            get
            {
                return this.FiltreKargoIDField;
            }
            set
            {
                this.FiltreKargoIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreSehirID
        {
            get
            {
                return this.FiltreSehirIDField;
            }
            set
            {
                this.FiltreSehirIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreUlkeID
        {
            get
            {
                return this.FiltreUlkeIDField;
            }
            set
            {
                this.FiltreUlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoDesiFiyatSelectResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoDesiFiyatSelectResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.KargoDesiFiyat[] KargoDesiFiyatListesiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.KargoDesiFiyat[] KargoDesiFiyatListesi
        {
            get
            {
                return this.KargoDesiFiyatListesiField;
            }
            set
            {
                this.KargoDesiFiyatListesiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoDesiFiyat", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoDesiFiyat : object
    {
        
        private double DesiBasField;
        
        private double DesiSonField;
        
        private double FiyatField;
        
        private int IDField;
        
        private int KargoIDField;
        
        private string SehirAdiField;
        
        private int SehirIDField;
        
        private bool SehirIciField;
        
        private string UlkeAdiField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double DesiBas
        {
            get
            {
                return this.DesiBasField;
            }
            set
            {
                this.DesiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double DesiSon
        {
            get
            {
                return this.DesiSonField;
            }
            set
            {
                this.DesiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Fiyat
        {
            get
            {
                return this.FiyatField;
            }
            set
            {
                this.FiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoID
        {
            get
            {
                return this.KargoIDField;
            }
            set
            {
                this.KargoIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SehirAdi
        {
            get
            {
                return this.SehirAdiField;
            }
            set
            {
                this.SehirAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SehirID
        {
            get
            {
                return this.SehirIDField;
            }
            set
            {
                this.SehirIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SehirIci
        {
            get
            {
                return this.SehirIciField;
            }
            set
            {
                this.SehirIciField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UlkeAdi
        {
            get
            {
                return this.UlkeAdiField;
            }
            set
            {
                this.UlkeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoDesiFiyatGuncelleRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoDesiFiyatGuncelleRequest : object
    {
        
        private PushDashboard.CustomServis.KargoDesiFiyatBilgisi KargoDesiFiyatBilgisiField;
        
        private int KargoKapsamiField;
        
        private bool SadeceSehirIcineGonderimField;
        
        private bool UlkeBelirleField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.KargoDesiFiyatBilgisi KargoDesiFiyatBilgisi
        {
            get
            {
                return this.KargoDesiFiyatBilgisiField;
            }
            set
            {
                this.KargoDesiFiyatBilgisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoKapsami
        {
            get
            {
                return this.KargoKapsamiField;
            }
            set
            {
                this.KargoKapsamiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SadeceSehirIcineGonderim
        {
            get
            {
                return this.SadeceSehirIcineGonderimField;
            }
            set
            {
                this.SadeceSehirIcineGonderimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UlkeBelirle
        {
            get
            {
                return this.UlkeBelirleField;
            }
            set
            {
                this.UlkeBelirleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoDesiFiyatBilgisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoDesiFiyatBilgisi : object
    {
        
        private double DesiBasField;
        
        private double DesiSonField;
        
        private double FiyatField;
        
        private int IDField;
        
        private int KargoIDField;
        
        private int SehirIDField;
        
        private bool SehirIciField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double DesiBas
        {
            get
            {
                return this.DesiBasField;
            }
            set
            {
                this.DesiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double DesiSon
        {
            get
            {
                return this.DesiSonField;
            }
            set
            {
                this.DesiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Fiyat
        {
            get
            {
                return this.FiyatField;
            }
            set
            {
                this.FiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoID
        {
            get
            {
                return this.KargoIDField;
            }
            set
            {
                this.KargoIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SehirID
        {
            get
            {
                return this.SehirIDField;
            }
            set
            {
                this.SehirIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SehirIci
        {
            get
            {
                return this.SehirIciField;
            }
            set
            {
                this.SehirIciField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectUlkeRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectUlkeRequest : object
    {
        
        private int FiltreUlkeIDField;
        
        private string FiltreUlkeKoduField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreUlkeID
        {
            get
            {
                return this.FiltreUlkeIDField;
            }
            set
            {
                this.FiltreUlkeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FiltreUlkeKodu
        {
            get
            {
                return this.FiltreUlkeKoduField;
            }
            set
            {
                this.FiltreUlkeKoduField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoUlke", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoUlke : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIlRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIlRequest : object
    {
        
        private int FiltreIlIDField;
        
        private int FiltreUlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreIlID
        {
            get
            {
                return this.FiltreIlIDField;
            }
            set
            {
                this.FiltreIlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreUlkeID
        {
            get
            {
                return this.FiltreUlkeIDField;
            }
            set
            {
                this.FiltreUlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoIl", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoIl : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        private int UlkeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UlkeID
        {
            get
            {
                return this.UlkeIDField;
            }
            set
            {
                this.UlkeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIlceRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIlceRequest : object
    {
        
        private int FiltreIlIDField;
        
        private int FiltreIlceIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreIlID
        {
            get
            {
                return this.FiltreIlIDField;
            }
            set
            {
                this.FiltreIlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiltreIlceID
        {
            get
            {
                return this.FiltreIlceIDField;
            }
            set
            {
                this.FiltreIlceIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoIlce", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoIlce : object
    {
        
        private int IDField;
        
        private int ILIDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ILID
        {
            get
            {
                return this.ILIDField;
            }
            set
            {
                this.ILIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="KargoFirma", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class KargoFirma : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepSelectRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepSelectRequest : object
    {
        
        private PushDashboard.CustomServis.IadeTalepFiltre FiltreField;
        
        private PushDashboard.CustomServis.WebServisSayfalama SayfalamaField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalepFiltre Filtre
        {
            get
            {
                return this.FiltreField;
            }
            set
            {
                this.FiltreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebServisSayfalama Sayfalama
        {
            get
            {
                return this.SayfalamaField;
            }
            set
            {
                this.SayfalamaField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeTalepFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeTalepFiltre : object
    {
        
        private int DurumIDField;
        
        private System.Nullable<System.DateTime> EklemeTarihi1Field;
        
        private System.Nullable<System.DateTime> EklemeTarihi2Field;
        
        private int IDField;
        
        private int ParaIadeTipiField;
        
        private bool PazaryeriAktarildiField;
        
        private int SiparisIDField;
        
        private string SiparisKaynagiField;
        
        private bool UrunGetirField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi1
        {
            get
            {
                return this.EklemeTarihi1Field;
            }
            set
            {
                this.EklemeTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi2
        {
            get
            {
                return this.EklemeTarihi2Field;
            }
            set
            {
                this.EklemeTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ParaIadeTipi
        {
            get
            {
                return this.ParaIadeTipiField;
            }
            set
            {
                this.ParaIadeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PazaryeriAktarildi
        {
            get
            {
                return this.PazaryeriAktarildiField;
            }
            set
            {
                this.PazaryeriAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UrunGetir
        {
            get
            {
                return this.UrunGetirField;
            }
            set
            {
                this.UrunGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebServisSayfalama", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebServisSayfalama : object
    {
        
        private int KayitSayisiField;
        
        private int SayfaNoField;
        
        private string SiralamaDegeriField;
        
        private string SiralamaYonuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KayitSayisi
        {
            get
            {
                return this.KayitSayisiField;
            }
            set
            {
                this.KayitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SayfaNo
        {
            get
            {
                return this.SayfaNoField;
            }
            set
            {
                this.SayfaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaDegeri
        {
            get
            {
                return this.SiralamaDegeriField;
            }
            set
            {
                this.SiralamaDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaYonu
        {
            get
            {
                return this.SiralamaYonuField;
            }
            set
            {
                this.SiralamaYonuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepResponse : object
    {
        
        private string ErrorMessageField;
        
        private PushDashboard.CustomServis.IadeTalep[] IadeTalepListField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalep[] IadeTalepList
        {
            get
            {
                return this.IadeTalepListField;
            }
            set
            {
                this.IadeTalepListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeTalep", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeTalep : object
    {
        
        private double BekleyenUrunAdediField;
        
        private string CevapField;
        
        private System.DateTime CevapTarihiField;
        
        private string CevaplayanField;
        
        private int CevaplayanIDField;
        
        private System.DateTime EklemeTarihiField;
        
        private int IDField;
        
        private string IbanField;
        
        private string IbanIsimSoyisimField;
        
        private string KargoKoduField;
        
        private string MagazaAdiField;
        
        private int MagazaIDField;
        
        private string MagazaIadeKoduField;
        
        private string NotlarField;
        
        private string OdemeTipiField;
        
        private int OdemeTipiIDField;
        
        private double OnaylananUrunAdediField;
        
        private string ParaBirimiField;
        
        private bool PazaryeriAktarildiField;
        
        private string PazaryeriIadeIdField;
        
        private double ReddedilenUrunAdediField;
        
        private int SiparisIDField;
        
        private string SiparisKaynagiField;
        
        private string SiparisNoField;
        
        private System.DateTime SiparisTarihiField;
        
        private double TutarField;
        
        private string TutarStrField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double BekleyenUrunAdedi
        {
            get
            {
                return this.BekleyenUrunAdediField;
            }
            set
            {
                this.BekleyenUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevap
        {
            get
            {
                return this.CevapField;
            }
            set
            {
                this.CevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CevapTarihi
        {
            get
            {
                return this.CevapTarihiField;
            }
            set
            {
                this.CevapTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevaplayan
        {
            get
            {
                return this.CevaplayanField;
            }
            set
            {
                this.CevaplayanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CevaplayanID
        {
            get
            {
                return this.CevaplayanIDField;
            }
            set
            {
                this.CevaplayanIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Iban
        {
            get
            {
                return this.IbanField;
            }
            set
            {
                this.IbanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IbanIsimSoyisim
        {
            get
            {
                return this.IbanIsimSoyisimField;
            }
            set
            {
                this.IbanIsimSoyisimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoKodu
        {
            get
            {
                return this.KargoKoduField;
            }
            set
            {
                this.KargoKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaAdi
        {
            get
            {
                return this.MagazaAdiField;
            }
            set
            {
                this.MagazaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaID
        {
            get
            {
                return this.MagazaIDField;
            }
            set
            {
                this.MagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MagazaIadeKodu
        {
            get
            {
                return this.MagazaIadeKoduField;
            }
            set
            {
                this.MagazaIadeKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notlar
        {
            get
            {
                return this.NotlarField;
            }
            set
            {
                this.NotlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipiID
        {
            get
            {
                return this.OdemeTipiIDField;
            }
            set
            {
                this.OdemeTipiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OnaylananUrunAdedi
        {
            get
            {
                return this.OnaylananUrunAdediField;
            }
            set
            {
                this.OnaylananUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool PazaryeriAktarildi
        {
            get
            {
                return this.PazaryeriAktarildiField;
            }
            set
            {
                this.PazaryeriAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PazaryeriIadeId
        {
            get
            {
                return this.PazaryeriIadeIdField;
            }
            set
            {
                this.PazaryeriIadeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ReddedilenUrunAdedi
        {
            get
            {
                return this.ReddedilenUrunAdediField;
            }
            set
            {
                this.ReddedilenUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SiparisTarihi
        {
            get
            {
                return this.SiparisTarihiField;
            }
            set
            {
                this.SiparisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TutarStr
        {
            get
            {
                return this.TutarStrField;
            }
            set
            {
                this.TutarStrField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepUrunSelectRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepUrunSelectRequest : object
    {
        
        private int IadeTalepIDField;
        
        private int[] IadeTalepIdListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeTalepID
        {
            get
            {
                return this.IadeTalepIDField;
            }
            set
            {
                this.IadeTalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] IadeTalepIdList
        {
            get
            {
                return this.IadeTalepIdListField;
            }
            set
            {
                this.IadeTalepIdListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepUrunResponse : object
    {
        
        private double BekleyenUrunAdediField;
        
        private string ErrorMessageField;
        
        private PushDashboard.CustomServis.IadeTalepUrun[] IadeTalepUrunListField;
        
        private bool IsErrorField;
        
        private double OnaylananUrunAdediField;
        
        private double ReddedilenUrunAdediField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double BekleyenUrunAdedi
        {
            get
            {
                return this.BekleyenUrunAdediField;
            }
            set
            {
                this.BekleyenUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalepUrun[] IadeTalepUrunList
        {
            get
            {
                return this.IadeTalepUrunListField;
            }
            set
            {
                this.IadeTalepUrunListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double OnaylananUrunAdedi
        {
            get
            {
                return this.OnaylananUrunAdediField;
            }
            set
            {
                this.OnaylananUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ReddedilenUrunAdedi
        {
            get
            {
                return this.ReddedilenUrunAdediField;
            }
            set
            {
                this.ReddedilenUrunAdediField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeTalepUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeTalepUrun : object
    {
        
        private double AdetField;
        
        private string BarkodField;
        
        private string CevapField;
        
        private string CevapResimField;
        
        private System.DateTime CevapTarihiField;
        
        private string DurumField;
        
        private int DurumIDField;
        
        private string EkseceneklerField;
        
        private int IDField;
        
        private int IadeTalepIDField;
        
        private int IptalIadeNedenIDField;
        
        private string IptalIadeNedenTanimField;
        
        private int IptalIadeTipIDField;
        
        private string IptalIadeTipTanimField;
        
        private int PazaryeriCevapIdField;
        
        private string PazaryeriIadeIdField;
        
        private string ResimField;
        
        private int SiparisIDField;
        
        private int SiparisUrunIDField;
        
        private string StokKoduField;
        
        private string UrlField;
        
        private string UrunAdField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private string UrunNotuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Barkod
        {
            get
            {
                return this.BarkodField;
            }
            set
            {
                this.BarkodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevap
        {
            get
            {
                return this.CevapField;
            }
            set
            {
                this.CevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CevapResim
        {
            get
            {
                return this.CevapResimField;
            }
            set
            {
                this.CevapResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CevapTarihi
        {
            get
            {
                return this.CevapTarihiField;
            }
            set
            {
                this.CevapTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Eksecenekler
        {
            get
            {
                return this.EkseceneklerField;
            }
            set
            {
                this.EkseceneklerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeTalepID
        {
            get
            {
                return this.IadeTalepIDField;
            }
            set
            {
                this.IadeTalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IptalIadeNedenID
        {
            get
            {
                return this.IptalIadeNedenIDField;
            }
            set
            {
                this.IptalIadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IptalIadeNedenTanim
        {
            get
            {
                return this.IptalIadeNedenTanimField;
            }
            set
            {
                this.IptalIadeNedenTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IptalIadeTipID
        {
            get
            {
                return this.IptalIadeTipIDField;
            }
            set
            {
                this.IptalIadeTipIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IptalIadeTipTanim
        {
            get
            {
                return this.IptalIadeTipTanimField;
            }
            set
            {
                this.IptalIadeTipTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriCevapId
        {
            get
            {
                return this.PazaryeriCevapIdField;
            }
            set
            {
                this.PazaryeriCevapIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PazaryeriIadeId
        {
            get
            {
                return this.PazaryeriIadeIdField;
            }
            set
            {
                this.PazaryeriIadeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Resim
        {
            get
            {
                return this.ResimField;
            }
            set
            {
                this.ResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAd
        {
            get
            {
                return this.UrunAdField;
            }
            set
            {
                this.UrunAdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunNotu
        {
            get
            {
                return this.UrunNotuField;
            }
            set
            {
                this.UrunNotuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepGuncelleRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepGuncelleRequest : object
    {
        
        private bool BankaKomisyonuIadeField;
        
        private bool IadeKoduOlusturField;
        
        private double IadeTutariField;
        
        private bool KargoTutariIadeField;
        
        private bool KartaIadeYapField;
        
        private bool MailGonderField;
        
        private PushDashboard.CustomServis.WebSiparisDurumlari SiparisDurumuField;
        
        private bool SiparisDurumuGuncelleField;
        
        private int SiparisUrunDurumField;
        
        private PushDashboard.CustomServis.IadeTalepUpdate TalepField;
        
        private bool UrunleriGuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BankaKomisyonuIade
        {
            get
            {
                return this.BankaKomisyonuIadeField;
            }
            set
            {
                this.BankaKomisyonuIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IadeKoduOlustur
        {
            get
            {
                return this.IadeKoduOlusturField;
            }
            set
            {
                this.IadeKoduOlusturField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IadeTutari
        {
            get
            {
                return this.IadeTutariField;
            }
            set
            {
                this.IadeTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoTutariIade
        {
            get
            {
                return this.KargoTutariIadeField;
            }
            set
            {
                this.KargoTutariIadeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KartaIadeYap
        {
            get
            {
                return this.KartaIadeYapField;
            }
            set
            {
                this.KartaIadeYapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGonder
        {
            get
            {
                return this.MailGonderField;
            }
            set
            {
                this.MailGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebSiparisDurumlari SiparisDurumu
        {
            get
            {
                return this.SiparisDurumuField;
            }
            set
            {
                this.SiparisDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisDurumuGuncelle
        {
            get
            {
                return this.SiparisDurumuGuncelleField;
            }
            set
            {
                this.SiparisDurumuGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunDurum
        {
            get
            {
                return this.SiparisUrunDurumField;
            }
            set
            {
                this.SiparisUrunDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalepUpdate Talep
        {
            get
            {
                return this.TalepField;
            }
            set
            {
                this.TalepField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UrunleriGuncelle
        {
            get
            {
                return this.UrunleriGuncelleField;
            }
            set
            {
                this.UrunleriGuncelleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeTalepUpdate", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeTalepUpdate : object
    {
        
        private int IDField;
        
        private PushDashboard.CustomServis.IadeTalepUrunGuncelle[] IadeTalepUrunlerField;
        
        private string IbanField;
        
        private string IbanIsimSoyisimField;
        
        private string KargoCagriKoduField;
        
        private int KargoEntegrasyonIDField;
        
        private string NotlarField;
        
        private System.Nullable<System.DateTime> OlusturmaTarihiField;
        
        private System.Nullable<bool> PazaryeriAktarildiField;
        
        private string PazaryeriIadeIdField;
        
        private int SiparisIDField;
        
        private string TumUrunCevapField;
        
        private int TumUrunCevaplayanIDField;
        
        private int TumUrunDurumIDField;
        
        private int TumUrunIadeIptalIadeNedenIDField;
        
        private int TumUrunIadeIptalIadeTipIDField;
        
        private double TutarField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalepUrunGuncelle[] IadeTalepUrunler
        {
            get
            {
                return this.IadeTalepUrunlerField;
            }
            set
            {
                this.IadeTalepUrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Iban
        {
            get
            {
                return this.IbanField;
            }
            set
            {
                this.IbanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IbanIsimSoyisim
        {
            get
            {
                return this.IbanIsimSoyisimField;
            }
            set
            {
                this.IbanIsimSoyisimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string KargoCagriKodu
        {
            get
            {
                return this.KargoCagriKoduField;
            }
            set
            {
                this.KargoCagriKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KargoEntegrasyonID
        {
            get
            {
                return this.KargoEntegrasyonIDField;
            }
            set
            {
                this.KargoEntegrasyonIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Notlar
        {
            get
            {
                return this.NotlarField;
            }
            set
            {
                this.NotlarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OlusturmaTarihi
        {
            get
            {
                return this.OlusturmaTarihiField;
            }
            set
            {
                this.OlusturmaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> PazaryeriAktarildi
        {
            get
            {
                return this.PazaryeriAktarildiField;
            }
            set
            {
                this.PazaryeriAktarildiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PazaryeriIadeId
        {
            get
            {
                return this.PazaryeriIadeIdField;
            }
            set
            {
                this.PazaryeriIadeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TumUrunCevap
        {
            get
            {
                return this.TumUrunCevapField;
            }
            set
            {
                this.TumUrunCevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunCevaplayanID
        {
            get
            {
                return this.TumUrunCevaplayanIDField;
            }
            set
            {
                this.TumUrunCevaplayanIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunDurumID
        {
            get
            {
                return this.TumUrunDurumIDField;
            }
            set
            {
                this.TumUrunDurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunIadeIptalIadeNedenID
        {
            get
            {
                return this.TumUrunIadeIptalIadeNedenIDField;
            }
            set
            {
                this.TumUrunIadeIptalIadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TumUrunIadeIptalIadeTipID
        {
            get
            {
                return this.TumUrunIadeIptalIadeTipIDField;
            }
            set
            {
                this.TumUrunIadeIptalIadeTipIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebSiparisDurumlari", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum WebSiparisDurumlari : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnSiparis = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnayBekliyor = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Onaylandi = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeBekliyor = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Paketleniyor = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TedarikEdiliyor = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KargoyaVerildi = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimEdildi = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iptal = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Iade = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Silinmis = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeTalepAlindi = 11,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeUlastiOdemeYapilacak = 12,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeOdemeYapildi = 13,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimOncesiIptal = 14,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalTalebi = 15,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KismiIadeTalebi = 16,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        KismiIadeYapildi = 17,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        TeslimEdilemedi = 18,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="IadeTalepUrunGuncelle", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class IadeTalepUrunGuncelle : object
    {
        
        private double AdetField;
        
        private string CevapField;
        
        private string CevapResimField;
        
        private int DurumIDField;
        
        private int IadeTalepIDField;
        
        private int IptalIadeNedenIDField;
        
        private int IptalIadeTipIDField;
        
        private int PazaryeriCevapIdField;
        
        private string PazaryeriIadeIdField;
        
        private int SiparisUrunIDField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private string UrunNotuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevap
        {
            get
            {
                return this.CevapField;
            }
            set
            {
                this.CevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CevapResim
        {
            get
            {
                return this.CevapResimField;
            }
            set
            {
                this.CevapResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IadeTalepID
        {
            get
            {
                return this.IadeTalepIDField;
            }
            set
            {
                this.IadeTalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IptalIadeNedenID
        {
            get
            {
                return this.IptalIadeNedenIDField;
            }
            set
            {
                this.IptalIadeNedenIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IptalIadeTipID
        {
            get
            {
                return this.IptalIadeTipIDField;
            }
            set
            {
                this.IptalIadeTipIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int PazaryeriCevapId
        {
            get
            {
                return this.PazaryeriCevapIdField;
            }
            set
            {
                this.PazaryeriCevapIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PazaryeriIadeId
        {
            get
            {
                return this.PazaryeriIadeIdField;
            }
            set
            {
                this.PazaryeriIadeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunNotu
        {
            get
            {
                return this.UrunNotuField;
            }
            set
            {
                this.UrunNotuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebIadeTalepUpdateResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebIadeTalepUpdateResponse : object
    {
        
        private string ErrorMessageField;
        
        private string IadeKargoKoduField;
        
        private PushDashboard.CustomServis.IadeTalep IadeTalepField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.BLEnumsOdemeDurumlari OdemeDurumField;
        
        private string OdemeHataMesajField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IadeKargoKodu
        {
            get
            {
                return this.IadeKargoKoduField;
            }
            set
            {
                this.IadeKargoKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.IadeTalep IadeTalep
        {
            get
            {
                return this.IadeTalepField;
            }
            set
            {
                this.IadeTalepField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.BLEnumsOdemeDurumlari OdemeDurum
        {
            get
            {
                return this.OdemeDurumField;
            }
            set
            {
                this.OdemeDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeHataMesaj
        {
            get
            {
                return this.OdemeHataMesajField;
            }
            set
            {
                this.OdemeHataMesajField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLEnums.OdemeDurumlari", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public enum BLEnumsOdemeDurumlari : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OnayBekliyor = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Onaylandi = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hatali = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IadeEdilmis = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        IptalEdilmis = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeBekliyor = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OdemeTalepEdildi = 6,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepSelectRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepSelectRequest : object
    {
        
        private PushDashboard.CustomServis.DegisimTalepFiltre FiltreField;
        
        private PushDashboard.CustomServis.WebServisSayfalama SayfalamaField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalepFiltre Filtre
        {
            get
            {
                return this.FiltreField;
            }
            set
            {
                this.FiltreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebServisSayfalama Sayfalama
        {
            get
            {
                return this.SayfalamaField;
            }
            set
            {
                this.SayfalamaField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DegisimTalepFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DegisimTalepFiltre : object
    {
        
        private int DurumIDField;
        
        private System.Nullable<System.DateTime> EklemeTarihi1Field;
        
        private System.Nullable<System.DateTime> EklemeTarihi2Field;
        
        private int IDField;
        
        private int ParaIadeTipiField;
        
        private int SiparisIDField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi1
        {
            get
            {
                return this.EklemeTarihi1Field;
            }
            set
            {
                this.EklemeTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi2
        {
            get
            {
                return this.EklemeTarihi2Field;
            }
            set
            {
                this.EklemeTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ParaIadeTipi
        {
            get
            {
                return this.ParaIadeTipiField;
            }
            set
            {
                this.ParaIadeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepResponse : object
    {
        
        private PushDashboard.CustomServis.DegisimTalep[] DegisimTalepListField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalep[] DegisimTalepList
        {
            get
            {
                return this.DegisimTalepListField;
            }
            set
            {
                this.DegisimTalepListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DegisimTalep", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DegisimTalep : object
    {
        
        private string DurumField;
        
        private int DurumIDField;
        
        private System.DateTime EklemeTarihiField;
        
        private int IDField;
        
        private string KodField;
        
        private string OdemeTipiField;
        
        private int OdemeTipiIDField;
        
        private int SiparisIDField;
        
        private string SiparisNoField;
        
        private System.DateTime SiparisTarihiField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kod
        {
            get
            {
                return this.KodField;
            }
            set
            {
                this.KodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipiID
        {
            get
            {
                return this.OdemeTipiIDField;
            }
            set
            {
                this.OdemeTipiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisNo
        {
            get
            {
                return this.SiparisNoField;
            }
            set
            {
                this.SiparisNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SiparisTarihi
        {
            get
            {
                return this.SiparisTarihiField;
            }
            set
            {
                this.SiparisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepUrunSelectRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepUrunSelectRequest : object
    {
        
        private int DegisimTalepIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DegisimTalepID
        {
            get
            {
                return this.DegisimTalepIDField;
            }
            set
            {
                this.DegisimTalepIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepUrunResponse : object
    {
        
        private PushDashboard.CustomServis.DegisimTalepUrun[] DegisimTalepUrunListField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalepUrun[] DegisimTalepUrunList
        {
            get
            {
                return this.DegisimTalepUrunListField;
            }
            set
            {
                this.DegisimTalepUrunListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DegisimTalepUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DegisimTalepUrun : object
    {
        
        private double AdetField;
        
        private string BarkodField;
        
        private int DegisimTalepIDField;
        
        private string DurumField;
        
        private int DurumIDField;
        
        private string EkseceneklerField;
        
        private int IDField;
        
        private string ResimField;
        
        private int SiparisIDField;
        
        private int SiparisUrunIDField;
        
        private string StokKoduField;
        
        private string TalepStokKoduField;
        
        private string TalepUrunAdField;
        
        private string TalepUrunEkseceneklerField;
        
        private int TalepUrunIDField;
        
        private int TalepUrunKartiIDField;
        
        private string TalepUrunResimField;
        
        private string TalepUrunUrlField;
        
        private string UrlField;
        
        private string UrunAdField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Barkod
        {
            get
            {
                return this.BarkodField;
            }
            set
            {
                this.BarkodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DegisimTalepID
        {
            get
            {
                return this.DegisimTalepIDField;
            }
            set
            {
                this.DegisimTalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Eksecenekler
        {
            get
            {
                return this.EkseceneklerField;
            }
            set
            {
                this.EkseceneklerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Resim
        {
            get
            {
                return this.ResimField;
            }
            set
            {
                this.ResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TalepStokKodu
        {
            get
            {
                return this.TalepStokKoduField;
            }
            set
            {
                this.TalepStokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TalepUrunAd
        {
            get
            {
                return this.TalepUrunAdField;
            }
            set
            {
                this.TalepUrunAdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TalepUrunEksecenekler
        {
            get
            {
                return this.TalepUrunEkseceneklerField;
            }
            set
            {
                this.TalepUrunEkseceneklerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TalepUrunID
        {
            get
            {
                return this.TalepUrunIDField;
            }
            set
            {
                this.TalepUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TalepUrunKartiID
        {
            get
            {
                return this.TalepUrunKartiIDField;
            }
            set
            {
                this.TalepUrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TalepUrunResim
        {
            get
            {
                return this.TalepUrunResimField;
            }
            set
            {
                this.TalepUrunResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TalepUrunUrl
        {
            get
            {
                return this.TalepUrunUrlField;
            }
            set
            {
                this.TalepUrunUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAd
        {
            get
            {
                return this.UrunAdField;
            }
            set
            {
                this.UrunAdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepGuncelleRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepGuncelleRequest : object
    {
        
        private bool MailGonderField;
        
        private PushDashboard.CustomServis.DegisimTalepUpdate TalepField;
        
        private bool UrunleriGuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGonder
        {
            get
            {
                return this.MailGonderField;
            }
            set
            {
                this.MailGonderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalepUpdate Talep
        {
            get
            {
                return this.TalepField;
            }
            set
            {
                this.TalepField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UrunleriGuncelle
        {
            get
            {
                return this.UrunleriGuncelleField;
            }
            set
            {
                this.UrunleriGuncelleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DegisimTalepUpdate", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DegisimTalepUpdate : object
    {
        
        private PushDashboard.CustomServis.DegisimTalepUrunGuncelle[] DegisimTalepUrunlerField;
        
        private int DurumIDField;
        
        private int IDField;
        
        private string KodField;
        
        private int SiparisIDField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalepUrunGuncelle[] DegisimTalepUrunler
        {
            get
            {
                return this.DegisimTalepUrunlerField;
            }
            set
            {
                this.DegisimTalepUrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kod
        {
            get
            {
                return this.KodField;
            }
            set
            {
                this.KodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DegisimTalepUrunGuncelle", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DegisimTalepUrunGuncelle : object
    {
        
        private double AdetField;
        
        private int DegisimTalepIDField;
        
        private int DurumIDField;
        
        private int IDField;
        
        private int SiparisUrunIDField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DegisimTalepID
        {
            get
            {
                return this.DegisimTalepIDField;
            }
            set
            {
                this.DegisimTalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisUrunID
        {
            get
            {
                return this.SiparisUrunIDField;
            }
            set
            {
                this.SiparisUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebDegisimTalepUpdateResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebDegisimTalepUpdateResponse : object
    {
        
        private PushDashboard.CustomServis.DegisimTalep DegisimTalepField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private string KodField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.DegisimTalep DegisimTalep
        {
            get
            {
                return this.DegisimTalepField;
            }
            set
            {
                this.DegisimTalepField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kod
        {
            get
            {
                return this.KodField;
            }
            set
            {
                this.KodField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTaksitSecenekRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTaksitSecenekRequest : object
    {
        
        private int MaksTaksitSayisiField;
        
        private double TutarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaksTaksitSayisi
        {
            get
            {
                return this.MaksTaksitSayisiField;
            }
            set
            {
                this.MaksTaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Tutar
        {
            get
            {
                return this.TutarField;
            }
            set
            {
                this.TutarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTaksitSecenekResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTaksitSecenekResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.WebTaksitBanka[] TaksitSecenekleriField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebTaksitBanka[] TaksitSecenekleri
        {
            get
            {
                return this.TaksitSecenekleriField;
            }
            set
            {
                this.TaksitSecenekleriField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTaksitBanka", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTaksitBanka : object
    {
        
        private string BankaAdiField;
        
        private int BankaIdField;
        
        private PushDashboard.CustomServis.WebTaksitSecenek[] TaksitlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BankaAdi
        {
            get
            {
                return this.BankaAdiField;
            }
            set
            {
                this.BankaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaId
        {
            get
            {
                return this.BankaIdField;
            }
            set
            {
                this.BankaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebTaksitSecenek[] Taksitler
        {
            get
            {
                return this.TaksitlerField;
            }
            set
            {
                this.TaksitlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTaksitSecenek", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebTaksitSecenek : object
    {
        
        private int BankaIdField;
        
        private int TaksitSayisiField;
        
        private string TaksitTanimField;
        
        private double TaksitTutariField;
        
        private double ToplamTutarField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaId
        {
            get
            {
                return this.BankaIdField;
            }
            set
            {
                this.BankaIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TaksitSayisi
        {
            get
            {
                return this.TaksitSayisiField;
            }
            set
            {
                this.TaksitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TaksitTanim
        {
            get
            {
                return this.TaksitTanimField;
            }
            set
            {
                this.TaksitTanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double TaksitTutari
        {
            get
            {
                return this.TaksitTutariField;
            }
            set
            {
                this.TaksitTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamTutar
        {
            get
            {
                return this.ToplamTutarField;
            }
            set
            {
                this.ToplamTutarField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanya", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanya : object
    {
        
        private int Adet1Field;
        
        private int Adet2Field;
        
        private bool AktifField;
        
        private int BankaIDField;
        
        private System.DateTime BaslangicTarihiField;
        
        private System.DateTime BitisTarihiField;
        
        private System.DateTime EklemeTarihiField;
        
        private string ExtJsonDataField;
        
        private int HedefEtiketIDField;
        
        private int HedefKategoriIDField;
        
        private int HedefUrunIDField;
        
        private int HediyeCekiKategoriIDField;
        
        private System.Nullable<System.DateTime> HediyeCekiKullanimBaslangicTarihiField;
        
        private System.Nullable<System.DateTime> HediyeCekiKullanimBitisTarihiField;
        
        private int IDField;
        
        private double IndirimliUrunIslemDegeriField;
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        private int KampanyaTuruField;
        
        private int OdemeTipiField;
        
        private bool SablonField;
        
        private double SepetTutariField;
        
        private string TanimField;
        
        private double TekrarTutariField;
        
        private int UrunID1Field;
        
        private int UrunID2Field;
        
        private int UyeMaksKullanimSayisiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Adet1
        {
            get
            {
                return this.Adet1Field;
            }
            set
            {
                this.Adet1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Adet2
        {
            get
            {
                return this.Adet2Field;
            }
            set
            {
                this.Adet2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaID
        {
            get
            {
                return this.BankaIDField;
            }
            set
            {
                this.BankaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BaslangicTarihi
        {
            get
            {
                return this.BaslangicTarihiField;
            }
            set
            {
                this.BaslangicTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BitisTarihi
        {
            get
            {
                return this.BitisTarihiField;
            }
            set
            {
                this.BitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ExtJsonData
        {
            get
            {
                return this.ExtJsonDataField;
            }
            set
            {
                this.ExtJsonDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HedefEtiketID
        {
            get
            {
                return this.HedefEtiketIDField;
            }
            set
            {
                this.HedefEtiketIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HedefKategoriID
        {
            get
            {
                return this.HedefKategoriIDField;
            }
            set
            {
                this.HedefKategoriIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HedefUrunID
        {
            get
            {
                return this.HedefUrunIDField;
            }
            set
            {
                this.HedefUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int HediyeCekiKategoriID
        {
            get
            {
                return this.HediyeCekiKategoriIDField;
            }
            set
            {
                this.HediyeCekiKategoriIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> HediyeCekiKullanimBaslangicTarihi
        {
            get
            {
                return this.HediyeCekiKullanimBaslangicTarihiField;
            }
            set
            {
                this.HediyeCekiKullanimBaslangicTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> HediyeCekiKullanimBitisTarihi
        {
            get
            {
                return this.HediyeCekiKullanimBitisTarihiField;
            }
            set
            {
                this.HediyeCekiKullanimBitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IndirimliUrunIslemDegeri
        {
            get
            {
                return this.IndirimliUrunIslemDegeriField;
            }
            set
            {
                this.IndirimliUrunIslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KampanyaTuru
        {
            get
            {
                return this.KampanyaTuruField;
            }
            set
            {
                this.KampanyaTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Sablon
        {
            get
            {
                return this.SablonField;
            }
            set
            {
                this.SablonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double SepetTutari
        {
            get
            {
                return this.SepetTutariField;
            }
            set
            {
                this.SepetTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double TekrarTutari
        {
            get
            {
                return this.TekrarTutariField;
            }
            set
            {
                this.TekrarTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID1
        {
            get
            {
                return this.UrunID1Field;
            }
            set
            {
                this.UrunID1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID2
        {
            get
            {
                return this.UrunID2Field;
            }
            set
            {
                this.UrunID2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeMaksKullanimSayisi
        {
            get
            {
                return this.UyeMaksKullanimSayisiField;
            }
            set
            {
                this.UyeMaksKullanimSayisiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetFavoriUrunlerRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetFavoriUrunlerRequest : object
    {
        
        private System.Nullable<System.DateTime> BaslangicTarihiField;
        
        private System.Nullable<System.DateTime> BitisTarihiField;
        
        private int KayitSayisiField;
        
        private int SayfaNoField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> BaslangicTarihi
        {
            get
            {
                return this.BaslangicTarihiField;
            }
            set
            {
                this.BaslangicTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> BitisTarihi
        {
            get
            {
                return this.BitisTarihiField;
            }
            set
            {
                this.BitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KayitSayisi
        {
            get
            {
                return this.KayitSayisiField;
            }
            set
            {
                this.KayitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SayfaNo
        {
            get
            {
                return this.SayfaNoField;
            }
            set
            {
                this.SayfaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetFavoriUrunlerResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetFavoriUrunlerResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.WebFavoriUrunler[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebFavoriUrunler[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebFavoriUrunler", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebFavoriUrunler : object
    {
        
        private System.DateTime EklemeTarihiField;
        
        private int FavoriUrunIDField;
        
        private string ParaBirimiField;
        
        private string ResimUrlField;
        
        private string StokKoduField;
        
        private double ToplamStokAdediField;
        
        private string UrunAdiField;
        
        private double UrunFiyatiField;
        
        private double UrunFiyatiKdvField;
        
        private int UrunKartiIDField;
        
        private double UrunSayisiField;
        
        private string UrunUrlField;
        
        private int UyeIDField;
        
        private int VaryasyonSayisiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FavoriUrunID
        {
            get
            {
                return this.FavoriUrunIDField;
            }
            set
            {
                this.FavoriUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimUrl
        {
            get
            {
                return this.ResimUrlField;
            }
            set
            {
                this.ResimUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamStokAdedi
        {
            get
            {
                return this.ToplamStokAdediField;
            }
            set
            {
                this.ToplamStokAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyati
        {
            get
            {
                return this.UrunFiyatiField;
            }
            set
            {
                this.UrunFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyatiKdv
        {
            get
            {
                return this.UrunFiyatiKdvField;
            }
            set
            {
                this.UrunFiyatiKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunSayisi
        {
            get
            {
                return this.UrunSayisiField;
            }
            set
            {
                this.UrunSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunUrl
        {
            get
            {
                return this.UrunUrlField;
            }
            set
            {
                this.UrunUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int VaryasyonSayisi
        {
            get
            {
                return this.VaryasyonSayisiField;
            }
            set
            {
                this.VaryasyonSayisiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddFavoriUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddFavoriUrunRequest : object
    {
        
        private double AdetField;
        
        private int UrunKartiIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddFavoriUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddFavoriUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveFavoriUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveFavoriUrunRequest : object
    {
        
        private int FavoriUrunIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FavoriUrunID
        {
            get
            {
                return this.FavoriUrunIDField;
            }
            set
            {
                this.FavoriUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveFavoriUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveFavoriUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetFiyatAlarmUrunlerRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetFiyatAlarmUrunlerRequest : object
    {
        
        private int UrunKartiIdField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiId
        {
            get
            {
                return this.UrunKartiIdField;
            }
            set
            {
                this.UrunKartiIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetFiyatAlarmUrunlerResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetFiyatAlarmUrunlerResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.WebFiyatAlarmUrunler[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebFiyatAlarmUrunler[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebFiyatAlarmUrunler", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebFiyatAlarmUrunler : object
    {
        
        private System.DateTime EklemeTarihiField;
        
        private double EklenenFiyatField;
        
        private int FiyatAlarmUrunIDField;
        
        private string ParaBirimiField;
        
        private string ResimUrlField;
        
        private string StokKoduField;
        
        private double ToplamStokAdediField;
        
        private string UrunAdiField;
        
        private double UrunFiyatiField;
        
        private double UrunFiyatiKdvField;
        
        private int UrunKartiIDField;
        
        private string UrunUrlField;
        
        private int UyeIDField;
        
        private int VaryasyonSayisiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double EklenenFiyat
        {
            get
            {
                return this.EklenenFiyatField;
            }
            set
            {
                this.EklenenFiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiyatAlarmUrunID
        {
            get
            {
                return this.FiyatAlarmUrunIDField;
            }
            set
            {
                this.FiyatAlarmUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimUrl
        {
            get
            {
                return this.ResimUrlField;
            }
            set
            {
                this.ResimUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamStokAdedi
        {
            get
            {
                return this.ToplamStokAdediField;
            }
            set
            {
                this.ToplamStokAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyati
        {
            get
            {
                return this.UrunFiyatiField;
            }
            set
            {
                this.UrunFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyatiKdv
        {
            get
            {
                return this.UrunFiyatiKdvField;
            }
            set
            {
                this.UrunFiyatiKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunUrl
        {
            get
            {
                return this.UrunUrlField;
            }
            set
            {
                this.UrunUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int VaryasyonSayisi
        {
            get
            {
                return this.VaryasyonSayisiField;
            }
            set
            {
                this.VaryasyonSayisiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddFiyatAlarmUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddFiyatAlarmUrunRequest : object
    {
        
        private double FiyatField;
        
        private int MagazaIDField;
        
        private int UrunKartiIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Fiyat
        {
            get
            {
                return this.FiyatField;
            }
            set
            {
                this.FiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaID
        {
            get
            {
                return this.MagazaIDField;
            }
            set
            {
                this.MagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddFiyatAlarmUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddFiyatAlarmUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveFiyatAlarmUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveFiyatAlarmUrunRequest : object
    {
        
        private int FiyatAlarmUrunIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FiyatAlarmUrunID
        {
            get
            {
                return this.FiyatAlarmUrunIDField;
            }
            set
            {
                this.FiyatAlarmUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveFiyatAlarmUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveFiyatAlarmUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetStokAlarmUrunlerRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetStokAlarmUrunlerRequest : object
    {
        
        private int UrunKartiIdField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiId
        {
            get
            {
                return this.UrunKartiIdField;
            }
            set
            {
                this.UrunKartiIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetStokAlarmUrunlerResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetStokAlarmUrunlerResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.WebStokAlarmUrunler[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebStokAlarmUrunler[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebStokAlarmUrunler", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebStokAlarmUrunler : object
    {
        
        private System.DateTime EklemeTarihiField;
        
        private string ParaBirimiField;
        
        private string ResimUrlField;
        
        private int StokAlarmUrunIDField;
        
        private string StokKoduField;
        
        private double ToplamStokAdediField;
        
        private string UrunAdiField;
        
        private double UrunFiyatiField;
        
        private double UrunFiyatiKdvField;
        
        private int UrunKartiIDField;
        
        private string UrunUrlField;
        
        private int UyeIDField;
        
        private int VaryasyonSayisiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ResimUrl
        {
            get
            {
                return this.ResimUrlField;
            }
            set
            {
                this.ResimUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int StokAlarmUrunID
        {
            get
            {
                return this.StokAlarmUrunIDField;
            }
            set
            {
                this.StokAlarmUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StokKodu
        {
            get
            {
                return this.StokKoduField;
            }
            set
            {
                this.StokKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double ToplamStokAdedi
        {
            get
            {
                return this.ToplamStokAdediField;
            }
            set
            {
                this.ToplamStokAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyati
        {
            get
            {
                return this.UrunFiyatiField;
            }
            set
            {
                this.UrunFiyatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double UrunFiyatiKdv
        {
            get
            {
                return this.UrunFiyatiKdvField;
            }
            set
            {
                this.UrunFiyatiKdvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunUrl
        {
            get
            {
                return this.UrunUrlField;
            }
            set
            {
                this.UrunUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int VaryasyonSayisi
        {
            get
            {
                return this.VaryasyonSayisiField;
            }
            set
            {
                this.VaryasyonSayisiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddStokAlarmUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddStokAlarmUrunRequest : object
    {
        
        private double FiyatField;
        
        private int MagazaIDField;
        
        private int UrunIDField;
        
        private int UrunKartiIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Fiyat
        {
            get
            {
                return this.FiyatField;
            }
            set
            {
                this.FiyatField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MagazaID
        {
            get
            {
                return this.MagazaIDField;
            }
            set
            {
                this.MagazaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunID
        {
            get
            {
                return this.UrunIDField;
            }
            set
            {
                this.UrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunKartiID
        {
            get
            {
                return this.UrunKartiIDField;
            }
            set
            {
                this.UrunKartiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="AddStokAlarmUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class AddStokAlarmUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveStokAlarmUrunRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveStokAlarmUrunRequest : object
    {
        
        private int StokAlarmUrunIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int StokAlarmUrunID
        {
            get
            {
                return this.StokAlarmUrunIDField;
            }
            set
            {
                this.StokAlarmUrunIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RemoveStokAlarmUrunResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class RemoveStokAlarmUrunResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMenuRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetMenuRequest : object
    {
        
        private System.Nullable<int> AktifField;
        
        private string DilField;
        
        private System.Nullable<int> MenuIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Dil
        {
            get
            {
                return this.DilField;
            }
            set
            {
                this.DilField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> MenuID
        {
            get
            {
                return this.MenuIDField;
            }
            set
            {
                this.MenuIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveMenuRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveMenuRequest : object
    {
        
        private string DilField;
        
        private PushDashboard.CustomServis.WebMenu[] MenulerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Dil
        {
            get
            {
                return this.DilField;
            }
            set
            {
                this.DilField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebMenu[] Menuler
        {
            get
            {
                return this.MenulerField;
            }
            set
            {
                this.MenulerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeleteMenuRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DeleteMenuRequest : object
    {
        
        private PushDashboard.CustomServis.WebMenu[] MenulerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebMenu[] Menuler
        {
            get
            {
                return this.MenulerField;
            }
            set
            {
                this.MenulerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSaveSupportTicketRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSaveSupportTicketRequest : object
    {
        
        private string BaslikField;
        
        private int CevaplayanField;
        
        private int KonuIdField;
        
        private string MesajField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Baslik
        {
            get
            {
                return this.BaslikField;
            }
            set
            {
                this.BaslikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Cevaplayan
        {
            get
            {
                return this.CevaplayanField;
            }
            set
            {
                this.CevaplayanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KonuId
        {
            get
            {
                return this.KonuIdField;
            }
            set
            {
                this.KonuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mesaj
        {
            get
            {
                return this.MesajField;
            }
            set
            {
                this.MesajField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSaveSupportTicketResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSaveSupportTicketResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketSubjectsRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketSubjectsRequest : object
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketSubjectsResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketSubjectsResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        private PushDashboard.CustomServis.ServisSupportTicketSubject[] SubjectsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.ServisSupportTicketSubject[] Subjects
        {
            get
            {
                return this.SubjectsField;
            }
            set
            {
                this.SubjectsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSupportTicketSubject", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSupportTicketSubject : object
    {
        
        private int KonuIdField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KonuId
        {
            get
            {
                return this.KonuIdField;
            }
            set
            {
                this.KonuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSaveSupportTicketAnswerRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSaveSupportTicketAnswerRequest : object
    {
        
        private int DestekIdField;
        
        private string MesajField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DestekId
        {
            get
            {
                return this.DestekIdField;
            }
            set
            {
                this.DestekIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mesaj
        {
            get
            {
                return this.MesajField;
            }
            set
            {
                this.MesajField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisSaveSupportTicketAnswerResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisSaveSupportTicketAnswerResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisUpdateTicketStatusRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisUpdateTicketStatusRequest : object
    {
        
        private int KonuIdField;
        
        private int YeniDurumField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KonuId
        {
            get
            {
                return this.KonuIdField;
            }
            set
            {
                this.KonuIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int YeniDurum
        {
            get
            {
                return this.YeniDurumField;
            }
            set
            {
                this.YeniDurumField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisUpdateTicketStatusResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisUpdateTicketStatusResponse : object
    {
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketsRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketsRequest : object
    {
        
        private int DestekIdField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DestekId
        {
            get
            {
                return this.DestekIdField;
            }
            set
            {
                this.DestekIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketsResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketsResponse : object
    {
        
        private PushDashboard.CustomServis.ServisMusteriTalep[] DestekTalepleriField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.ServisMusteriTalep[] DestekTalepleri
        {
            get
            {
                return this.DestekTalepleriField;
            }
            set
            {
                this.DestekTalepleriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisMusteriTalep", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisMusteriTalep : object
    {
        
        private bool CozulduField;
        
        private string DurumField;
        
        private int DurumIDField;
        
        private System.DateTime EklemeTarihiField;
        
        private int IDField;
        
        private System.DateTime KapatilmaTarihiField;
        
        private string KonuField;
        
        private int KonuIDField;
        
        private string MesajField;
        
        private string SorumluAdiField;
        
        private int SorumluIDField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Cozuldu
        {
            get
            {
                return this.CozulduField;
            }
            set
            {
                this.CozulduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Durum
        {
            get
            {
                return this.DurumField;
            }
            set
            {
                this.DurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime KapatilmaTarihi
        {
            get
            {
                return this.KapatilmaTarihiField;
            }
            set
            {
                this.KapatilmaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Konu
        {
            get
            {
                return this.KonuField;
            }
            set
            {
                this.KonuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KonuID
        {
            get
            {
                return this.KonuIDField;
            }
            set
            {
                this.KonuIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mesaj
        {
            get
            {
                return this.MesajField;
            }
            set
            {
                this.MesajField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SorumluAdi
        {
            get
            {
                return this.SorumluAdiField;
            }
            set
            {
                this.SorumluAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SorumluID
        {
            get
            {
                return this.SorumluIDField;
            }
            set
            {
                this.SorumluIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetAllSupportTicketsRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetAllSupportTicketsRequest : object
    {
        
        private PushDashboard.CustomServis.WebMusteriTalepFiltre FiltreField;
        
        private PushDashboard.CustomServis.WebServisSayfalama SayfalamaField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebMusteriTalepFiltre Filtre
        {
            get
            {
                return this.FiltreField;
            }
            set
            {
                this.FiltreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebServisSayfalama Sayfalama
        {
            get
            {
                return this.SayfalamaField;
            }
            set
            {
                this.SayfalamaField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebMusteriTalepFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebMusteriTalepFiltre : object
    {
        
        private int CozulduField;
        
        private int DetayIdField;
        
        private int DurumIDField;
        
        private System.Nullable<System.DateTime> EklemeTarihi1Field;
        
        private System.Nullable<System.DateTime> EklemeTarihi2Field;
        
        private int IDField;
        
        private System.Nullable<System.DateTime> KapatilmaTarihi1Field;
        
        private System.Nullable<System.DateTime> KapatilmaTarihi2Field;
        
        private int KonuIDField;
        
        private string MesajField;
        
        private int SitedeGosterField;
        
        private int TipField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Cozuldu
        {
            get
            {
                return this.CozulduField;
            }
            set
            {
                this.CozulduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DetayId
        {
            get
            {
                return this.DetayIdField;
            }
            set
            {
                this.DetayIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DurumID
        {
            get
            {
                return this.DurumIDField;
            }
            set
            {
                this.DurumIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi1
        {
            get
            {
                return this.EklemeTarihi1Field;
            }
            set
            {
                this.EklemeTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> EklemeTarihi2
        {
            get
            {
                return this.EklemeTarihi2Field;
            }
            set
            {
                this.EklemeTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> KapatilmaTarihi1
        {
            get
            {
                return this.KapatilmaTarihi1Field;
            }
            set
            {
                this.KapatilmaTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> KapatilmaTarihi2
        {
            get
            {
                return this.KapatilmaTarihi2Field;
            }
            set
            {
                this.KapatilmaTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KonuID
        {
            get
            {
                return this.KonuIDField;
            }
            set
            {
                this.KonuIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mesaj
        {
            get
            {
                return this.MesajField;
            }
            set
            {
                this.MesajField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SitedeGoster
        {
            get
            {
                return this.SitedeGosterField;
            }
            set
            {
                this.SitedeGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketMessagesRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketMessagesRequest : object
    {
        
        private int DestekIdField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DestekId
        {
            get
            {
                return this.DestekIdField;
            }
            set
            {
                this.DestekIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisGetSupportTicketMessagesResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisGetSupportTicketMessagesResponse : object
    {
        
        private PushDashboard.CustomServis.ServisMusteriCevap[] DestekMesajlariField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.ServisMusteriCevap[] DestekMesajlari
        {
            get
            {
                return this.DestekMesajlariField;
            }
            set
            {
                this.DestekMesajlariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ServisMusteriCevap", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ServisMusteriCevap : object
    {
        
        private string CevapField;
        
        private int CevaplayanField;
        
        private string CevaplayanStrField;
        
        private int IDField;
        
        private System.DateTime KonusmaTarihiField;
        
        private int TalepIDField;
        
        private string UyeAdiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cevap
        {
            get
            {
                return this.CevapField;
            }
            set
            {
                this.CevapField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Cevaplayan
        {
            get
            {
                return this.CevaplayanField;
            }
            set
            {
                this.CevaplayanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CevaplayanStr
        {
            get
            {
                return this.CevaplayanStrField;
            }
            set
            {
                this.CevaplayanStrField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime KonusmaTarihi
        {
            get
            {
                return this.KonusmaTarihiField;
            }
            set
            {
                this.KonusmaTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TalepID
        {
            get
            {
                return this.TalepIDField;
            }
            set
            {
                this.TalepIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeAdi
        {
            get
            {
                return this.UyeAdiField;
            }
            set
            {
                this.UyeAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetCampaingBannersRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetCampaingBannersRequest : object
    {
        
        private int CampaingBannerIdField;
        
        private int GroupIdField;
        
        private int TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CampaingBannerId
        {
            get
            {
                return this.CampaingBannerIdField;
            }
            set
            {
                this.CampaingBannerIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GroupId
        {
            get
            {
                return this.GroupIdField;
            }
            set
            {
                this.GroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetCampaingBannersResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetCampaingBannersResponse : object
    {
        
        private PushDashboard.CustomServis.CampaingBannerList[] CampaingBannersField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.CampaingBannerList[] CampaingBanners
        {
            get
            {
                return this.CampaingBannersField;
            }
            set
            {
                this.CampaingBannersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CampaingBannerList", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class CampaingBannerList : object
    {
        
        private bool ActiveField;
        
        private string BannetTypeTextField;
        
        private int BrandIdField;
        
        private int CampaingBannerIdField;
        
        private int CategoryIdField;
        
        private string ClassField;
        
        private bool CountDownActiveField;
        
        private System.DateTime CountDownDateField;
        
        private System.DateTime CreatedDateField;
        
        private string DefinitionsField;
        
        private int GroupIdField;
        
        private string ImageField;
        
        private string ImagePathField;
        
        private string LangField;
        
        private int OrderField;
        
        private string StrCountDownDateField;
        
        private int TypeField;
        
        private string UrlField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Active
        {
            get
            {
                return this.ActiveField;
            }
            set
            {
                this.ActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BannetTypeText
        {
            get
            {
                return this.BannetTypeTextField;
            }
            set
            {
                this.BannetTypeTextField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BrandId
        {
            get
            {
                return this.BrandIdField;
            }
            set
            {
                this.BrandIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CampaingBannerId
        {
            get
            {
                return this.CampaingBannerIdField;
            }
            set
            {
                this.CampaingBannerIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CategoryId
        {
            get
            {
                return this.CategoryIdField;
            }
            set
            {
                this.CategoryIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Class
        {
            get
            {
                return this.ClassField;
            }
            set
            {
                this.ClassField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CountDownActive
        {
            get
            {
                return this.CountDownActiveField;
            }
            set
            {
                this.CountDownActiveField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CountDownDate
        {
            get
            {
                return this.CountDownDateField;
            }
            set
            {
                this.CountDownDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreatedDate
        {
            get
            {
                return this.CreatedDateField;
            }
            set
            {
                this.CreatedDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Definitions
        {
            get
            {
                return this.DefinitionsField;
            }
            set
            {
                this.DefinitionsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GroupId
        {
            get
            {
                return this.GroupIdField;
            }
            set
            {
                this.GroupIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Image
        {
            get
            {
                return this.ImageField;
            }
            set
            {
                this.ImageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ImagePath
        {
            get
            {
                return this.ImagePathField;
            }
            set
            {
                this.ImagePathField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Lang
        {
            get
            {
                return this.LangField;
            }
            set
            {
                this.LangField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Order
        {
            get
            {
                return this.OrderField;
            }
            set
            {
                this.OrderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StrCountDownDate
        {
            get
            {
                return this.StrCountDownDateField;
            }
            set
            {
                this.StrCountDownDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Url
        {
            get
            {
                return this.UrlField;
            }
            set
            {
                this.UrlField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaTicimax", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaTicimax : object
    {
        
        private string AciklamaField;
        
        private bool AndroidAktifField;
        
        private System.DateTime BaslangicTarihiField;
        
        private System.DateTime BitisTarihiField;
        
        private System.DateTime DuzenlemeTarihiField;
        
        private int DuzenleyenKullaniciField;
        
        private System.DateTime EklemeTarihiField;
        
        private int EkleyenKullaniciField;
        
        private int GunKosuluField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] GunlerField;
        
        private PushDashboard.CustomServis.WebKampanyaHediye HediyelerField;
        
        private int IDField;
        
        private bool IOSAktifField;
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        private PushDashboard.CustomServis.WebKampanyaKosul KosullarField;
        
        private int MaksSiparisSayisiField;
        
        private double MinAlisverisTutariField;
        
        private int MinUrunAdediField;
        
        private bool MobilAktifField;
        
        private int OncelikField;
        
        private string ParabirimiField;
        
        private PushDashboard.CustomServis.WebKampanyaSaatAraligi[] SaatAraliklariField;
        
        private bool SablonField;
        
        private bool SepetteAciklamaGosterField;
        
        private string TanimField;
        
        private double TekrarDegeriField;
        
        private int TipField;
        
        private string UlkeField;
        
        private int UyeMaksKullanimSayisiField;
        
        private PushDashboard.CustomServis.WebKampanyaUye[] UyelerField;
        
        private PushDashboard.CustomServis.WebKampanyaUygulamaYontemi UygulamaYontemleriField;
        
        private bool WebAktifField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Aciklama
        {
            get
            {
                return this.AciklamaField;
            }
            set
            {
                this.AciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AndroidAktif
        {
            get
            {
                return this.AndroidAktifField;
            }
            set
            {
                this.AndroidAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BaslangicTarihi
        {
            get
            {
                return this.BaslangicTarihiField;
            }
            set
            {
                this.BaslangicTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BitisTarihi
        {
            get
            {
                return this.BitisTarihiField;
            }
            set
            {
                this.BitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DuzenlemeTarihi
        {
            get
            {
                return this.DuzenlemeTarihiField;
            }
            set
            {
                this.DuzenlemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DuzenleyenKullanici
        {
            get
            {
                return this.DuzenleyenKullaniciField;
            }
            set
            {
                this.DuzenleyenKullaniciField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklemeTarihi
        {
            get
            {
                return this.EklemeTarihiField;
            }
            set
            {
                this.EklemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int EkleyenKullanici
        {
            get
            {
                return this.EkleyenKullaniciField;
            }
            set
            {
                this.EkleyenKullaniciField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GunKosulu
        {
            get
            {
                return this.GunKosuluField;
            }
            set
            {
                this.GunKosuluField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Gunler
        {
            get
            {
                return this.GunlerField;
            }
            set
            {
                this.GunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaHediye Hediyeler
        {
            get
            {
                return this.HediyelerField;
            }
            set
            {
                this.HediyelerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IOSAktif
        {
            get
            {
                return this.IOSAktifField;
            }
            set
            {
                this.IOSAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaKosul Kosullar
        {
            get
            {
                return this.KosullarField;
            }
            set
            {
                this.KosullarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MaksSiparisSayisi
        {
            get
            {
                return this.MaksSiparisSayisiField;
            }
            set
            {
                this.MaksSiparisSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double MinAlisverisTutari
        {
            get
            {
                return this.MinAlisverisTutariField;
            }
            set
            {
                this.MinAlisverisTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MinUrunAdedi
        {
            get
            {
                return this.MinUrunAdediField;
            }
            set
            {
                this.MinUrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MobilAktif
        {
            get
            {
                return this.MobilAktifField;
            }
            set
            {
                this.MobilAktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Oncelik
        {
            get
            {
                return this.OncelikField;
            }
            set
            {
                this.OncelikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Parabirimi
        {
            get
            {
                return this.ParabirimiField;
            }
            set
            {
                this.ParabirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaSaatAraligi[] SaatAraliklari
        {
            get
            {
                return this.SaatAraliklariField;
            }
            set
            {
                this.SaatAraliklariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Sablon
        {
            get
            {
                return this.SablonField;
            }
            set
            {
                this.SablonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SepetteAciklamaGoster
        {
            get
            {
                return this.SepetteAciklamaGosterField;
            }
            set
            {
                this.SepetteAciklamaGosterField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double TekrarDegeri
        {
            get
            {
                return this.TekrarDegeriField;
            }
            set
            {
                this.TekrarDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeMaksKullanimSayisi
        {
            get
            {
                return this.UyeMaksKullanimSayisiField;
            }
            set
            {
                this.UyeMaksKullanimSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaUye[] Uyeler
        {
            get
            {
                return this.UyelerField;
            }
            set
            {
                this.UyelerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaUygulamaYontemi UygulamaYontemleri
        {
            get
            {
                return this.UygulamaYontemleriField;
            }
            set
            {
                this.UygulamaYontemleriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool WebAktif
        {
            get
            {
                return this.WebAktifField;
            }
            set
            {
                this.WebAktifField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaHediye", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaHediye : object
    {
        
        private PushDashboard.CustomServis.WebKampanyaHediyeCeki HediyeCekiField;
        
        private PushDashboard.CustomServis.WebKampanyaHediyeUrunBilgisi UrunField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaHediyeCeki HediyeCeki
        {
            get
            {
                return this.HediyeCekiField;
            }
            set
            {
                this.HediyeCekiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaHediyeUrunBilgisi Urun
        {
            get
            {
                return this.UrunField;
            }
            set
            {
                this.UrunField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaKosul", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaKosul : object
    {
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] BankalarField;
        
        private string[] BinNumaralariField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] BirlestirilebilirKampanyalarField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] EtiketlerField;
        
        private bool HediyeCekiIleKullanilabilirField;
        
        private bool IndirimliUrunlerdeGecerliField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] KategorilerField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] MarkalarField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] OdemeTipleriField;
        
        private int[] TaksitlerField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] TedarikcilerField;
        
        private int TipField;
        
        private PushDashboard.CustomServis.WebKampanyaKosulUrun[] UrunlerField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] UyeGruplariField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Bankalar
        {
            get
            {
                return this.BankalarField;
            }
            set
            {
                this.BankalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] BinNumaralari
        {
            get
            {
                return this.BinNumaralariField;
            }
            set
            {
                this.BinNumaralariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] BirlestirilebilirKampanyalar
        {
            get
            {
                return this.BirlestirilebilirKampanyalarField;
            }
            set
            {
                this.BirlestirilebilirKampanyalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Etiketler
        {
            get
            {
                return this.EtiketlerField;
            }
            set
            {
                this.EtiketlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool HediyeCekiIleKullanilabilir
        {
            get
            {
                return this.HediyeCekiIleKullanilabilirField;
            }
            set
            {
                this.HediyeCekiIleKullanilabilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IndirimliUrunlerdeGecerli
        {
            get
            {
                return this.IndirimliUrunlerdeGecerliField;
            }
            set
            {
                this.IndirimliUrunlerdeGecerliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Kategoriler
        {
            get
            {
                return this.KategorilerField;
            }
            set
            {
                this.KategorilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Markalar
        {
            get
            {
                return this.MarkalarField;
            }
            set
            {
                this.MarkalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] OdemeTipleri
        {
            get
            {
                return this.OdemeTipleriField;
            }
            set
            {
                this.OdemeTipleriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int[] Taksitler
        {
            get
            {
                return this.TaksitlerField;
            }
            set
            {
                this.TaksitlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Tedarikciler
        {
            get
            {
                return this.TedarikcilerField;
            }
            set
            {
                this.TedarikcilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Tip
        {
            get
            {
                return this.TipField;
            }
            set
            {
                this.TipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaKosulUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] UyeGruplari
        {
            get
            {
                return this.UyeGruplariField;
            }
            set
            {
                this.UyeGruplariField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaUygulamaYontemi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaUygulamaYontemi : object
    {
        
        private PushDashboard.CustomServis.WebKampanyaFiyatAdetAraligi[] AdetAraliklariField;
        
        private PushDashboard.CustomServis.WebKampanyaFiyatAdetAraligi[] FiyatAraliklariField;
        
        private PushDashboard.CustomServis.WebKampanyaIndirimDeger[] IndirimDegerleriField;
        
        private int IndirimUrunOncelikField;
        
        private bool SatirBazliUygulaField;
        
        private bool SepeteUygulaField;
        
        private bool SepetteIndirimField;
        
        private int UcretliUrunAdediField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaFiyatAdetAraligi[] AdetAraliklari
        {
            get
            {
                return this.AdetAraliklariField;
            }
            set
            {
                this.AdetAraliklariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaFiyatAdetAraligi[] FiyatAraliklari
        {
            get
            {
                return this.FiyatAraliklariField;
            }
            set
            {
                this.FiyatAraliklariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIndirimDeger[] IndirimDegerleri
        {
            get
            {
                return this.IndirimDegerleriField;
            }
            set
            {
                this.IndirimDegerleriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IndirimUrunOncelik
        {
            get
            {
                return this.IndirimUrunOncelikField;
            }
            set
            {
                this.IndirimUrunOncelikField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SatirBazliUygula
        {
            get
            {
                return this.SatirBazliUygulaField;
            }
            set
            {
                this.SatirBazliUygulaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SepeteUygula
        {
            get
            {
                return this.SepeteUygulaField;
            }
            set
            {
                this.SepeteUygulaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SepetteIndirim
        {
            get
            {
                return this.SepetteIndirimField;
            }
            set
            {
                this.SepetteIndirimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UcretliUrunAdedi
        {
            get
            {
                return this.UcretliUrunAdediField;
            }
            set
            {
                this.UcretliUrunAdediField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaIDTanim", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaIDTanim : object
    {
        
        private int IDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaSaatAraligi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaSaatAraligi : object
    {
        
        private int BaslangicSaatiField;
        
        private int BitisSaatiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BaslangicSaati
        {
            get
            {
                return this.BaslangicSaatiField;
            }
            set
            {
                this.BaslangicSaatiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BitisSaati
        {
            get
            {
                return this.BitisSaatiField;
            }
            set
            {
                this.BitisSaatiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaUye", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaUye : object
    {
        
        private int IdField;
        
        private string IsimField;
        
        private string MailField;
        
        private bool MailGonderField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGonder
        {
            get
            {
                return this.MailGonderField;
            }
            set
            {
                this.MailGonderField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaHediyeCeki", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaHediyeCeki : object
    {
        
        private bool BaskaKampanyalarlaBilestirilebilirField;
        
        private int BaslamaSuresiField;
        
        private System.DateTime BaslangicTarihiField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] BirlestirilebilirKampanyalarField;
        
        private System.DateTime BitisTarihiField;
        
        private int GecerliOlduguSureField;
        
        private bool IndirimliUrunleKullanilabilirField;
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        private bool KargoIndirimiField;
        
        private bool KargoUcretsizField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] KategorilerField;
        
        private int KullanimSayisiField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] MarkalarField;
        
        private double MinimumSepetTutariField;
        
        private bool SiparisTarihineGoreOlusturField;
        
        private int UyeMaksKullanimSayisiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BaskaKampanyalarlaBilestirilebilir
        {
            get
            {
                return this.BaskaKampanyalarlaBilestirilebilirField;
            }
            set
            {
                this.BaskaKampanyalarlaBilestirilebilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BaslamaSuresi
        {
            get
            {
                return this.BaslamaSuresiField;
            }
            set
            {
                this.BaslamaSuresiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BaslangicTarihi
        {
            get
            {
                return this.BaslangicTarihiField;
            }
            set
            {
                this.BaslangicTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] BirlestirilebilirKampanyalar
        {
            get
            {
                return this.BirlestirilebilirKampanyalarField;
            }
            set
            {
                this.BirlestirilebilirKampanyalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime BitisTarihi
        {
            get
            {
                return this.BitisTarihiField;
            }
            set
            {
                this.BitisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int GecerliOlduguSure
        {
            get
            {
                return this.GecerliOlduguSureField;
            }
            set
            {
                this.GecerliOlduguSureField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IndirimliUrunleKullanilabilir
        {
            get
            {
                return this.IndirimliUrunleKullanilabilirField;
            }
            set
            {
                this.IndirimliUrunleKullanilabilirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoIndirimi
        {
            get
            {
                return this.KargoIndirimiField;
            }
            set
            {
                this.KargoIndirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KargoUcretsiz
        {
            get
            {
                return this.KargoUcretsizField;
            }
            set
            {
                this.KargoUcretsizField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Kategoriler
        {
            get
            {
                return this.KategorilerField;
            }
            set
            {
                this.KategorilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KullanimSayisi
        {
            get
            {
                return this.KullanimSayisiField;
            }
            set
            {
                this.KullanimSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Markalar
        {
            get
            {
                return this.MarkalarField;
            }
            set
            {
                this.MarkalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double MinimumSepetTutari
        {
            get
            {
                return this.MinimumSepetTutariField;
            }
            set
            {
                this.MinimumSepetTutariField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SiparisTarihineGoreOlustur
        {
            get
            {
                return this.SiparisTarihineGoreOlusturField;
            }
            set
            {
                this.SiparisTarihineGoreOlusturField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeMaksKullanimSayisi
        {
            get
            {
                return this.UyeMaksKullanimSayisiField;
            }
            set
            {
                this.UyeMaksKullanimSayisiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaHediyeUrunBilgisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaHediyeUrunBilgisi : object
    {
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] EtiketlerField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] KategorilerField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] MarkalarField;
        
        private bool OtomatikSepeteEkleField;
        
        private PushDashboard.CustomServis.WebKampanyaIDTanim[] TedarikcilerField;
        
        private int UrunAdediField;
        
        private PushDashboard.CustomServis.WebKampanyaHediyeUrun[] UrunlerField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Etiketler
        {
            get
            {
                return this.EtiketlerField;
            }
            set
            {
                this.EtiketlerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Kategoriler
        {
            get
            {
                return this.KategorilerField;
            }
            set
            {
                this.KategorilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Markalar
        {
            get
            {
                return this.MarkalarField;
            }
            set
            {
                this.MarkalarField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OtomatikSepeteEkle
        {
            get
            {
                return this.OtomatikSepeteEkleField;
            }
            set
            {
                this.OtomatikSepeteEkleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaIDTanim[] Tedarikciler
        {
            get
            {
                return this.TedarikcilerField;
            }
            set
            {
                this.TedarikcilerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunAdedi
        {
            get
            {
                return this.UrunAdediField;
            }
            set
            {
                this.UrunAdediField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebKampanyaHediyeUrun[] Urunler
        {
            get
            {
                return this.UrunlerField;
            }
            set
            {
                this.UrunlerField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaHediyeUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaHediyeUrun : object
    {
        
        private double AdetField;
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        private string ResimField;
        
        private string UrunAdiField;
        
        private int UrunIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Resim
        {
            get
            {
                return this.ResimField;
            }
            set
            {
                this.ResimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunId
        {
            get
            {
                return this.UrunIdField;
            }
            set
            {
                this.UrunIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaKosulUrun", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaKosulUrun : object
    {
        
        private double AdetField;
        
        private string UrunAdiField;
        
        private int UrunIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Adet
        {
            get
            {
                return this.AdetField;
            }
            set
            {
                this.AdetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UrunAdi
        {
            get
            {
                return this.UrunAdiField;
            }
            set
            {
                this.UrunAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UrunId
        {
            get
            {
                return this.UrunIdField;
            }
            set
            {
                this.UrunIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaFiyatAdetAraligi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaFiyatAdetAraligi : object
    {
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        private double MaxField;
        
        private double MinField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Max
        {
            get
            {
                return this.MaxField;
            }
            set
            {
                this.MaxField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Min
        {
            get
            {
                return this.MinField;
            }
            set
            {
                this.MinField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebKampanyaIndirimDeger", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class WebKampanyaIndirimDeger : object
    {
        
        private double IslemDegeriField;
        
        private int IslemTipiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double IslemDegeri
        {
            get
            {
                return this.IslemDegeriField;
            }
            set
            {
                this.IslemDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectSilinenUyelerRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectSilinenUyelerRequest : object
    {
        
        private System.Nullable<System.DateTime> SilinmeTarihiBasField;
        
        private System.Nullable<System.DateTime> SilinmeTarihiSonField;
        
        private System.Nullable<System.DateTime> UyelikTarihiBasField;
        
        private System.Nullable<System.DateTime> UyelikTarihiSonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SilinmeTarihiBas
        {
            get
            {
                return this.SilinmeTarihiBasField;
            }
            set
            {
                this.SilinmeTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SilinmeTarihiSon
        {
            get
            {
                return this.SilinmeTarihiSonField;
            }
            set
            {
                this.SilinmeTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UyelikTarihiBas
        {
            get
            {
                return this.UyelikTarihiBasField;
            }
            set
            {
                this.UyelikTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UyelikTarihiSon
        {
            get
            {
                return this.UyelikTarihiSonField;
            }
            set
            {
                this.UyelikTarihiSonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetMailListRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.CustomServis.DeleteMailListRequest))]
    public partial class GetMailListRequest : object
    {
        
        private string MailField;
        
        private string MailKodField;
        
        private string TelefonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MailKod
        {
            get
            {
                return this.MailKodField;
            }
            set
            {
                this.MailKodField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeleteMailListRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class DeleteMailListRequest : PushDashboard.CustomServis.GetMailListRequest
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectWebhookRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectWebhookRequest : object
    {
        
        private int IDField;
        
        private System.Nullable<PushDashboard.CustomServis.WebhookIslem> IslemTipiField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<PushDashboard.CustomServis.WebhookIslem> IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectIadeOdemeListesiRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectIadeOdemeListesiRequest : object
    {
        
        private System.Nullable<int> KullaniciIDField;
        
        private PushDashboard.CustomServis.WebIadeOdemeDurum OdemeDurumuField;
        
        private System.Nullable<int> OdemeTipiField;
        
        private System.Nullable<System.DateTime> OrderDateBeginField;
        
        private System.Nullable<System.DateTime> OrderDateEndField;
        
        private System.Nullable<int> SiparisIDField;
        
        private System.Nullable<System.DateTime> TarihBaslangicField;
        
        private System.Nullable<System.DateTime> TarihBitisField;
        
        private System.Nullable<int> UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> KullaniciID
        {
            get
            {
                return this.KullaniciIDField;
            }
            set
            {
                this.KullaniciIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.CustomServis.WebIadeOdemeDurum OdemeDurumu
        {
            get
            {
                return this.OdemeDurumuField;
            }
            set
            {
                this.OdemeDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OdemeTipi
        {
            get
            {
                return this.OdemeTipiField;
            }
            set
            {
                this.OdemeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OrderDateBegin
        {
            get
            {
                return this.OrderDateBeginField;
            }
            set
            {
                this.OrderDateBeginField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> OrderDateEnd
        {
            get
            {
                return this.OrderDateEndField;
            }
            set
            {
                this.OrderDateEndField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TarihBaslangic
        {
            get
            {
                return this.TarihBaslangicField;
            }
            set
            {
                this.TarihBaslangicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> TarihBitis
        {
            get
            {
                return this.TarihBitisField;
            }
            set
            {
                this.TarihBitisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectOdemeBildirimiRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectOdemeBildirimiRequest : object
    {
        
        private int BankaIDField;
        
        private int ItemIDField;
        
        private int SiparisIDField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BankaID
        {
            get
            {
                return this.BankaIDField;
            }
            set
            {
                this.BankaIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ItemID
        {
            get
            {
                return this.ItemIDField;
            }
            set
            {
                this.ItemIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SiparisID
        {
            get
            {
                return this.SiparisIDField;
            }
            set
            {
                this.SiparisIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SaveOdemeBildirimDurumRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SaveOdemeBildirimDurumRequest : object
    {
        
        private System.Nullable<int> OdemeBildirimIdField;
        
        private System.Nullable<int> OnayDurumuField;
        
        private System.Nullable<int> OnaylayanKullaniciIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OdemeBildirimId
        {
            get
            {
                return this.OdemeBildirimIdField;
            }
            set
            {
                this.OdemeBildirimIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OnayDurumu
        {
            get
            {
                return this.OnayDurumuField;
            }
            set
            {
                this.OnayDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OnaylayanKullaniciId
        {
            get
            {
                return this.OnaylayanKullaniciIdField;
            }
            set
            {
                this.OnaylayanKullaniciIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SelectMarketPlaceBilgiRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class SelectMarketPlaceBilgiRequest : object
    {
        
        private System.Nullable<bool> IsMarketplaceField;
        
        private System.Nullable<bool> KargoTeslimSuresiDoluMuField;
        
        private System.Nullable<bool> PazaryeriOdemesiYapildiMiField;
        
        private System.Nullable<int> SiparisDurumField;
        
        private string SiparisKaynagiField;
        
        private System.Nullable<System.DateTime> SiparisTarihiBasField;
        
        private System.Nullable<System.DateTime> SiparisTarihiSonField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IsMarketplace
        {
            get
            {
                return this.IsMarketplaceField;
            }
            set
            {
                this.IsMarketplaceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> KargoTeslimSuresiDoluMu
        {
            get
            {
                return this.KargoTeslimSuresiDoluMuField;
            }
            set
            {
                this.KargoTeslimSuresiDoluMuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> PazaryeriOdemesiYapildiMi
        {
            get
            {
                return this.PazaryeriOdemesiYapildiMiField;
            }
            set
            {
                this.PazaryeriOdemesiYapildiMiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> SiparisDurum
        {
            get
            {
                return this.SiparisDurumField;
            }
            set
            {
                this.SiparisDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiparisKaynagi
        {
            get
            {
                return this.SiparisKaynagiField;
            }
            set
            {
                this.SiparisKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiBas
        {
            get
            {
                return this.SiparisTarihiBasField;
            }
            set
            {
                this.SiparisTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SiparisTarihiSon
        {
            get
            {
                return this.SiparisTarihiSonField;
            }
            set
            {
                this.SiparisTarihiSonField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetDinamikFormDataRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class GetDinamikFormDataRequest : object
    {
        
        private System.DateTime EklenmeTarihiBaslangicField;
        
        private System.DateTime EklenmeTarihiBitisField;
        
        private int FormIdField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklenmeTarihiBaslangic
        {
            get
            {
                return this.EklenmeTarihiBaslangicField;
            }
            set
            {
                this.EklenmeTarihiBaslangicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime EklenmeTarihiBitis
        {
            get
            {
                return this.EklenmeTarihiBitisField;
            }
            set
            {
                this.EklenmeTarihiBitisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int FormId
        {
            get
            {
                return this.FormIdField;
            }
            set
            {
                this.FormIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="PushDashboard.CustomServis.ICustomServis")]
    public interface ICustomServis
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveEntegrasyonId", ReplyAction="http://tempuri.org/ICustomServis/SaveEntegrasyonIdResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebServisResponse> SaveEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectEntegrasyonId", ReplyAction="http://tempuri.org/ICustomServis/SelectEntegrasyonIdResponse")]
        System.Threading.Tasks.Task<string> SelectEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/DeleteEntegrasyonId", ReplyAction="http://tempuri.org/ICustomServis/DeleteEntegrasyonIdResponse")]
        System.Threading.Tasks.Task DeleteEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectMailList", ReplyAction="http://tempuri.org/ICustomServis/SelectMailListResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.MailList[]> SelectMailListAsync(string UyeKodu, System.Nullable<System.DateTime> BaslangicTarihi, System.Nullable<System.DateTime> BitisTarihi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectEntegrasyon", ReplyAction="http://tempuri.org/ICustomServis/SelectEntegrasyonResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.Entegrasyon[]> SelectEntegrasyonAsync(string UyeKodu, string EntegrasyonKodu, int KayitSayisi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectKargoDesiFiyat", ReplyAction="http://tempuri.org/ICustomServis/SelectKargoDesiFiyatResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoDesiFiyatSelectResponse> SelectKargoDesiFiyatAsync(string UyeKodu, PushDashboard.CustomServis.KargoDesiFiyatSelectRequest SelectRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GuncelleKargoDesiFiyat", ReplyAction="http://tempuri.org/ICustomServis/GuncelleKargoDesiFiyatResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoDesiFiyatSelectResponse> GuncelleKargoDesiFiyatAsync(string UyeKodu, PushDashboard.CustomServis.KargoDesiFiyatGuncelleRequest GuncelleRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectUlkeler", ReplyAction="http://tempuri.org/ICustomServis/SelectUlkelerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoUlke[]> SelectUlkelerAsync(string UyeKodu, PushDashboard.CustomServis.SelectUlkeRequest SelectRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectIller", ReplyAction="http://tempuri.org/ICustomServis/SelectIllerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoIl[]> SelectIllerAsync(string UyeKodu, PushDashboard.CustomServis.SelectIlRequest SelectRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectIlceler", ReplyAction="http://tempuri.org/ICustomServis/SelectIlcelerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoIlce[]> SelectIlcelerAsync(string UyeKodu, PushDashboard.CustomServis.SelectIlceRequest SelectRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectKargoFirmalari", ReplyAction="http://tempuri.org/ICustomServis/SelectKargoFirmalariResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoFirma[]> SelectKargoFirmalariAsync(string UyeKodu);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectIadeTalebi", ReplyAction="http://tempuri.org/ICustomServis/SelectIadeTalebiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepResponse> SelectIadeTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepSelectRequest IadeTalebiRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectIadeTalebiUrun", ReplyAction="http://tempuri.org/ICustomServis/SelectIadeTalebiUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepUrunResponse> SelectIadeTalebiUrunAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepUrunSelectRequest IadeTalebiUrunRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/UpdateIadeTalebi", ReplyAction="http://tempuri.org/ICustomServis/UpdateIadeTalebiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepUpdateResponse> UpdateIadeTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepGuncelleRequest IadeTalebi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectDegisimTalebi", ReplyAction="http://tempuri.org/ICustomServis/SelectDegisimTalebiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepResponse> SelectDegisimTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepSelectRequest DegisimTalepRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectDegisimTalebiUrun", ReplyAction="http://tempuri.org/ICustomServis/SelectDegisimTalebiUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepUrunResponse> SelectDegisimTalebiUrunAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepUrunSelectRequest DegisimTalepUrunRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/UpdateDegisimTalebi", ReplyAction="http://tempuri.org/ICustomServis/UpdateDegisimTalebiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepUpdateResponse> UpdateDegisimTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepGuncelleRequest DegisimTalep);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetTaksitSecenek", ReplyAction="http://tempuri.org/ICustomServis/GetTaksitSecenekResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebTaksitSecenekResponse> GetTaksitSecenekAsync(string UyeKodu, PushDashboard.CustomServis.WebTaksitSecenekRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetKampanya", ReplyAction="http://tempuri.org/ICustomServis/GetKampanyaResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebKampanya[]> GetKampanyaAsync(string UyeKodu, int KampanyaId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetFavoriUrunler", ReplyAction="http://tempuri.org/ICustomServis/GetFavoriUrunlerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetFavoriUrunlerResponse> GetFavoriUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetFavoriUrunlerRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/AddFavoriUrun", ReplyAction="http://tempuri.org/ICustomServis/AddFavoriUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.AddFavoriUrunResponse> AddFavoriUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddFavoriUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/RemoveFavoriUrun", ReplyAction="http://tempuri.org/ICustomServis/RemoveFavoriUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveFavoriUrunResponse> RemoveFavoriUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveFavoriUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetFiyatAlarmUrunler", ReplyAction="http://tempuri.org/ICustomServis/GetFiyatAlarmUrunlerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetFiyatAlarmUrunlerResponse> GetFiyatAlarmUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetFiyatAlarmUrunlerRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/AddFiyatAlarmUrun", ReplyAction="http://tempuri.org/ICustomServis/AddFiyatAlarmUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.AddFiyatAlarmUrunResponse> AddFiyatAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddFiyatAlarmUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/RemoveFiyatAlarmUrun", ReplyAction="http://tempuri.org/ICustomServis/RemoveFiyatAlarmUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveFiyatAlarmUrunResponse> RemoveFiyatAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveFiyatAlarmUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetStokAlarmUrunler", ReplyAction="http://tempuri.org/ICustomServis/GetStokAlarmUrunlerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetStokAlarmUrunlerResponse> GetStokAlarmUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetStokAlarmUrunlerRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/AddStokAlarmUrun", ReplyAction="http://tempuri.org/ICustomServis/AddStokAlarmUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.AddStokAlarmUrunResponse> AddStokAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddStokAlarmUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/RemoveStokAlarmUrun", ReplyAction="http://tempuri.org/ICustomServis/RemoveStokAlarmUrunResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveStokAlarmUrunResponse> RemoveStokAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveStokAlarmUrunRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetMenu", ReplyAction="http://tempuri.org/ICustomServis/GetMenuResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetMenuResponse> GetMenuAsync(string UyeKodu, PushDashboard.CustomServis.GetMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveMenu", ReplyAction="http://tempuri.org/ICustomServis/SaveMenuResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveMenuResponse> SaveMenuAsync(string UyeKodu, PushDashboard.CustomServis.SaveMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/DeleteMenu", ReplyAction="http://tempuri.org/ICustomServis/DeleteMenuResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteMenuResponse> DeleteMenuAsync(string UyeKodu, PushDashboard.CustomServis.DeleteMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveSupportTicket", ReplyAction="http://tempuri.org/ICustomServis/SaveSupportTicketResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisSaveSupportTicketResponse> SaveSupportTicketAsync(string UyeKodu, PushDashboard.CustomServis.ServisSaveSupportTicketRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetSupportTicketSubject", ReplyAction="http://tempuri.org/ICustomServis/GetSupportTicketSubjectResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketSubjectsResponse> GetSupportTicketSubjectAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketSubjectsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveSupportTicketAnswer", ReplyAction="http://tempuri.org/ICustomServis/SaveSupportTicketAnswerResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisSaveSupportTicketAnswerResponse> SaveSupportTicketAnswerAsync(string UyeKodu, PushDashboard.CustomServis.ServisSaveSupportTicketAnswerRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/UpdateTicketStatus", ReplyAction="http://tempuri.org/ICustomServis/UpdateTicketStatusResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisUpdateTicketStatusResponse> UpdateTicketStatusAsync(string UyeKodu, PushDashboard.CustomServis.ServisUpdateTicketStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetSupportTickets", ReplyAction="http://tempuri.org/ICustomServis/GetSupportTicketsResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketsResponse> GetSupportTicketsAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetAllSupportTickets", ReplyAction="http://tempuri.org/ICustomServis/GetAllSupportTicketsResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketsResponse> GetAllSupportTicketsAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetAllSupportTicketsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetSupportTicketMessages", ReplyAction="http://tempuri.org/ICustomServis/GetSupportTicketMessagesResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketMessagesResponse> GetSupportTicketMessagesAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketMessagesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetCampaingBanners", ReplyAction="http://tempuri.org/ICustomServis/GetCampaingBannersResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetCampaingBannersResponse> GetCampaingBannersAsync(string UyeKodu, PushDashboard.CustomServis.GetCampaingBannersRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetKampanyaV2", ReplyAction="http://tempuri.org/ICustomServis/GetKampanyaV2Response")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.WebKampanyaTicimax[]> GetKampanyaV2Async(string UyeKodu, int KampanyaId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectSiparisKaynaklari", ReplyAction="http://tempuri.org/ICustomServis/SelectSiparisKaynaklariResponse")]
        System.Threading.Tasks.Task<string[]> SelectSiparisKaynaklariAsync(string UyeKodu);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectBanka", ReplyAction="http://tempuri.org/ICustomServis/SelectBankaResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectBankaResponse> SelectBankaAsync(string UyeKodu, int BankaID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectSilinenUyeList", ReplyAction="http://tempuri.org/ICustomServis/SelectSilinenUyeListResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectSilinenUyelerResponse> SelectSilinenUyeListAsync(string UyeKodu, PushDashboard.CustomServis.SelectSilinenUyelerRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetMailList", ReplyAction="http://tempuri.org/ICustomServis/GetMailListResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetMailListResponse> GetMailListAsync(string UyeKodu, PushDashboard.CustomServis.GetMailListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveMailList", ReplyAction="http://tempuri.org/ICustomServis/SaveMailListResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveMailListResponse> SaveMailListAsync(string UyeKodu, PushDashboard.CustomServis.MailList mailList);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveWebhook", ReplyAction="http://tempuri.org/ICustomServis/SaveWebhookResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveWebhookResponse> SaveWebhookAsync(string UyeKodu, PushDashboard.CustomServis.Webhook webhook);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectWebhook", ReplyAction="http://tempuri.org/ICustomServis/SelectWebhookResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectWebhookResponse> SelectWebhookAsync(string UyeKodu, PushDashboard.CustomServis.SelectWebhookRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/DeleteWebhook", ReplyAction="http://tempuri.org/ICustomServis/DeleteWebhookResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteWebhookResponse> DeleteWebhookAsync(string UyeKodu, int webhookId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectIadeOdemeListesi", ReplyAction="http://tempuri.org/ICustomServis/SelectIadeOdemeListesiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectIadeOdemeListesiResponse> SelectIadeOdemeListesiAsync(string UyeKodu, PushDashboard.CustomServis.SelectIadeOdemeListesiRequest filtre, PushDashboard.CustomServis.WebServisSayfalama sayfalama);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/DeleteMailList", ReplyAction="http://tempuri.org/ICustomServis/DeleteMailListResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteMailListResponse> DeleteMailListAsync(string UyeKodu, PushDashboard.CustomServis.DeleteMailListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectOdemeBildirimi", ReplyAction="http://tempuri.org/ICustomServis/SelectOdemeBildirimiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectOdemeBildirimiResponse> SelectOdemeBildirimiAsync(string UyeKodu, PushDashboard.CustomServis.SelectOdemeBildirimiRequest selectOdemeBildirimiRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SaveOdemeBildirimDurum", ReplyAction="http://tempuri.org/ICustomServis/SaveOdemeBildirimDurumResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveOdemeBildirimDurumResponse> SaveOdemeBildirimDurumAsync(string UyeKodu, PushDashboard.CustomServis.SaveOdemeBildirimDurumRequest saveOdemeBildirimRequest);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/SelectSiparisMarketplaceBilgi", ReplyAction="http://tempuri.org/ICustomServis/SelectSiparisMarketplaceBilgiResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectMarketPlaceBilgiRespone> SelectSiparisMarketplaceBilgiAsync(string UyeKodu, PushDashboard.CustomServis.SelectMarketPlaceBilgiRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ICustomServis/GetDinamikFormData", ReplyAction="http://tempuri.org/ICustomServis/GetDinamikFormDataResponse")]
        System.Threading.Tasks.Task<PushDashboard.CustomServis.GetDinamikFormDataResponse> GetDinamikFormDataAsync(string uyeKodu, PushDashboard.CustomServis.GetDinamikFormDataRequest request);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface ICustomServisChannel : PushDashboard.CustomServis.ICustomServis, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class CustomServisClient : System.ServiceModel.ClientBase<PushDashboard.CustomServis.ICustomServis>, PushDashboard.CustomServis.ICustomServis
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public CustomServisClient() : 
                base(CustomServisClient.GetDefaultBinding(), CustomServisClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_ICustomServis.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CustomServisClient(EndpointConfiguration endpointConfiguration) : 
                base(CustomServisClient.GetBindingForEndpoint(endpointConfiguration), CustomServisClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CustomServisClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(CustomServisClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CustomServisClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(CustomServisClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public CustomServisClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebServisResponse> SaveEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon)
        {
            return base.Channel.SaveEntegrasyonIdAsync(UyeKodu, entegrasyon);
        }
        
        public System.Threading.Tasks.Task<string> SelectEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon)
        {
            return base.Channel.SelectEntegrasyonIdAsync(UyeKodu, entegrasyon);
        }
        
        public System.Threading.Tasks.Task DeleteEntegrasyonIdAsync(string UyeKodu, PushDashboard.CustomServis.Entegrasyon entegrasyon)
        {
            return base.Channel.DeleteEntegrasyonIdAsync(UyeKodu, entegrasyon);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.MailList[]> SelectMailListAsync(string UyeKodu, System.Nullable<System.DateTime> BaslangicTarihi, System.Nullable<System.DateTime> BitisTarihi)
        {
            return base.Channel.SelectMailListAsync(UyeKodu, BaslangicTarihi, BitisTarihi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.Entegrasyon[]> SelectEntegrasyonAsync(string UyeKodu, string EntegrasyonKodu, int KayitSayisi)
        {
            return base.Channel.SelectEntegrasyonAsync(UyeKodu, EntegrasyonKodu, KayitSayisi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoDesiFiyatSelectResponse> SelectKargoDesiFiyatAsync(string UyeKodu, PushDashboard.CustomServis.KargoDesiFiyatSelectRequest SelectRequest)
        {
            return base.Channel.SelectKargoDesiFiyatAsync(UyeKodu, SelectRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoDesiFiyatSelectResponse> GuncelleKargoDesiFiyatAsync(string UyeKodu, PushDashboard.CustomServis.KargoDesiFiyatGuncelleRequest GuncelleRequest)
        {
            return base.Channel.GuncelleKargoDesiFiyatAsync(UyeKodu, GuncelleRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoUlke[]> SelectUlkelerAsync(string UyeKodu, PushDashboard.CustomServis.SelectUlkeRequest SelectRequest)
        {
            return base.Channel.SelectUlkelerAsync(UyeKodu, SelectRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoIl[]> SelectIllerAsync(string UyeKodu, PushDashboard.CustomServis.SelectIlRequest SelectRequest)
        {
            return base.Channel.SelectIllerAsync(UyeKodu, SelectRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoIlce[]> SelectIlcelerAsync(string UyeKodu, PushDashboard.CustomServis.SelectIlceRequest SelectRequest)
        {
            return base.Channel.SelectIlcelerAsync(UyeKodu, SelectRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.KargoFirma[]> SelectKargoFirmalariAsync(string UyeKodu)
        {
            return base.Channel.SelectKargoFirmalariAsync(UyeKodu);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepResponse> SelectIadeTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepSelectRequest IadeTalebiRequest)
        {
            return base.Channel.SelectIadeTalebiAsync(UyeKodu, IadeTalebiRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepUrunResponse> SelectIadeTalebiUrunAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepUrunSelectRequest IadeTalebiUrunRequest)
        {
            return base.Channel.SelectIadeTalebiUrunAsync(UyeKodu, IadeTalebiUrunRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebIadeTalepUpdateResponse> UpdateIadeTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebIadeTalepGuncelleRequest IadeTalebi)
        {
            return base.Channel.UpdateIadeTalebiAsync(UyeKodu, IadeTalebi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepResponse> SelectDegisimTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepSelectRequest DegisimTalepRequest)
        {
            return base.Channel.SelectDegisimTalebiAsync(UyeKodu, DegisimTalepRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepUrunResponse> SelectDegisimTalebiUrunAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepUrunSelectRequest DegisimTalepUrunRequest)
        {
            return base.Channel.SelectDegisimTalebiUrunAsync(UyeKodu, DegisimTalepUrunRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebDegisimTalepUpdateResponse> UpdateDegisimTalebiAsync(string UyeKodu, PushDashboard.CustomServis.WebDegisimTalepGuncelleRequest DegisimTalep)
        {
            return base.Channel.UpdateDegisimTalebiAsync(UyeKodu, DegisimTalep);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebTaksitSecenekResponse> GetTaksitSecenekAsync(string UyeKodu, PushDashboard.CustomServis.WebTaksitSecenekRequest request)
        {
            return base.Channel.GetTaksitSecenekAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebKampanya[]> GetKampanyaAsync(string UyeKodu, int KampanyaId)
        {
            return base.Channel.GetKampanyaAsync(UyeKodu, KampanyaId);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetFavoriUrunlerResponse> GetFavoriUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetFavoriUrunlerRequest request)
        {
            return base.Channel.GetFavoriUrunlerAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.AddFavoriUrunResponse> AddFavoriUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddFavoriUrunRequest request)
        {
            return base.Channel.AddFavoriUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveFavoriUrunResponse> RemoveFavoriUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveFavoriUrunRequest request)
        {
            return base.Channel.RemoveFavoriUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetFiyatAlarmUrunlerResponse> GetFiyatAlarmUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetFiyatAlarmUrunlerRequest request)
        {
            return base.Channel.GetFiyatAlarmUrunlerAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.AddFiyatAlarmUrunResponse> AddFiyatAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddFiyatAlarmUrunRequest request)
        {
            return base.Channel.AddFiyatAlarmUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveFiyatAlarmUrunResponse> RemoveFiyatAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveFiyatAlarmUrunRequest request)
        {
            return base.Channel.RemoveFiyatAlarmUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetStokAlarmUrunlerResponse> GetStokAlarmUrunlerAsync(string UyeKodu, PushDashboard.CustomServis.GetStokAlarmUrunlerRequest request)
        {
            return base.Channel.GetStokAlarmUrunlerAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.AddStokAlarmUrunResponse> AddStokAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.AddStokAlarmUrunRequest request)
        {
            return base.Channel.AddStokAlarmUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.RemoveStokAlarmUrunResponse> RemoveStokAlarmUrunAsync(string UyeKodu, PushDashboard.CustomServis.RemoveStokAlarmUrunRequest request)
        {
            return base.Channel.RemoveStokAlarmUrunAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetMenuResponse> GetMenuAsync(string UyeKodu, PushDashboard.CustomServis.GetMenuRequest request)
        {
            return base.Channel.GetMenuAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveMenuResponse> SaveMenuAsync(string UyeKodu, PushDashboard.CustomServis.SaveMenuRequest request)
        {
            return base.Channel.SaveMenuAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteMenuResponse> DeleteMenuAsync(string UyeKodu, PushDashboard.CustomServis.DeleteMenuRequest request)
        {
            return base.Channel.DeleteMenuAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisSaveSupportTicketResponse> SaveSupportTicketAsync(string UyeKodu, PushDashboard.CustomServis.ServisSaveSupportTicketRequest request)
        {
            return base.Channel.SaveSupportTicketAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketSubjectsResponse> GetSupportTicketSubjectAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketSubjectsRequest request)
        {
            return base.Channel.GetSupportTicketSubjectAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisSaveSupportTicketAnswerResponse> SaveSupportTicketAnswerAsync(string UyeKodu, PushDashboard.CustomServis.ServisSaveSupportTicketAnswerRequest request)
        {
            return base.Channel.SaveSupportTicketAnswerAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisUpdateTicketStatusResponse> UpdateTicketStatusAsync(string UyeKodu, PushDashboard.CustomServis.ServisUpdateTicketStatusRequest request)
        {
            return base.Channel.UpdateTicketStatusAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketsResponse> GetSupportTicketsAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketsRequest request)
        {
            return base.Channel.GetSupportTicketsAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketsResponse> GetAllSupportTicketsAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetAllSupportTicketsRequest request)
        {
            return base.Channel.GetAllSupportTicketsAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.ServisGetSupportTicketMessagesResponse> GetSupportTicketMessagesAsync(string UyeKodu, PushDashboard.CustomServis.ServisGetSupportTicketMessagesRequest request)
        {
            return base.Channel.GetSupportTicketMessagesAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetCampaingBannersResponse> GetCampaingBannersAsync(string UyeKodu, PushDashboard.CustomServis.GetCampaingBannersRequest request)
        {
            return base.Channel.GetCampaingBannersAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.WebKampanyaTicimax[]> GetKampanyaV2Async(string UyeKodu, int KampanyaId)
        {
            return base.Channel.GetKampanyaV2Async(UyeKodu, KampanyaId);
        }
        
        public System.Threading.Tasks.Task<string[]> SelectSiparisKaynaklariAsync(string UyeKodu)
        {
            return base.Channel.SelectSiparisKaynaklariAsync(UyeKodu);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectBankaResponse> SelectBankaAsync(string UyeKodu, int BankaID)
        {
            return base.Channel.SelectBankaAsync(UyeKodu, BankaID);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectSilinenUyelerResponse> SelectSilinenUyeListAsync(string UyeKodu, PushDashboard.CustomServis.SelectSilinenUyelerRequest request)
        {
            return base.Channel.SelectSilinenUyeListAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetMailListResponse> GetMailListAsync(string UyeKodu, PushDashboard.CustomServis.GetMailListRequest request)
        {
            return base.Channel.GetMailListAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveMailListResponse> SaveMailListAsync(string UyeKodu, PushDashboard.CustomServis.MailList mailList)
        {
            return base.Channel.SaveMailListAsync(UyeKodu, mailList);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveWebhookResponse> SaveWebhookAsync(string UyeKodu, PushDashboard.CustomServis.Webhook webhook)
        {
            return base.Channel.SaveWebhookAsync(UyeKodu, webhook);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectWebhookResponse> SelectWebhookAsync(string UyeKodu, PushDashboard.CustomServis.SelectWebhookRequest request)
        {
            return base.Channel.SelectWebhookAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteWebhookResponse> DeleteWebhookAsync(string UyeKodu, int webhookId)
        {
            return base.Channel.DeleteWebhookAsync(UyeKodu, webhookId);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectIadeOdemeListesiResponse> SelectIadeOdemeListesiAsync(string UyeKodu, PushDashboard.CustomServis.SelectIadeOdemeListesiRequest filtre, PushDashboard.CustomServis.WebServisSayfalama sayfalama)
        {
            return base.Channel.SelectIadeOdemeListesiAsync(UyeKodu, filtre, sayfalama);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.DeleteMailListResponse> DeleteMailListAsync(string UyeKodu, PushDashboard.CustomServis.DeleteMailListRequest request)
        {
            return base.Channel.DeleteMailListAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectOdemeBildirimiResponse> SelectOdemeBildirimiAsync(string UyeKodu, PushDashboard.CustomServis.SelectOdemeBildirimiRequest selectOdemeBildirimiRequest)
        {
            return base.Channel.SelectOdemeBildirimiAsync(UyeKodu, selectOdemeBildirimiRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SaveOdemeBildirimDurumResponse> SaveOdemeBildirimDurumAsync(string UyeKodu, PushDashboard.CustomServis.SaveOdemeBildirimDurumRequest saveOdemeBildirimRequest)
        {
            return base.Channel.SaveOdemeBildirimDurumAsync(UyeKodu, saveOdemeBildirimRequest);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.SelectMarketPlaceBilgiRespone> SelectSiparisMarketplaceBilgiAsync(string UyeKodu, PushDashboard.CustomServis.SelectMarketPlaceBilgiRequest request)
        {
            return base.Channel.SelectSiparisMarketplaceBilgiAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.CustomServis.GetDinamikFormDataResponse> GetDinamikFormDataAsync(string uyeKodu, PushDashboard.CustomServis.GetDinamikFormDataRequest request)
        {
            return base.Channel.GetDinamikFormDataAsync(uyeKodu, request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ICustomServis))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_ICustomServis))
            {
                return new System.ServiceModel.EndpointAddress("http://perlucia.ticimaxtest.com/Servis/CustomServis.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return CustomServisClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_ICustomServis);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return CustomServisClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_ICustomServis);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_ICustomServis,
        }
    }
}
