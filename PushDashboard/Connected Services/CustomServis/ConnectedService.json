{"providerId": "Microsoft.Tools.ServiceModel.Svcutil", "version": "2.1.0", "ExtendedData": {"inputs": ["https://perlucia.ticimaxtest.com/Servis/CustomServis.svc"], "namespaceMappings": ["*, PushDashboard.CustomServis"], "outputFile": "CustomServis.cs", "references": ["Microsoft.AspNetCore.Authentication.Abstractions, {Microsoft.AspNetCore.Authentication.Abstractions, 2.3.0}", "Microsoft.AspNetCore.Authorization, {Microsoft.AspNetCore.Authorization, 2.3.0}", "Microsoft.AspNetCore.Authorization.Policy, {Microsoft.AspNetCore.Authorization.Policy, 2.3.0}", "Microsoft.AspNetCore.Connections.Abstractions, {Microsoft.AspNetCore.Connections.Abstractions, 2.3.0}", "Microsoft.AspNetCore.Cryptography.Internal, {Microsoft.AspNetCore.Cryptography.Internal, 8.0.5}", "Microsoft.AspNetCore.Cryptography.KeyDerivation, {Microsoft.AspNetCore.Cryptography.KeyDerivation, 8.0.5}", "Microsoft.AspNetCore.Hosting.Abstractions, {Microsoft.AspNetCore.Hosting.Abstractions, 2.3.0}", "Microsoft.AspNetCore.Hosting.Server.Abstractions, {Microsoft.AspNetCore.Hosting.Server.Abstractions, 2.3.0}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.3.0}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.3.0}", "Microsoft.AspNetCore.Http.Connections, {Microsoft.AspNetCore.Http.Connections, 1.2.0}", "Microsoft.AspNetCore.Http.Connections.Common, {Microsoft.AspNetCore.Http.Connections.Common, 1.2.0}", "Microsoft.AspNetCore.Http.Extensions, {Microsoft.AspNetCore.Http.Extensions, 2.3.0}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 2.3.0}", "Microsoft.AspNetCore.Identity.EntityFrameworkCore, {Microsoft.AspNetCore.Identity.EntityFrameworkCore, 8.0.5}", "Microsoft.AspNetCore.Routing, {Microsoft.AspNetCore.Routing, 2.3.0}", "Microsoft.AspNetCore.Routing.Abstractions, {Microsoft.AspNetCore.Routing.Abstractions, 2.3.0}", "Microsoft.AspNetCore.SignalR, {Microsoft.AspNetCore.SignalR, 1.2.0}", "Microsoft.AspNetCore.SignalR.Common, {Microsoft.AspNetCore.SignalR.Common, 1.2.0}", "Microsoft.AspNetCore.SignalR.Core, {Microsoft.AspNetCore.SignalR.Core, 1.2.0}", "Microsoft.AspNetCore.SignalR.Protocols.Json, {Microsoft.AspNetCore.SignalR.Protocols.Json, 1.2.0}", "Microsoft.AspNetCore.WebSockets, {Microsoft.AspNetCore.WebSockets, 2.3.0}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.3.0}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 6.0.0}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 8.0.8}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 8.0.8}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 8.0.8}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 8.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 8.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 8.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 8.0.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 8.0.2}", "Microsoft.Extensions.Diagnostics.Abstractions, {Microsoft.Extensions.Diagnostics.Abstractions, 8.0.1}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 8.0.0}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 8.0.1}", "Microsoft.Extensions.Identity.Core, {Microsoft.Extensions.Identity.Core, 8.0.5}", "Microsoft.Extensions.Identity.Stores, {Microsoft.Extensions.Identity.Stores, 8.0.5}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 8.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 8.0.2}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 8.0.11}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 8.0.2}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 8.0.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.8.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.8.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 6.8.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 6.8.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.3.0}", "Newtonsoft.Json, {Newtonsoft.J<PERSON>, 11.0.2}", "Npgsql, {Npgsql, 8.0.4}", "Npgsql.EntityFrameworkCore.PostgreSQL, {Npgsql.EntityFrameworkCore.PostgreSQL, 8.0.8}", "Otp.NET, {Otp.NET, 1.4.0}", "QRCoder, {QRC<PERSON>r, 1.6.0}", "System.Formats.Asn1, {System.Formats.Asn1, 6.0.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.Pipelines, {System.IO.Pipelines, 8.0.0}", "System.Net.WebSockets.WebSocketProtocol, {System.Net.WebSockets.WebSocketProtocol, 5.1.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 4.5.0}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 6.0.1}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 6.0.1}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 8.0.0}", "System.Threading.Channels, {System.Threading.Channels, 8.0.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}", "<PERSON><PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON><PERSON>, 3.1.47}"], "targetFramework": "net8.0", "typeReuseMode": "All"}}