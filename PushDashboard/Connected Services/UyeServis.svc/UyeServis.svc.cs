//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PushDashboard.UyeServis
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeGirisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeGirisi : object
    {
        
        private bool AdminField;
        
        private string MailField;
        
        private string OtpField;
        
        private string SifreField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Admin
        {
            get
            {
                return this.AdminField;
            }
            set
            {
                this.AdminField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Otp
        {
            get
            {
                return this.OtpField;
            }
            set
            {
                this.OtpField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sifre
        {
            get
            {
                return this.SifreField;
            }
            set
            {
                this.SifreField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeGirisiSonuc", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeGirisiSonuc : object
    {
        
        private bool BasariliField;
        
        private string IsimField;
        
        private int KullaniciIDField;
        
        private string MesajField;
        
        private bool OtpZorunluField;
        
        private string[] RollerField;
        
        private bool SifreSifirlaField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Basarili
        {
            get
            {
                return this.BasariliField;
            }
            set
            {
                this.BasariliField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KullaniciID
        {
            get
            {
                return this.KullaniciIDField;
            }
            set
            {
                this.KullaniciIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mesaj
        {
            get
            {
                return this.MesajField;
            }
            set
            {
                this.MesajField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool OtpZorunlu
        {
            get
            {
                return this.OtpZorunluField;
            }
            set
            {
                this.OtpZorunluField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string[] Roller
        {
            get
            {
                return this.RollerField;
            }
            set
            {
                this.RollerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SifreSifirla
        {
            get
            {
                return this.SifreSifirlaField;
            }
            set
            {
                this.SifreSifirlaField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeFiltre : object
    {
        
        private int AktifField;
        
        private int AlisverisYaptiField;
        
        private System.Nullable<bool> BakiyeGetirField;
        
        private int CinsiyetField;
        
        private System.Nullable<System.DateTime> DogumTarihi1Field;
        
        private System.Nullable<System.DateTime> DogumTarihi2Field;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihi1Field;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihi2Field;
        
        private int IlIDField;
        
        private int IlceIDField;
        
        private System.Nullable<System.DateTime> IzinGuncellemeTarihi1Field;
        
        private System.Nullable<System.DateTime> IzinGuncellemeTarihi2Field;
        
        private System.Nullable<System.DateTime> IzinGuncellemeTarihiBasField;
        
        private System.Nullable<bool> IzinGuncellemeTarihiGetirField;
        
        private System.Nullable<System.DateTime> IzinGuncellemeTarihiSonField;
        
        private string MailField;
        
        private int MailIzinField;
        
        private string MusteriKoduField;
        
        private System.Nullable<bool> OnayField;
        
        private int SmsIzinField;
        
        private System.Nullable<System.DateTime> SonGirisTarihi1Field;
        
        private System.Nullable<System.DateTime> SonGirisTarihi2Field;
        
        private string TelefonField;
        
        private string TelefonEsitField;
        
        private int UyeIDField;
        
        private System.Nullable<System.DateTime> UyelikTarihi1Field;
        
        private System.Nullable<System.DateTime> UyelikTarihi2Field;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int AlisverisYapti
        {
            get
            {
                return this.AlisverisYaptiField;
            }
            set
            {
                this.AlisverisYaptiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> BakiyeGetir
        {
            get
            {
                return this.BakiyeGetirField;
            }
            set
            {
                this.BakiyeGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Cinsiyet
        {
            get
            {
                return this.CinsiyetField;
            }
            set
            {
                this.CinsiyetField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DogumTarihi1
        {
            get
            {
                return this.DogumTarihi1Field;
            }
            set
            {
                this.DogumTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DogumTarihi2
        {
            get
            {
                return this.DogumTarihi2Field;
            }
            set
            {
                this.DogumTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihi1
        {
            get
            {
                return this.DuzenlemeTarihi1Field;
            }
            set
            {
                this.DuzenlemeTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihi2
        {
            get
            {
                return this.DuzenlemeTarihi2Field;
            }
            set
            {
                this.DuzenlemeTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceID
        {
            get
            {
                return this.IlceIDField;
            }
            set
            {
                this.IlceIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IzinGuncellemeTarihi1
        {
            get
            {
                return this.IzinGuncellemeTarihi1Field;
            }
            set
            {
                this.IzinGuncellemeTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IzinGuncellemeTarihi2
        {
            get
            {
                return this.IzinGuncellemeTarihi2Field;
            }
            set
            {
                this.IzinGuncellemeTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IzinGuncellemeTarihiBas
        {
            get
            {
                return this.IzinGuncellemeTarihiBasField;
            }
            set
            {
                this.IzinGuncellemeTarihiBasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> IzinGuncellemeTarihiGetir
        {
            get
            {
                return this.IzinGuncellemeTarihiGetirField;
            }
            set
            {
                this.IzinGuncellemeTarihiGetirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> IzinGuncellemeTarihiSon
        {
            get
            {
                return this.IzinGuncellemeTarihiSonField;
            }
            set
            {
                this.IzinGuncellemeTarihiSonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MusteriKodu
        {
            get
            {
                return this.MusteriKoduField;
            }
            set
            {
                this.MusteriKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> Onay
        {
            get
            {
                return this.OnayField;
            }
            set
            {
                this.OnayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SmsIzin
        {
            get
            {
                return this.SmsIzinField;
            }
            set
            {
                this.SmsIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SonGirisTarihi1
        {
            get
            {
                return this.SonGirisTarihi1Field;
            }
            set
            {
                this.SonGirisTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> SonGirisTarihi2
        {
            get
            {
                return this.SonGirisTarihi2Field;
            }
            set
            {
                this.SonGirisTarihi2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TelefonEsit
        {
            get
            {
                return this.TelefonEsitField;
            }
            set
            {
                this.TelefonEsitField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UyelikTarihi1
        {
            get
            {
                return this.UyelikTarihi1Field;
            }
            set
            {
                this.UyelikTarihi1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> UyelikTarihi2
        {
            get
            {
                return this.UyelikTarihi2Field;
            }
            set
            {
                this.UyelikTarihi2Field = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeSayfalama", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeSayfalama : object
    {
        
        private int KayitSayisiField;
        
        private int SayfaNoField;
        
        private string SiralamaDegeriField;
        
        private string SiralamaYonuField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int KayitSayisi
        {
            get
            {
                return this.KayitSayisiField;
            }
            set
            {
                this.KayitSayisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SayfaNo
        {
            get
            {
                return this.SayfaNoField;
            }
            set
            {
                this.SayfaNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaDegeri
        {
            get
            {
                return this.SiralamaDegeriField;
            }
            set
            {
                this.SiralamaDegeriField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SiralamaYonu
        {
            get
            {
                return this.SiralamaYonuField;
            }
            set
            {
                this.SiralamaYonuField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Uye", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class Uye : object
    {
        
        private bool AktifField;
        
        private System.Nullable<bool> AlisverissizOdemeField;
        
        private PushDashboard.UyeServis.UyeBakiyeBilgisi[] BakiyeBilgisiField;
        
        private string CepTelefonuField;
        
        private PushDashboard.UyeServis.BLKullaniciCihazBilgisi CihazBilgisiField;
        
        private int CinsiyetIDField;
        
        private System.DateTime DogumTarihiField;
        
        private System.DateTime DuzenlemeTarihiField;
        
        private int IDField;
        
        private string IlField;
        
        private int IlIDField;
        
        private string IlceField;
        
        private int IlceIDField;
        
        private string IsimField;
        
        private System.DateTime IzinGuncellemeTarihiField;
        
        private bool KVKKSozlesmeOnayField;
        
        private bool KapidaOdemeYasaklaField;
        
        private double KrediLimitiField;
        
        private string MailField;
        
        private bool MailIzinField;
        
        private string MeslekField;
        
        private string MusteriKoduField;
        
        private string OgrenimDurumuField;
        
        private bool OnayField;
        
        private System.DateTime OnayTarihiField;
        
        private int ParaPuanField;
        
        private string SifreField;
        
        private bool SmsIzinField;
        
        private string SonGirisIpField;
        
        private System.DateTime SonGirisTarihiField;
        
        private string SoyisimField;
        
        private PushDashboard.UyeServis.UyeSozlesmeBilgisi[] SozlesmeBilgisiField;
        
        private string TelefonField;
        
        private string UyeTuruField;
        
        private int UyeTuruIDField;
        
        private int UyelikKaynagiField;
        
        private bool UyelikSozlesmeOnayField;
        
        private System.DateTime UyelikTarihiField;
        
        private string UyelikTipiField;
        
        private int UyelikTipiIDField;
        
        private string VKayitDilField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> AlisverissizOdeme
        {
            get
            {
                return this.AlisverissizOdemeField;
            }
            set
            {
                this.AlisverissizOdemeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.UyeBakiyeBilgisi[] BakiyeBilgisi
        {
            get
            {
                return this.BakiyeBilgisiField;
            }
            set
            {
                this.BakiyeBilgisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CepTelefonu
        {
            get
            {
                return this.CepTelefonuField;
            }
            set
            {
                this.CepTelefonuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.BLKullaniciCihazBilgisi CihazBilgisi
        {
            get
            {
                return this.CihazBilgisiField;
            }
            set
            {
                this.CihazBilgisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int CinsiyetID
        {
            get
            {
                return this.CinsiyetIDField;
            }
            set
            {
                this.CinsiyetIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DogumTarihi
        {
            get
            {
                return this.DogumTarihiField;
            }
            set
            {
                this.DogumTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DuzenlemeTarihi
        {
            get
            {
                return this.DuzenlemeTarihiField;
            }
            set
            {
                this.DuzenlemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Il
        {
            get
            {
                return this.IlField;
            }
            set
            {
                this.IlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlID
        {
            get
            {
                return this.IlIDField;
            }
            set
            {
                this.IlIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IlceID
        {
            get
            {
                return this.IlceIDField;
            }
            set
            {
                this.IlceIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Isim
        {
            get
            {
                return this.IsimField;
            }
            set
            {
                this.IsimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime IzinGuncellemeTarihi
        {
            get
            {
                return this.IzinGuncellemeTarihiField;
            }
            set
            {
                this.IzinGuncellemeTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KVKKSozlesmeOnay
        {
            get
            {
                return this.KVKKSozlesmeOnayField;
            }
            set
            {
                this.KVKKSozlesmeOnayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KapidaOdemeYasakla
        {
            get
            {
                return this.KapidaOdemeYasaklaField;
            }
            set
            {
                this.KapidaOdemeYasaklaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double KrediLimiti
        {
            get
            {
                return this.KrediLimitiField;
            }
            set
            {
                this.KrediLimitiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Meslek
        {
            get
            {
                return this.MeslekField;
            }
            set
            {
                this.MeslekField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MusteriKodu
        {
            get
            {
                return this.MusteriKoduField;
            }
            set
            {
                this.MusteriKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OgrenimDurumu
        {
            get
            {
                return this.OgrenimDurumuField;
            }
            set
            {
                this.OgrenimDurumuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Onay
        {
            get
            {
                return this.OnayField;
            }
            set
            {
                this.OnayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime OnayTarihi
        {
            get
            {
                return this.OnayTarihiField;
            }
            set
            {
                this.OnayTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ParaPuan
        {
            get
            {
                return this.ParaPuanField;
            }
            set
            {
                this.ParaPuanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sifre
        {
            get
            {
                return this.SifreField;
            }
            set
            {
                this.SifreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsIzin
        {
            get
            {
                return this.SmsIzinField;
            }
            set
            {
                this.SmsIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SonGirisIp
        {
            get
            {
                return this.SonGirisIpField;
            }
            set
            {
                this.SonGirisIpField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime SonGirisTarihi
        {
            get
            {
                return this.SonGirisTarihiField;
            }
            set
            {
                this.SonGirisTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Soyisim
        {
            get
            {
                return this.SoyisimField;
            }
            set
            {
                this.SoyisimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.UyeSozlesmeBilgisi[] SozlesmeBilgisi
        {
            get
            {
                return this.SozlesmeBilgisiField;
            }
            set
            {
                this.SozlesmeBilgisiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyeTuru
        {
            get
            {
                return this.UyeTuruField;
            }
            set
            {
                this.UyeTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeTuruID
        {
            get
            {
                return this.UyeTuruIDField;
            }
            set
            {
                this.UyeTuruIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyelikKaynagi
        {
            get
            {
                return this.UyelikKaynagiField;
            }
            set
            {
                this.UyelikKaynagiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyelikSozlesmeOnay
        {
            get
            {
                return this.UyelikSozlesmeOnayField;
            }
            set
            {
                this.UyelikSozlesmeOnayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime UyelikTarihi
        {
            get
            {
                return this.UyelikTarihiField;
            }
            set
            {
                this.UyelikTarihiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UyelikTipi
        {
            get
            {
                return this.UyelikTipiField;
            }
            set
            {
                this.UyelikTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyelikTipiID
        {
            get
            {
                return this.UyelikTipiIDField;
            }
            set
            {
                this.UyelikTipiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VKayitDil
        {
            get
            {
                return this.VKayitDilField;
            }
            set
            {
                this.VKayitDilField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLKullaniciCihazBilgisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class BLKullaniciCihazBilgisi : object
    {
        
        private PushDashboard.UyeServis.BLEnumsBrowserType BrowserField;
        
        private string BrowserStrField;
        
        private bool IsMobileDeviceField;
        
        private PushDashboard.UyeServis.BLEnumsPlatformType PlatformField;
        
        private string PlatformStrField;
        
        private PushDashboard.UyeServis.BLEnumsTicimaxMobilAppType TicimaxMobilAppField;
        
        private string TicimaxMobilAppStrField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.BLEnumsBrowserType Browser
        {
            get
            {
                return this.BrowserField;
            }
            set
            {
                this.BrowserField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BrowserStr
        {
            get
            {
                return this.BrowserStrField;
            }
            set
            {
                this.BrowserStrField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsMobileDevice
        {
            get
            {
                return this.IsMobileDeviceField;
            }
            set
            {
                this.IsMobileDeviceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.BLEnumsPlatformType Platform
        {
            get
            {
                return this.PlatformField;
            }
            set
            {
                this.PlatformField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PlatformStr
        {
            get
            {
                return this.PlatformStrField;
            }
            set
            {
                this.PlatformStrField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.BLEnumsTicimaxMobilAppType TicimaxMobilApp
        {
            get
            {
                return this.TicimaxMobilAppField;
            }
            set
            {
                this.TicimaxMobilAppField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TicimaxMobilAppStr
        {
            get
            {
                return this.TicimaxMobilAppStrField;
            }
            set
            {
                this.TicimaxMobilAppStrField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeBakiyeBilgisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeBakiyeBilgisi : object
    {
        
        private double BakiyeField;
        
        private int BlokeDurumField;
        
        private string ParaBirimiField;
        
        private System.DateTime TarihField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Bakiye
        {
            get
            {
                return this.BakiyeField;
            }
            set
            {
                this.BakiyeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int BlokeDurum
        {
            get
            {
                return this.BlokeDurumField;
            }
            set
            {
                this.BlokeDurumField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeSozlesmeBilgisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeSozlesmeBilgisi : object
    {
        
        private int SozlesmeIdField;
        
        private string SozlesmeTipiField;
        
        private int SozlesmeTipiIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SozlesmeId
        {
            get
            {
                return this.SozlesmeIdField;
            }
            set
            {
                this.SozlesmeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SozlesmeTipi
        {
            get
            {
                return this.SozlesmeTipiField;
            }
            set
            {
                this.SozlesmeTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SozlesmeTipiId
        {
            get
            {
                return this.SozlesmeTipiIdField;
            }
            set
            {
                this.SozlesmeTipiIdField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLEnums.BrowserType", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public enum BLEnumsBrowserType : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Other = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InternetExplorer = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Chrome = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Opera = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Safari = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        FireFox = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Edge = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Yandex = 7,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLEnums.PlatformType", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public enum BLEnumsPlatformType : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Other = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        iOS = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Android = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        BlackBerry = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        WindowsPhone = 4,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BLEnums.TicimaxMobilAppType", Namespace="http://schemas.datacontract.org/2004/07/Ticimax.BL")]
    public enum BLEnumsTicimaxMobilAppType : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        None = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Android = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        iOS = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NativeAndroid = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NativeIOS = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeAyar", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeAyar : object
    {
        
        private bool AlisverissizOdemeGuncelleField;
        
        private bool CepTelefonuGuncelleField;
        
        private bool CinsiyetGuncelleField;
        
        private bool DogumTarihiGuncelleField;
        
        private bool IlGuncelleField;
        
        private bool IlceGuncelleField;
        
        private bool IsimGuncelleField;
        
        private bool KVKKSozlesmeOnayGuncelleField;
        
        private bool KapidaOdemeYasaklaGuncelleField;
        
        private bool KrediLimitiGuncelleField;
        
        private bool MailGuncelleField;
        
        private bool MailIzinGuncelleField;
        
        private bool MeslekGuncelleField;
        
        private bool MusteriKoduGuncelleField;
        
        private bool SifreGuncelleField;
        
        private System.Nullable<PushDashboard.UyeServis.UyeSifreKaydetmeTuru> SifreKaydetmeTuruField;
        
        private bool SmsIzinGuncelleField;
        
        private bool TelefonGuncelleField;
        
        private bool UyeSifreyiKendiOlustursunField;
        
        private bool UyelikSozlesmeOnayGuncelleField;
        
        private bool UyelikTarihiGuncelleField;
        
        private bool UyelikTuruGuncelleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool AlisverissizOdemeGuncelle
        {
            get
            {
                return this.AlisverissizOdemeGuncelleField;
            }
            set
            {
                this.AlisverissizOdemeGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CepTelefonuGuncelle
        {
            get
            {
                return this.CepTelefonuGuncelleField;
            }
            set
            {
                this.CepTelefonuGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool CinsiyetGuncelle
        {
            get
            {
                return this.CinsiyetGuncelleField;
            }
            set
            {
                this.CinsiyetGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DogumTarihiGuncelle
        {
            get
            {
                return this.DogumTarihiGuncelleField;
            }
            set
            {
                this.DogumTarihiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IlGuncelle
        {
            get
            {
                return this.IlGuncelleField;
            }
            set
            {
                this.IlGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IlceGuncelle
        {
            get
            {
                return this.IlceGuncelleField;
            }
            set
            {
                this.IlceGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsimGuncelle
        {
            get
            {
                return this.IsimGuncelleField;
            }
            set
            {
                this.IsimGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KVKKSozlesmeOnayGuncelle
        {
            get
            {
                return this.KVKKSozlesmeOnayGuncelleField;
            }
            set
            {
                this.KVKKSozlesmeOnayGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KapidaOdemeYasaklaGuncelle
        {
            get
            {
                return this.KapidaOdemeYasaklaGuncelleField;
            }
            set
            {
                this.KapidaOdemeYasaklaGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool KrediLimitiGuncelle
        {
            get
            {
                return this.KrediLimitiGuncelleField;
            }
            set
            {
                this.KrediLimitiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailGuncelle
        {
            get
            {
                return this.MailGuncelleField;
            }
            set
            {
                this.MailGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MailIzinGuncelle
        {
            get
            {
                return this.MailIzinGuncelleField;
            }
            set
            {
                this.MailIzinGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MeslekGuncelle
        {
            get
            {
                return this.MeslekGuncelleField;
            }
            set
            {
                this.MeslekGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool MusteriKoduGuncelle
        {
            get
            {
                return this.MusteriKoduGuncelleField;
            }
            set
            {
                this.MusteriKoduGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SifreGuncelle
        {
            get
            {
                return this.SifreGuncelleField;
            }
            set
            {
                this.SifreGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<PushDashboard.UyeServis.UyeSifreKaydetmeTuru> SifreKaydetmeTuru
        {
            get
            {
                return this.SifreKaydetmeTuruField;
            }
            set
            {
                this.SifreKaydetmeTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool SmsIzinGuncelle
        {
            get
            {
                return this.SmsIzinGuncelleField;
            }
            set
            {
                this.SmsIzinGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool TelefonGuncelle
        {
            get
            {
                return this.TelefonGuncelleField;
            }
            set
            {
                this.TelefonGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyeSifreyiKendiOlustursun
        {
            get
            {
                return this.UyeSifreyiKendiOlustursunField;
            }
            set
            {
                this.UyeSifreyiKendiOlustursunField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyelikSozlesmeOnayGuncelle
        {
            get
            {
                return this.UyelikSozlesmeOnayGuncelleField;
            }
            set
            {
                this.UyelikSozlesmeOnayGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyelikTarihiGuncelle
        {
            get
            {
                return this.UyelikTarihiGuncelleField;
            }
            set
            {
                this.UyelikTarihiGuncelleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool UyelikTuruGuncelle
        {
            get
            {
                return this.UyelikTuruGuncelleField;
            }
            set
            {
                this.UyelikTuruGuncelleField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeSifreKaydetmeTuru", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum UyeSifreKaydetmeTuru : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Plain = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Hash = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeAdres", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeAdres : object
    {
        
        private string AdresField;
        
        private string AdresTarifiField;
        
        private bool AktifField;
        
        private string AliciAdiField;
        
        private string AliciTelefonField;
        
        private string FirmaAdiField;
        
        private int IDField;
        
        private string IlceField;
        
        private bool IsKurumsalField;
        
        private string PostaKoduField;
        
        private string SehirField;
        
        private string TanimField;
        
        private string UlkeField;
        
        private int UyeIdField;
        
        private string VergiDairesiField;
        
        private string VergiNoField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adres
        {
            get
            {
                return this.AdresField;
            }
            set
            {
                this.AdresField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdresTarifi
        {
            get
            {
                return this.AdresTarifiField;
            }
            set
            {
                this.AdresTarifiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Aktif
        {
            get
            {
                return this.AktifField;
            }
            set
            {
                this.AktifField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciAdi
        {
            get
            {
                return this.AliciAdiField;
            }
            set
            {
                this.AliciAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AliciTelefon
        {
            get
            {
                return this.AliciTelefonField;
            }
            set
            {
                this.AliciTelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FirmaAdi
        {
            get
            {
                return this.FirmaAdiField;
            }
            set
            {
                this.FirmaAdiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ilce
        {
            get
            {
                return this.IlceField;
            }
            set
            {
                this.IlceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsKurumsal
        {
            get
            {
                return this.IsKurumsalField;
            }
            set
            {
                this.IsKurumsalField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PostaKodu
        {
            get
            {
                return this.PostaKoduField;
            }
            set
            {
                this.PostaKoduField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Sehir
        {
            get
            {
                return this.SehirField;
            }
            set
            {
                this.SehirField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Ulke
        {
            get
            {
                return this.UlkeField;
            }
            set
            {
                this.UlkeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiDairesi
        {
            get
            {
                return this.VergiDairesiField;
            }
            set
            {
                this.VergiDairesiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string VergiNo
        {
            get
            {
                return this.VergiNoField;
            }
            set
            {
                this.VergiNoField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeTuru", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeTuru : object
    {
        
        private PushDashboard.UyeServis.FiyatTuru FiyatTuruField;
        
        private int IDField;
        
        private int ParaBirimiIDField;
        
        private string TanimField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.FiyatTuru FiyatTuru
        {
            get
            {
                return this.FiyatTuruField;
            }
            set
            {
                this.FiyatTuruField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ParaBirimiID
        {
            get
            {
                return this.ParaBirimiIDField;
            }
            set
            {
                this.ParaBirimiIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tanim
        {
            get
            {
                return this.TanimField;
            }
            set
            {
                this.TanimField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="FiyatTuru", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum FiyatTuru : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SatisFiyati = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati1 = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati2 = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati3 = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati4 = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati5 = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati6 = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati7 = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati8 = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati9 = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati10 = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati11 = 11,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati12 = 12,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati13 = 13,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati14 = 14,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati15 = 15,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati16 = 16,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati17 = 17,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati18 = 18,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati19 = 19,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UyeTipiFiyati20 = 20,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebServisResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.UyeServis.UyeIzinGecmisiResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.UyeServis.UpdateUyeIzinResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.UyeServis.ParaPuanGuncelleResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.UyeServis.UpdateBakiyeResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(PushDashboard.UyeServis.UyeSilResponse))]
    public partial class WebServisResponse : object
    {
        
        private int ErrorCodeField;
        
        private string ErrorMessageField;
        
        private bool IsErrorField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsError
        {
            get
            {
                return this.IsErrorField;
            }
            set
            {
                this.IsErrorField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeIzinGecmisiResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeIzinGecmisiResponse : PushDashboard.UyeServis.WebServisResponse
    {
        
        private PushDashboard.UyeServis.UyeIzinGecmisi[] IzinGecmisiListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.UyeIzinGecmisi[] IzinGecmisiList
        {
            get
            {
                return this.IzinGecmisiListField;
            }
            set
            {
                this.IzinGecmisiListField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateUyeIzinResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateUyeIzinResponse : PushDashboard.UyeServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ParaPuanGuncelleResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ParaPuanGuncelleResponse : PushDashboard.UyeServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateBakiyeResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateBakiyeResponse : PushDashboard.UyeServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeSilResponse", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeSilResponse : PushDashboard.UyeServis.WebServisResponse
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeIzinGecmisi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeIzinGecmisi : object
    {
        
        private string CepTelefonuField;
        
        private bool DegerField;
        
        private int Duzenleyen_IdField;
        
        private int IdField;
        
        private int IzinTipField;
        
        private string KaynakField;
        
        private string MailAddressField;
        
        private System.DateTime TarihField;
        
        private string TelefonField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CepTelefonu
        {
            get
            {
                return this.CepTelefonuField;
            }
            set
            {
                this.CepTelefonuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Deger
        {
            get
            {
                return this.DegerField;
            }
            set
            {
                this.DegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Duzenleyen_Id
        {
            get
            {
                return this.Duzenleyen_IdField;
            }
            set
            {
                this.Duzenleyen_IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IzinTip
        {
            get
            {
                return this.IzinTipField;
            }
            set
            {
                this.IzinTipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kaynak
        {
            get
            {
                return this.KaynakField;
            }
            set
            {
                this.KaynakField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MailAddress
        {
            get
            {
                return this.MailAddressField;
            }
            set
            {
                this.MailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Telefon
        {
            get
            {
                return this.TelefonField;
            }
            set
            {
                this.TelefonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UyeIzinGecmisiFiltre", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UyeIzinGecmisiFiltre : object
    {
        
        private System.Nullable<int> DegerField;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihBaslangicField;
        
        private System.Nullable<System.DateTime> DuzenlemeTarihBitisField;
        
        private int Duzenleyen_IdField;
        
        private int IdField;
        
        private int IzinTipField;
        
        private string KaynakField;
        
        private int UyeIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> Deger
        {
            get
            {
                return this.DegerField;
            }
            set
            {
                this.DegerField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihBaslangic
        {
            get
            {
                return this.DuzenlemeTarihBaslangicField;
            }
            set
            {
                this.DuzenlemeTarihBaslangicField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DuzenlemeTarihBitis
        {
            get
            {
                return this.DuzenlemeTarihBitisField;
            }
            set
            {
                this.DuzenlemeTarihBitisField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Duzenleyen_Id
        {
            get
            {
                return this.Duzenleyen_IdField;
            }
            set
            {
                this.Duzenleyen_IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IzinTip
        {
            get
            {
                return this.IzinTipField;
            }
            set
            {
                this.IzinTipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Kaynak
        {
            get
            {
                return this.KaynakField;
            }
            set
            {
                this.KaynakField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeId
        {
            get
            {
                return this.UyeIdField;
            }
            set
            {
                this.UyeIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateUyeIzinRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateUyeIzinRequest : object
    {
        
        private string MailField;
        
        private System.Nullable<bool> MailIzinField;
        
        private System.Nullable<bool> SmsIzinField;
        
        private System.Nullable<System.DateTime> TarihField;
        
        private string TelefonNoField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Mail
        {
            get
            {
                return this.MailField;
            }
            set
            {
                this.MailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> MailIzin
        {
            get
            {
                return this.MailIzinField;
            }
            set
            {
                this.MailIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> SmsIzin
        {
            get
            {
                return this.SmsIzinField;
            }
            set
            {
                this.SmsIzinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> Tarih
        {
            get
            {
                return this.TarihField;
            }
            set
            {
                this.TarihField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TelefonNo
        {
            get
            {
                return this.TelefonNoField;
            }
            set
            {
                this.TelefonNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ParaPuanGuncelleRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class ParaPuanGuncelleRequest : object
    {
        
        private PushDashboard.UyeServis.ParaPuanGuncelleIslemTipi IslemTipiField;
        
        private int ParaPuanField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.ParaPuanGuncelleIslemTipi IslemTipi
        {
            get
            {
                return this.IslemTipiField;
            }
            set
            {
                this.IslemTipiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ParaPuan
        {
            get
            {
                return this.ParaPuanField;
            }
            set
            {
                this.ParaPuanField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ParaPuanGuncelleIslemTipi", Namespace="http://schemas.datacontract.org/2004/07/")]
    public enum ParaPuanGuncelleIslemTipi : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Ekle = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Cikar = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="UpdateBakiyeRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class UpdateBakiyeRequest : object
    {
        
        private string AciklamaField;
        
        private double BakiyeField;
        
        private int BlokeField;
        
        private string ParaBirimiField;
        
        private int UyeIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Aciklama
        {
            get
            {
                return this.AciklamaField;
            }
            set
            {
                this.AciklamaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public double Bakiye
        {
            get
            {
                return this.BakiyeField;
            }
            set
            {
                this.BakiyeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Bloke
        {
            get
            {
                return this.BlokeField;
            }
            set
            {
                this.BlokeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParaBirimi
        {
            get
            {
                return this.ParaBirimiField;
            }
            set
            {
                this.ParaBirimiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int UyeID
        {
            get
            {
                return this.UyeIDField;
            }
            set
            {
                this.UyeIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BulkUpdateUyeIzinRequest", Namespace="http://schemas.datacontract.org/2004/07/")]
    public partial class BulkUpdateUyeIzinRequest : object
    {
        
        private PushDashboard.UyeServis.UpdateUyeIzinRequest[] UpdateUyeIzinListField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public PushDashboard.UyeServis.UpdateUyeIzinRequest[] UpdateUyeIzinList
        {
            get
            {
                return this.UpdateUyeIzinListField;
            }
            set
            {
                this.UpdateUyeIzinListField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="PushDashboard.UyeServis.IUyeServis")]
    public interface IUyeServis
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/GirisYap", ReplyAction="http://tempuri.org/IUyeServis/GirisYapResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeGirisiSonuc> GirisYapAsync(PushDashboard.UyeServis.UyeGirisi ug);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SelectUyeler", ReplyAction="http://tempuri.org/IUyeServis/SelectUyelerResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.Uye[]> SelectUyelerAsync(string UyeKodu, PushDashboard.UyeServis.UyeFiltre filtre, PushDashboard.UyeServis.UyeSayfalama sayfalama);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SelectUyeIdByMailOrTel", ReplyAction="http://tempuri.org/IUyeServis/SelectUyeIdByMailOrTelResponse")]
        System.Threading.Tasks.Task<int[]> SelectUyeIdByMailOrTelAsync(string UyeKodu, string uyeMail, string tel, System.Nullable<int> uyelikTipi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SaveUye", ReplyAction="http://tempuri.org/IUyeServis/SaveUyeResponse")]
        System.Threading.Tasks.Task<int> SaveUyeAsync(string UyeKodu, PushDashboard.UyeServis.Uye u, PushDashboard.UyeServis.UyeAyar ayar);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SelectUyeAdres", ReplyAction="http://tempuri.org/IUyeServis/SelectUyeAdresResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeAdres[]> SelectUyeAdresAsync(string UyeKodu, int adresId, int uyeId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SaveUyeAdres", ReplyAction="http://tempuri.org/IUyeServis/SaveUyeAdresResponse")]
        System.Threading.Tasks.Task<int> SaveUyeAdresAsync(string UyeKodu, PushDashboard.UyeServis.UyeAdres adres);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SelectUyeTuru", ReplyAction="http://tempuri.org/IUyeServis/SelectUyeTuruResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeTuru[]> SelectUyeTuruAsync(string UyeKodu, int uyeTuruId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SaveUyeTuru", ReplyAction="http://tempuri.org/IUyeServis/SaveUyeTuruResponse")]
        System.Threading.Tasks.Task<int> SaveUyeTuruAsync(string UyeKodu, PushDashboard.UyeServis.UyeTuru u);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/DeleteUye", ReplyAction="http://tempuri.org/IUyeServis/DeleteUyeResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeSilResponse> DeleteUyeAsync(string UyeKodu, int uyeId, string mailAdresi);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/SelectUyeIzinGecmisi", ReplyAction="http://tempuri.org/IUyeServis/SelectUyeIzinGecmisiResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeIzinGecmisiResponse> SelectUyeIzinGecmisiAsync(string UyeKodu, PushDashboard.UyeServis.UyeIzinGecmisiFiltre filtre);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/UpdateUyeIzin", ReplyAction="http://tempuri.org/IUyeServis/UpdateUyeIzinResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateUyeIzinResponse> UpdateUyeIzinAsync(string UyeKodu, PushDashboard.UyeServis.UpdateUyeIzinRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/ParaPuanGucelle", ReplyAction="http://tempuri.org/IUyeServis/ParaPuanGucelleResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.ParaPuanGuncelleResponse> ParaPuanGucelleAsync(string UyeKodu, PushDashboard.UyeServis.ParaPuanGuncelleRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/UpdateBakiye", ReplyAction="http://tempuri.org/IUyeServis/UpdateBakiyeResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateBakiyeResponse> UpdateBakiyeAsync(string UyeKodu, PushDashboard.UyeServis.UpdateBakiyeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IUyeServis/BulkUpdateUyeIzin", ReplyAction="http://tempuri.org/IUyeServis/BulkUpdateUyeIzinResponse")]
        System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateUyeIzinResponse> BulkUpdateUyeIzinAsync(string UyeKodu, PushDashboard.UyeServis.BulkUpdateUyeIzinRequest request);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface IUyeServisChannel : PushDashboard.UyeServis.IUyeServis, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class UyeServisClient : System.ServiceModel.ClientBase<PushDashboard.UyeServis.IUyeServis>, PushDashboard.UyeServis.IUyeServis
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public UyeServisClient() : 
                base(UyeServisClient.GetDefaultBinding(), UyeServisClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_IUyeServis.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public UyeServisClient(EndpointConfiguration endpointConfiguration) : 
                base(UyeServisClient.GetBindingForEndpoint(endpointConfiguration), UyeServisClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public UyeServisClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(UyeServisClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public UyeServisClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(UyeServisClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public UyeServisClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeGirisiSonuc> GirisYapAsync(PushDashboard.UyeServis.UyeGirisi ug)
        {
            return base.Channel.GirisYapAsync(ug);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.Uye[]> SelectUyelerAsync(string UyeKodu, PushDashboard.UyeServis.UyeFiltre filtre, PushDashboard.UyeServis.UyeSayfalama sayfalama)
        {
            return base.Channel.SelectUyelerAsync(UyeKodu, filtre, sayfalama);
        }
        
        public System.Threading.Tasks.Task<int[]> SelectUyeIdByMailOrTelAsync(string UyeKodu, string uyeMail, string tel, System.Nullable<int> uyelikTipi)
        {
            return base.Channel.SelectUyeIdByMailOrTelAsync(UyeKodu, uyeMail, tel, uyelikTipi);
        }
        
        public System.Threading.Tasks.Task<int> SaveUyeAsync(string UyeKodu, PushDashboard.UyeServis.Uye u, PushDashboard.UyeServis.UyeAyar ayar)
        {
            return base.Channel.SaveUyeAsync(UyeKodu, u, ayar);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeAdres[]> SelectUyeAdresAsync(string UyeKodu, int adresId, int uyeId)
        {
            return base.Channel.SelectUyeAdresAsync(UyeKodu, adresId, uyeId);
        }
        
        public System.Threading.Tasks.Task<int> SaveUyeAdresAsync(string UyeKodu, PushDashboard.UyeServis.UyeAdres adres)
        {
            return base.Channel.SaveUyeAdresAsync(UyeKodu, adres);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeTuru[]> SelectUyeTuruAsync(string UyeKodu, int uyeTuruId)
        {
            return base.Channel.SelectUyeTuruAsync(UyeKodu, uyeTuruId);
        }
        
        public System.Threading.Tasks.Task<int> SaveUyeTuruAsync(string UyeKodu, PushDashboard.UyeServis.UyeTuru u)
        {
            return base.Channel.SaveUyeTuruAsync(UyeKodu, u);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeSilResponse> DeleteUyeAsync(string UyeKodu, int uyeId, string mailAdresi)
        {
            return base.Channel.DeleteUyeAsync(UyeKodu, uyeId, mailAdresi);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UyeIzinGecmisiResponse> SelectUyeIzinGecmisiAsync(string UyeKodu, PushDashboard.UyeServis.UyeIzinGecmisiFiltre filtre)
        {
            return base.Channel.SelectUyeIzinGecmisiAsync(UyeKodu, filtre);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateUyeIzinResponse> UpdateUyeIzinAsync(string UyeKodu, PushDashboard.UyeServis.UpdateUyeIzinRequest request)
        {
            return base.Channel.UpdateUyeIzinAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.ParaPuanGuncelleResponse> ParaPuanGucelleAsync(string UyeKodu, PushDashboard.UyeServis.ParaPuanGuncelleRequest request)
        {
            return base.Channel.ParaPuanGucelleAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateBakiyeResponse> UpdateBakiyeAsync(string UyeKodu, PushDashboard.UyeServis.UpdateBakiyeRequest request)
        {
            return base.Channel.UpdateBakiyeAsync(UyeKodu, request);
        }
        
        public System.Threading.Tasks.Task<PushDashboard.UyeServis.UpdateUyeIzinResponse> BulkUpdateUyeIzinAsync(string UyeKodu, PushDashboard.UyeServis.BulkUpdateUyeIzinRequest request)
        {
            return base.Channel.BulkUpdateUyeIzinAsync(UyeKodu, request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IUyeServis))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IUyeServis))
            {
                return new System.ServiceModel.EndpointAddress("http://perlucia.ticimaxtest.com/Servis/UyeServis.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return UyeServisClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_IUyeServis);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return UyeServisClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_IUyeServis);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IUyeServis,
        }
    }
}
