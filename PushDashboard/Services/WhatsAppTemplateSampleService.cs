using PushDashboard.Services.WhatsApp;

namespace PushDashboard.Services;

public interface IWhatsAppTemplateSampleService
{
    List<WhatsAppTemplateSample> GetSampleTemplates();
    WhatsAppTemplateSample? GetSampleTemplate(string id);
}

public class WhatsAppTemplateSampleService : IWhatsAppTemplateSampleService
{
    public List<WhatsAppTemplateSample> GetSampleTemplates()
    {
        return new List<WhatsAppTemplateSample>
        {
            new WhatsAppTemplateSample
            {
                Id = "welcome_message",
                Name = "İlk Alışveriş Hoş Geldin Mesajı",
                Description = "İlk alışveriş yapan müşteriler için hoş geldin mesajı",
                Category = "MARKETING",
                Language = "tr",
                Components = new List<TemplateComponent>
                {
                    new TemplateComponent
                    {
                        Type = "HEADER",
                        Text = "🎉 Hoş Geldiniz!",
                        Format = "TEXT"
                    },
                    new TemplateComponent
                    {
                        Type = "BODY",
                        Text = "Merhaba {{1}}, {{2}} ailesine hoş geldiniz! İlk alışverişiniz için teşekkür ederiz. Sizin için özel fırsatlarımızı kaçırmayın."
                    },
                    new TemplateComponent
                    {
                        Type = "FOOTER",
                        Text = "İyi alışverişler dileriz"
                    },
                    new TemplateComponent
                    {
                        Type = "BUTTONS",
                        Buttons = new List<TemplateButton>
                        {
                            new TemplateButton
                            {
                                Type = "URL",
                                Text = "Alışverişe Devam Et",
                                Url = "https://example.com/shop"
                            }
                        }
                    }
                }
            },
            new WhatsAppTemplateSample
            {
                Id = "birthday_greeting",
                Name = "Doğum Günü Kutlama Mesajı",
                Description = "Müşterilerin doğum günü için kutlama mesajı",
                Category = "MARKETING",
                Language = "tr",
                Components = new List<TemplateComponent>
                {
                    new TemplateComponent
                    {
                        Type = "HEADER",
                        Text = "🎂 Doğum Günün Kutlu Olsun!",
                        Format = "TEXT"
                    },
                    new TemplateComponent
                    {
                        Type = "BODY",
                        Text = "Sevgili {{1}}, doğum gününüz kutlu olsun! 🎉 Bu özel günde sizin için hazırladığımız sürpriz hediyeyi kaçırmayın. Size özel %{{2}} indirim fırsatı!"
                    },
                    new TemplateComponent
                    {
                        Type = "FOOTER",
                        Text = "Mutlu yıllar dileriz ❤️"
                    },
                    new TemplateComponent
                    {
                        Type = "BUTTONS",
                        Buttons = new List<TemplateButton>
                        {
                            new TemplateButton
                            {
                                Type = "URL",
                                Text = "Hediyemi Al",
                                Url = "https://example.com/birthday-gift"
                            },
                            new TemplateButton
                            {
                                Type = "QUICK_REPLY",
                                Text = "Teşekkürler"
                            }
                        }
                    }
                }
            }
        };
    }

    public WhatsAppTemplateSample? GetSampleTemplate(string id)
    {
        return GetSampleTemplates().FirstOrDefault(t => t.Id == id);
    }
}

public class WhatsAppTemplateSample
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = "MARKETING";
    public string Language { get; set; } = "tr";
    public List<TemplateComponent> Components { get; set; } = new();
}
