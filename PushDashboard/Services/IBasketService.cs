using PushDashboard.ViewModels;

namespace PushDashboard.Services;

public interface IBasketService
{
    Task<BasketIndexViewModel> GetBasketsAsync(int companyId, int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null);
    Task<BasketIndexViewModel.BasketViewModel?> GetBasketDetailsAsync(string guidBasketId, int companyId);
    Task<BasketIndexViewModel.BasketItemsPagedViewModel> GetBasketItemsPagedAsync(string guidBasketId, int companyId, int page = 1, int pageSize = 10);
    Task<List<BasketIndexViewModel.CurrencyTotalViewModel>> GetCurrencyTotalsAsync(int companyId);
    Task<List<BasketIndexViewModel.BasketViewModel>> GetBasketsByCustomerAsync(int customerId, int companyId);
    Task<(bool Success, string Message)> SyncBasketsAsync(int companyId);
    Task<List<BasketIndexViewModel.BasketSyncLogViewModel>> GetRecentSyncLogsAsync(int companyId, int count = 10);
}
