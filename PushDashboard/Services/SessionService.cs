using Microsoft.AspNetCore.SignalR;
using PushDashboard.Hubs;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using UAParser;

namespace PushDashboard.Services;

public interface ISessionService
{
    Task<UserSession> CreateSessionAsync(string userId, string sessionId, HttpContext httpContext);
    Task UpdateSessionActivityAsync(string sessionId);
    Task<List<ActiveSessionViewModel>> GetActiveSessionsAsync(string userId, string? currentSessionId = null);
    Task<bool> TerminateSessionAsync(string userId, int sessionId);
    Task<int> TerminateAllOtherSessionsAsync(string userId, string currentSessionId);
    Task NotifySessionTerminatedAsync(string userId, int sessionId);
    Task CleanupExpiredSessionsAsync();
    string GetDeviceInfo(HttpContext httpContext);
    string GetLocationFromIp(string ipAddress);
}

public class SessionService : ISessionService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<SessionService> _logger;
    private readonly IHubContext<SessionHub> _hubContext;

    public SessionService(ApplicationDbContext context, ILogger<SessionService> logger, IHubContext<SessionHub> hubContext)
    {
        _context = context;
        _logger = logger;
        _hubContext = hubContext;
    }

    public async Task<UserSession> CreateSessionAsync(string userId, string sessionId, HttpContext httpContext)
    {
        try
        {
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
            var parser = Parser.GetDefault();
            var clientInfo = parser.Parse(userAgent);
            
            var ipAddress = GetClientIpAddress(httpContext);
            var location = GetLocationFromIp(ipAddress);

            var session = new UserSession
            {
                UserId = userId,
                SessionId = sessionId,
                DeviceInfo = userAgent,
                Browser = clientInfo.UA.Family + " " + clientInfo.UA.Major,
                OperatingSystem = clientInfo.OS.Family + " " + clientInfo.OS.Major,
                IpAddress = ipAddress,
                Location = location,
                DeviceType = GetDeviceType(clientInfo),
                CreatedAt = DateTime.UtcNow,
                LastActivityAt = DateTime.UtcNow,
                IsActive = true,
                IsCurrent = true
            };

            // Mark all other sessions as not current for this user
            await _context.UserSessions
                .Where(s => s.UserId == userId && s.IsActive)
                .ExecuteUpdateAsync(s => s.SetProperty(x => x.IsCurrent, false));

            _context.UserSessions.Add(session);
            await _context.SaveChangesAsync();
            
            // Notify the user about session termination
            await NotifySessionTerminatedAsync(userId, session.Id);

            _logger.LogInformation("Created new session {SessionId} for user {UserId}", sessionId, userId);
            return session;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating session for user {UserId}", userId);
            throw;
        }
    }

    public async Task UpdateSessionActivityAsync(string sessionId)
    {
        try
        {
            await _context.UserSessions
                .Where(s => s.SessionId == sessionId && s.IsActive)
                .ExecuteUpdateAsync(s => s.SetProperty(x => x.LastActivityAt, DateTime.UtcNow));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating session activity for session {SessionId}", sessionId);
        }
    }

    public async Task<List<ActiveSessionViewModel>> GetActiveSessionsAsync(string userId, string? currentSessionId = null)
    {
        try
        {
            var sessions = await _context.UserSessions
                .Where(s => s.UserId == userId && s.IsActive)
                .OrderByDescending(s => s.LastActivityAt)
                .Select(s => new ActiveSessionViewModel
                {
                    Id = s.Id,
                    SessionId = s.SessionId,
                    DeviceInfo = s.DeviceInfo ?? "",
                    Browser = s.Browser ?? "",
                    OperatingSystem = s.OperatingSystem ?? "",
                    IpAddress = s.IpAddress ?? "",
                    Location = s.Location ?? "",
                    CreatedAt = s.CreatedAt,
                    LastActivityAt = s.LastActivityAt,
                    IsActive = s.IsActive,
                    DeviceType = s.DeviceType ?? "",
                    IsCurrent = s.IsCurrent || s.SessionId == currentSessionId
                })
                .ToListAsync();

            return sessions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active sessions for user {UserId}", userId);
            return new List<ActiveSessionViewModel>();
        }
    }

    public async Task<bool> TerminateSessionAsync(string userId, int sessionId)
    {
        try
        {
            var session = await _context.UserSessions
                .FirstOrDefaultAsync(s => s.Id == sessionId && s.UserId == userId && s.IsActive);

            if (session == null)
            {
                return false;
            }

            session.IsActive = false;
            session.EndedAt = DateTime.UtcNow;
            session.IsCurrent = false;

            await _context.SaveChangesAsync();
            
            // Notify the user about session termination
            await NotifySessionTerminatedAsync(userId, session.Id);
            _logger.LogInformation("Terminated session {SessionId} for user {UserId}", sessionId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating session {SessionId} for user {UserId}", sessionId, userId);
            return false;
        }
    }

    public async Task<int> TerminateAllOtherSessionsAsync(string userId, string currentSessionId)
    {
        try
        {
            var terminatedCount = await _context.UserSessions
                .Where(s => s.UserId == userId && s.SessionId != currentSessionId && s.IsActive)
                .ExecuteUpdateAsync(s => s
                    .SetProperty(x => x.IsActive, false)
                    .SetProperty(x => x.EndedAt, DateTime.UtcNow)
                    .SetProperty(x => x.IsCurrent, false));

            _logger.LogInformation("Terminated {Count} other sessions for user {UserId}", terminatedCount, userId);
            return terminatedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating other sessions for user {UserId}", userId);
            return 0;
        }
    }

    public async Task CleanupExpiredSessionsAsync()
    {
        try
        {
            var expiredDate = DateTime.UtcNow.AddDays(-30); // Sessions older than 30 days
            
            var expiredCount = await _context.UserSessions
                .Where(s => s.LastActivityAt < expiredDate)
                .ExecuteDeleteAsync();

            if (expiredCount > 0)
            {
                _logger.LogInformation("Cleaned up {Count} expired sessions", expiredCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired sessions");
        }
    }

    public string GetDeviceInfo(HttpContext httpContext)
    {
        var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
        var parser = Parser.GetDefault();
        var clientInfo = parser.Parse(userAgent);
        
        return $"{clientInfo.UA.Family} {clientInfo.UA.Major} - {clientInfo.OS.Family} {clientInfo.OS.Major}";
    }

    public string GetLocationFromIp(string ipAddress)
    {
        // Bu basit bir implementasyon. Gerçek uygulamada IP geolocation servisi kullanılabilir
        if (ipAddress == "127.0.0.1" || ipAddress == "::1" || ipAddress.StartsWith("192.168.") || ipAddress.StartsWith("10."))
        {
            return "Yerel Ağ";
        }
        
        // Gerçek uygulamada burada IP geolocation API'si kullanılabilir
        return "İstanbul, Türkiye";
    }

    private string GetClientIpAddress(HttpContext httpContext)
    {
        var ipAddress = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        
        if (string.IsNullOrEmpty(ipAddress))
        {
            ipAddress = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        }
        
        if (string.IsNullOrEmpty(ipAddress))
        {
            ipAddress = httpContext.Connection.RemoteIpAddress?.ToString();
        }
        
        return ipAddress ?? "Unknown";
    }

    private string GetDeviceType(ClientInfo clientInfo)
    {
        var deviceFamily = clientInfo.Device.Family.ToLower();
        
        if (deviceFamily.Contains("mobile") || deviceFamily.Contains("phone"))
        {
            return "Mobile";
        }
        
        if (deviceFamily.Contains("tablet") || deviceFamily.Contains("ipad"))
        {
            return "Tablet";
        }
        
        return "Desktop";
    }

    public async Task NotifySessionTerminatedAsync(string userId, int sessionId)
    {
        try
        {
            await _hubContext.Clients.Group($"user_{userId}")
                .SendAsync("SessionTerminated", new { sessionId, message = "Oturumunuz başka bir cihazdan sonlandırıldı." });
            
            _logger.LogInformation("Sent session termination notification to user {UserId} for session {SessionId}", userId, sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending session termination notification to user {UserId}", userId);
        }
    }
}

