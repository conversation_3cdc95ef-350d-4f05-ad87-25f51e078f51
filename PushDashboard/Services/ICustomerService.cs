using PushDashboard.Models;
using PushDashboard.ViewModels;

namespace PushDashboard.Services;

public interface ICustomerService
{
    Task<CustomerIndexViewModel> GetCustomersAsync(int companyId, int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null);
    Task<(bool Success, string Message)> SyncCustomersAsync(int companyId);
    Task<Customer?> GetCustomerByIdAsync(int id, int companyId);
    Task<List<SyncLog>> GetSyncLogsAsync(int companyId, int limit = 10);
    Task<(int TotalCustomers, int ActiveCustomers, int NewCustomers, DateTime? LastSync)> GetCustomerStatsAsync(int companyId);

    // Yeni metodlar
    Task<(bool Success, string Message, int? CustomerId)> CreateCustomerAsync(int companyId, CreateCustomerViewModel model);
    Task<(bool Success, string Message, int? JobId)> StartBulkImportAsync(int companyId, string userId, IFormFile file);
    Task<CustomerImportProgressViewModel?> GetImportProgressAsync(int jobId, int companyId);
    Task<List<CustomerImportJobViewModel>> GetImportHistoryAsync(int companyId, int limit = 10);
    byte[] GenerateExcelTemplate();
}
