using PushDashboard.ViewModels;

namespace PushDashboard.Services;

public interface IDashboardService
{
    /// <summary>
    /// Ana dashboard verilerini getirir
    /// </summary>
    Task<DashboardIndexViewModel> GetDashboardDataAsync(int companyId);

    /// <summary>
    /// Dashboard istatistiklerini getirir
    /// </summary>
    Task<DashboardStatsViewModel> GetDashboardStatsAsync(int companyId);

    /// <summary>
    /// Modül kullanım özetini getirir
    /// </summary>
    Task<List<ModuleUsageOverviewViewModel>> GetModuleUsageOverviewAsync(int companyId);

    /// <summary>
    /// Entegrasyon durumlarını getirir
    /// </summary>
    Task<List<IntegrationStatusViewModel>> GetIntegrationStatusAsync(int companyId);

    /// <summary>
    /// Son aktiviteleri getirir
    /// </summary>
    Task<List<RecentActivityViewModel>> GetRecentActivitiesAsync(int companyId, int limit = 10);

    /// <summary>
    /// Bildirim metriklerini getirir
    /// </summary>
    Task<List<NotificationMetricsViewModel>> GetNotificationMetricsAsync(int companyId);

    /// <summary>
    /// Dashboard grafik verilerini getirir
    /// </summary>
    Task<DashboardChartsViewModel> GetDashboardChartsAsync(int companyId);

    /// <summary>
    /// Hızlı işlem kartlarını getirir
    /// </summary>
    Task<List<QuickActionViewModel>> GetQuickActionsAsync(int companyId);

    /// <summary>
    /// Gerçek zamanlı dashboard verilerini getirir (AJAX için)
    /// </summary>
    Task<object> GetRealTimeDataAsync(int companyId);
}
