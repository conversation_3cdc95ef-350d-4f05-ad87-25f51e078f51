using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.Integrations.Common;
using PushDashboard.Services.Integrations.Common.Models;
namespace PushDashboard.Services.Integrations
{
    public class EcommerceGiftVoucherFactory
    {
        private readonly ApplicationDbContext _context;
        private readonly IEcommerceServiceFactory _ecommerceServiceFactory;
        private readonly ILogger<EcommerceGiftVoucherFactory> _logger;

        public EcommerceGiftVoucherFactory(
            ApplicationDbContext context,
            IEcommerceServiceFactory ecommerceServiceFactory,
            ILogger<EcommerceGiftVoucherFactory> logger)
        {
            _context = context;
            _ecommerceServiceFactory = ecommerceServiceFactory;
            _logger = logger;
        }

        /// <summary>
        /// Şirketin aktif e-ticaret entegrasyonunu döner
        /// </summary>
        public async Task<CompanyIntegration?> GetActiveEcommerceIntegrationAsync(int companyId)
        {
            try
            {
                // E-ticaret kategorisindeki aktif entegrasyonları al
                var ecommerceIntegration = await _context.CompanyIntegrations
                    .Include(ci => ci.Integration)
                    .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                              ci.IsActive &&
                                              ci.IsConfigured &&
                                              ci.Integration.Type == "ecommerce");

                return ecommerceIntegration;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active e-commerce integration for company {CompanyId}", companyId);
                return null;
            }
        }

        /// <summary>
        /// Şirketin aktif e-ticaret servisi döner
        /// </summary>
        public async Task<IEcommerceService?> GetActiveEcommerceServiceAsync(int companyId)
        {
            try
            {
                return await _ecommerceServiceFactory.GetEcommerceServiceAsync(companyId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active e-commerce service for company {CompanyId}", companyId);
                return null;
            }
        }

        /// <summary>
        /// Şirketin hediye çeki oluşturabileceği platform adını döner
        /// </summary>
        public async Task<string?> GetActivePlatformNameAsync(int companyId)
        {
            var service = await GetActiveEcommerceServiceAsync(companyId);
            return service?.PlatformName;
        }

        /// <summary>
        /// Şirketin herhangi bir e-ticaret entegrasyonu var mı kontrol eder
        /// </summary>
        public async Task<bool> HasActiveEcommerceIntegrationAsync(int companyId)
        {
            var integration = await GetActiveEcommerceIntegrationAsync(companyId);
            return integration != null;
        }

        /// <summary>
        /// Müşteri için hediye çeki oluşturur (aktif e-ticaret platformu üzerinden)
        /// </summary>
        public async Task<GiftVoucherResult> CreateGiftVoucherAsync(int companyId, Customer customer, GiftVoucherSettings settings)
        {
            try
            {
                var service = await GetActiveEcommerceServiceAsync(companyId);
                if (service == null)
                {
                    return GiftVoucherResult.CreateFailure("No active e-commerce integration found that supports gift vouchers");
                }

                // GiftVoucherSettings'i EcommerceGiftVoucherRequest'e dönüştür
                var ecommerceRequest = new EcommerceGiftVoucherRequest
                {
                    Name = !string.IsNullOrEmpty(settings.Description)
                        ? $"{settings.Description} - {customer.FullName}"
                        : $"Hediye Çeki - {customer.FullName}",
                    DiscountType = settings.DiscountType == 1 ? "AMOUNT" : "PERCENTAGE",
                    DiscountValue = (decimal)settings.Amount,
                    CustomerId = customer.ExternalId > 0 ? customer.ExternalId.ToString() : null,
                    ExpiryDate = DateTime.Now.AddDays(settings.ValidityDays),
                    UsageLimit = 1
                };

                var response = await service.CreateGiftVoucherAsync(companyId, ecommerceRequest);

                if (response != null && response.Success && !string.IsNullOrEmpty(response.VoucherCode))
                {
                    _logger.LogInformation("Gift voucher created successfully for customer {CustomerId} (ExternalId: {ExternalId}): {VoucherCode}",
                        customer.Id, customer.ExternalId, response.VoucherCode);

                    var amountText = settings.DiscountType == 1 ? $"{settings.Amount:N0} TL" : $"%{settings.Amount:N0}";
                    return GiftVoucherResult.CreateSuccess(response.VoucherCode, amountText);
                }
                else
                {
                    var errorMessage = response?.Message ?? "No response from API";
                    _logger.LogWarning("Failed to create gift voucher for customer {CustomerId}. Response: {Response}",
                        customer.Id, errorMessage);

                    return GiftVoucherResult.CreateFailure(errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating gift voucher for customer {CustomerId}", customer.Id);
                return GiftVoucherResult.CreateFailure($"Exception: {ex.Message}");
            }
        }
    }
}
