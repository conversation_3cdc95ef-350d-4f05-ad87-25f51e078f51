using PushDashboard.Services.Integrations.Common;

namespace PushDashboard.Services.Integrations;

public interface IEcommerceServiceFactory
{
    Task<IEcommerceService?> GetEcommerceServiceAsync(int companyId);
    Task<Dictionary<string, object>?> GetActiveIntegrationSettingsAsync(int companyId);
    Task<string?> GetActiveIntegrationTypeAsync(int companyId);

    // Legacy methods - deprecated
    [Obsolete("Use GetEcommerceServiceAsync instead")]
    Task<IEcommerceService?> GetBasketServiceAsync(int companyId);
}
