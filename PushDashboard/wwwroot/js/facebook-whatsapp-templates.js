$(document).ready(function() {
    let currentTemplateId = null;
    let currentTemplate = null;
    let templateComponents = [];
    let templateVariables = {};
    let currentMediaComponentId = null;

    // Predefined variables that users can select from
    const predefinedVariables = {
        'customer_name': { label: 'Müşteri Adı', example: '<PERSON>met Yılmaz', order: 1 },
        'order_number': { label: 'Sipariş Numarası', example: 'ORD-2024-001', order: 2 },
        'order_total': { label: 'Sipari<PERSON> Tutarı', example: '₺299,90', order: 3 },
        'delivery_date': { label: 'Teslimat Tarihi', example: '15.01.2024', order: 4 },
        'delivery_address': { label: 'Teslimat Adresi', example: 'Kadıköy, İstanbul', order: 5 },
        'tracking_code': { label: 'Takip Kodu', example: 'ABC123', order: 6 },
        'product_name': { label: 'Ü<PERSON><PERSON><PERSON>', example: 'Premium Ürün', order: 7 },
        'discount_amount': { label: '<PERSON><PERSON><PERSON>', example: '%20', order: 8 },
        'appointment_time': { label: '<PERSON><PERSON><PERSON>', example: '14:30', order: 9 },
        'company_name': { label: 'Şirket Adı', example: 'ABC Şirketi', order: 10 },
        'verification_code': { label: 'Doğrulama Kodu', example: '123456', order: 11 },
        'expiry_date': { label: 'Son Geçerlilik', example: '31.12.2024', order: 12 },
        'phone_number': { label: 'Telefon Numarası', example: '+90 ************', order: 13 },
        'email_address': { label: 'E-posta Adresi', example: '<EMAIL>', order: 14 },
        'category_name': { label: 'Kategori Adı', example: 'Elektronik', order: 15 }
    };

    // Create template button handlers
    $('#createTemplateBtn, #createFirstTemplateBtn').on('click', function() {
        showCreateTemplateModal();
    });

    // Template item click handler
    $('.template-item').on('click', function() {
        const templateId = $(this).data('template-id');
        loadTemplate(templateId);
    });

    // View template button handler
    $('.view-template-btn').on('click', function(e) {
        e.stopPropagation();
        const templateId = $(this).data('template-id');
        showViewTemplateModal(templateId);
    });

    // Delete template button handler
    $('.delete-template-btn').on('click', function(e) {
        e.stopPropagation();
        const templateName = $(this).data('template-name');
        deleteTemplate(templateName);
    });

    // Close modal handlers
    $('#closeModalBtn, #closeModalBtn2, #closeViewModalBtn').on('click', function() {
        hideTemplateModal();
    });

    // Modal backdrop click
    $('#templateModal').on('click', function(e) {
        if (e.target === this) {
            hideTemplateModal();
        }
    });

    // Component builder event handlers
    $('#addHeaderBtn').on('click', function() {
        addComponent('HEADER');
    });

    $('#addBodyBtn').on('click', function() {
        addComponent('BODY');
    });

    $('#addFooterBtn').on('click', function() {
        addComponent('FOOTER');
    });

    $('#addButtonsBtn').on('click', function() {
        addComponent('BUTTONS');
    });

    // Preview with sample data
    $('#previewWithSampleData').on('click', function() {
        updatePreviewWithSampleData();
    });

    // Sample templates handlers
    $('#showSampleTemplatesBtn').on('click', function() {
        showSampleTemplatesModal();
    });

    $('#closeSampleModalBtn, #closeSampleModalBtn2').on('click', function() {
        hideSampleTemplatesModal();
    });



    // Media upload modal handlers
    $('#closeMediaModalBtn, #closeMediaModalBtn2').on('click', function() {
        hideMediaUploadModal();
    });

    $('#mediaFileInput').on('change', function() {
        handleFileSelection(this);
    });

    $('#uploadMediaBtn').on('click', function() {
        uploadSelectedMedia();
    });

    // Show create template modal
    function showCreateTemplateModal() {
        currentTemplateId = null;
        currentTemplate = null;
        templateComponents = [];
        templateVariables = {};

        $('#templateModalLabel span').text('Yeni WhatsApp Şablonu Oluştur');
        $('#createForm')[0].reset();

        // Clear components and variables
        $('#componentsBuilder').empty();
        $('#variableDefinitions').empty();
        $('#whatsappPreview').html('<div class="text-center text-gray-500 text-sm">Şablon bileşenlerini ekleyin</div>');
        $('#templateValidation').addClass('hidden');

        // Enable template name editing for new templates
        enableTemplateNameEditing();

        // Show create form, hide edit form
        $('#createTemplateForm').removeClass('hidden');
        $('#editTemplateForm').addClass('hidden');

        showTemplateModal();
    }

    // Show view template modal
    function showViewTemplateModal(templateId) {
        showLoading();

        $.get(`/WhatsAppTemplate/GetTemplate/?templateId=${templateId}`)
            .done(function(response) {
                if (response.success) {
                    currentTemplateId = templateId;
                    currentTemplate = response.template;
                    populateViewTemplateForm(response.template);
                    showTemplateModal();
                } else {
                    showToast('Hata', response.message || 'Şablon yüklenirken hata oluştu.', 'error');
                }
            })
            .fail(function() {
                showToast('Hata', 'Şablon yüklenirken hata oluştu.', 'error');
            })
            .always(function() {
                hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });            
    }

    // Load template data (legacy function - kept for compatibility)
    function loadTemplate(templateId) {
        showViewTemplateModal(templateId);
    }

    // Populate view form with template data
    function populateViewTemplateForm(template) {
        $('#templateModalLabel span').text(`${template.name} - Görüntüle`);

        // Show view form, hide create form
        $('#viewTemplateForm').removeClass('hidden');
        $('#createTemplateForm').addClass('hidden');

        // Populate template info
        $('#viewTemplateName').text(template.name);
        $('#viewTemplateCategory').text(template.category);
        $('#viewTemplateStatus').text(template.status);
        $('#viewTemplateLanguage').text(template.language);
        $('#viewTemplateCreated').text(template.createdTime ? new Date(template.createdTime).toLocaleDateString('tr-TR') : '-');
        $('#viewTemplateQuality').text(template.qualityScore || '-');

        // Status badge color
        const statusBadge = $('#viewTemplateStatus');
        statusBadge.removeClass('bg-green-100 text-green-800 bg-yellow-100 text-yellow-800 bg-red-100 text-red-800 bg-gray-100 text-gray-800');

        if (template.status === 'APPROVED') {
            statusBadge.addClass('bg-green-100 text-green-800');
        } else if (template.status === 'PENDING') {
            statusBadge.addClass('bg-yellow-100 text-yellow-800');
        } else if (template.status === 'REJECTED') {
            statusBadge.addClass('bg-red-100 text-red-800');
        } else {
            statusBadge.addClass('bg-gray-100 text-gray-800');
        }

        // Show rejection reason if exists
        if (template.rejectionReason) {
            $('#viewRejectionReasonContainer').removeClass('hidden');
            $('#viewRejectionReason').text(template.rejectionReason);
        } else {
            $('#viewRejectionReasonContainer').addClass('hidden');
        }

        // Convert template to component builder format
        convertTemplateToComponents(template);

        // Populate components in builder format
        populateViewComponents(templateComponents);

        // Update preview
        updateViewPreview();

        // Extract and show variables
        extractAndShowVariables();
    }



    // Populate components
    function populateComponents(components) {
        const container = $('#componentsContainer');
        container.empty();

        components.forEach((component, index) => {
            const componentHtml = createComponentHtml(component, index);
            container.append(componentHtml);
        });

        if (components.length === 0) {
            container.html('<p class="text-gray-500 text-sm">Bu şablonda henüz component bulunmuyor.</p>');
        }
    }

    // Create component HTML
    function createComponentHtml(component, index) {
        let componentContent = '';

        if (component.type === 'HEADER') {
            componentContent = `
                <div class="bg-blue-50 border border-blue-200 rounded p-3">
                    <div class="flex items-center mb-2">
                        <svg class="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                        <span class="font-medium text-blue-800">Header</span>
                    </div>
                    <p class="text-sm text-blue-700">${component.text || 'Metin yok'}</p>
                    ${component.format && component.format !== 'TEXT' ? `<p class="text-xs text-blue-600 mt-1">Format: ${component.format}</p>` : ''}
                </div>
            `;
        } else if (component.type === 'BODY') {
            componentContent = `
                <div class="bg-gray-50 border border-gray-200 rounded p-3">
                    <div class="flex items-center mb-2">
                        <svg class="w-4 h-4 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3a1 1 0 000 2h10a1 1 0 100-2H5zm0 4a1 1 0 100 2h10a1 1 0 100-2H5z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium text-gray-800">Body</span>
                    </div>
                    <p class="text-sm text-gray-700 whitespace-pre-wrap">${component.text || 'Metin yok'}</p>
                </div>
            `;
        } else if (component.type === 'FOOTER') {
            componentContent = `
                <div class="bg-gray-50 border border-gray-200 rounded p-3">
                    <div class="flex items-center mb-2">
                        <svg class="w-4 h-4 text-gray-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z"></path>
                        </svg>
                        <span class="font-medium text-gray-800">Footer</span>
                    </div>
                    <p class="text-sm text-gray-700">${component.text || 'Metin yok'}</p>
                </div>
            `;
        } else if (component.type === 'BUTTONS') {
            const buttonsHtml = component.buttons ? component.buttons.map(button => `
                <div class="bg-green-100 border border-green-200 rounded p-2 mb-2">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-green-800">${button.text}</span>
                        <span class="text-xs text-green-600">${button.type}</span>
                    </div>
                    ${button.url ? `<p class="text-xs text-green-600 mt-1">${button.url}</p>` : ''}
                    ${button.phoneNumber ? `<p class="text-xs text-green-600 mt-1">${button.phoneNumber}</p>` : ''}
                </div>
            `).join('') : '';

            componentContent = `
                <div class="bg-green-50 border border-green-200 rounded p-3">
                    <div class="flex items-center mb-2">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                        <span class="font-medium text-green-800">Buttons</span>
                    </div>
                    ${buttonsHtml || '<p class="text-sm text-green-700">Buton yok</p>'}
                </div>
            `;
        }

        return `<div class="component-item mb-3" data-index="${index}">${componentContent}</div>`;
    }

    // Create template
    $('#createTemplateBtn2').on('click', function() {
        // Validate template first
        if (!validateTemplate()) {
            showToast('Uyarı', 'Lütfen tüm doğrulama hatalarını düzeltin. Özellikle değişken türlerinin seçildiğinden emin olun.', 'warning');

            // Scroll to validation messages
            const $validation = $('#templateValidation');
            if (!$validation.hasClass('hidden')) {
                $validation[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return;
        }

        const templateName = $('#createTemplateName').val().trim();
        if (!templateName) {
            showToast('Uyarı', 'Şablon adı gereklidir.', 'warning');
            $('#createTemplateName').focus();
            return;
        }

        // Prepare components for Facebook API (Business Management API format)
        const facebookComponents = templateComponents.map(component => {
            const fbComponent = {
                type: component.type.toUpperCase()
            };

            if (component.type === 'HEADER') {
                console.log(`Processing HEADER component: format=${component.format}, text=${component.text}`);
                if (component.format === 'TEXT') {
                    fbComponent.text = component.text || '';
                    fbComponent.format = component.format;
                } else {
                    fbComponent.format = component.format?.toUpperCase();
                    if (component.text) {
                        fbComponent.example = {
                            header_handle: [component.text]
                        };
                    }
                }
                console.log(`HEADER fbComponent result:`, fbComponent);
            } else if (component.type === 'BODY') {
                fbComponent.text = component.text || '';

                // Extract variables and add example values
                const variableMatches = component.text.match(/\{\{(\d+)\}\}/g);
                if (variableMatches) {
                    const uniqueVars = [...new Set(variableMatches.map(match =>
                        parseInt(match.replace(/[{}]/g, ''))
                    ))].sort((a, b) => a - b);

                    fbComponent.example = {
                        body_text: [uniqueVars.map(varNum => {
                            const variable = templateVariables[varNum];
                            if (variable && variable.type && predefinedVariables[variable.type]) {
                                return predefinedVariables[variable.type].example;
                            }
                            return `sample_value_${varNum}`;
                        })]
                    };
                }
            } else if (component.type === 'FOOTER') {
                fbComponent.text = component.text || '';
            } else if (component.type === 'BUTTONS') {
                if (component.buttons && component.buttons.length > 0) {
                    fbComponent.buttons = component.buttons.map(button => {
                        const fbButton = {
                            type: (button.type || 'QUICK_REPLY').toUpperCase(),
                            text: button.text || ''
                        };

                        if (button.type === 'URL' && button.url) {
                            fbButton.url = button.url;
                            // If URL contains variables, add example
                            if (button.url.includes('{{')) {
                                fbButton.example = ['https://example.com/sample'];
                            }
                        } else if (button.type === 'PHONE_NUMBER' && button.phoneNumber) {
                            fbButton.phone_number = button.phoneNumber;
                        }

                        return fbButton;
                    });
                }
            }

            return fbComponent;
        });

        const formData = {
            name: templateName,
            category: $('#createTemplateCategory').val(),
            language: $('#createTemplateLanguage').val(),
            components: facebookComponents
        };

        // Debug: Log the data being sent
        console.log('=== TEMPLATE DEBUG INFO ===');
        console.log('Template Name:', templateName);
        console.log('Template Components:', templateComponents);

        // Debug each component in detail
        templateComponents.forEach((comp, index) => {
            console.log(`Component ${index}:`, {
                id: comp.id,
                type: comp.type,
                text: comp.text,
                format: comp.format,
                buttons: comp.buttons
            });

            // Special debug for HEADER components
            if (comp.type === 'HEADER') {
                console.log(`HEADER Component Debug:`, {
                    format: comp.format,
                    text: comp.text,
                    formatType: typeof comp.format,
                    textType: typeof comp.text
                });
            }
        });

        console.log('Facebook Components:', facebookComponents);
        console.log('Final Form Data:', JSON.stringify(formData, null, 2));
        console.log('=== END DEBUG INFO ===');

        showLoading();

        $.ajax({
            url: '/WhatsAppTemplate/CreateTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast('Başarılı', response.message || 'WhatsApp şablonu Facebook\'a gönderildi. Onay sürecini bekleyin.', 'success');
                hideTemplateModal();

                // Refresh page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast('Hata', response.message || 'Şablon oluşturulurken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Şablon oluşturulurken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });       
    });

    // Delete template
    function deleteTemplate(templateName) {
        if (!confirm('Bu şablonu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
            return;
        }

        showLoading();

        $.ajax({
            url: '/WhatsAppTemplate/DeleteTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                templateName: templateName
            }),
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast('Başarılı', response.message || 'WhatsApp şablonu başarıyla silindi.', 'success');

                // Refresh page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                console.error("Delete template error:", response);
                showToast("Hata", response.message || "Şablon silinirken hata oluştu.", "error");
            }
        })
        .fail(function() {
            showToast('Hata', 'Şablon silinirken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });        
    }

    // Show/hide modal
    function showTemplateModal() {
        $('#templateModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function hideTemplateModal() {
        $('#templateModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    // Loading and toast functions
    function showLoading() {
        // Implementation similar to email templates
    }

    function hideLoading() {
        // Implementation similar to email templates
    }

    function showToast(title, message, type) {
        // Implementation similar to email templates
        const toast = $(`
            <div class="fixed top-4 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm transform transition-all duration-300 translate-x-full">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        ${type === 'success' ? '<div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></div>' :
                          type === 'error' ? '<div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div>' :
                          '<div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg></div>'}
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">${title}</p>
                        <p class="text-sm text-gray-500">${message}</p>
                    </div>
                    <button class="ml-4 text-gray-400 hover:text-gray-600" onclick="$(this).closest('.fixed').remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `);

        $('body').append(toast);

        setTimeout(() => {
            toast.removeClass('translate-x-full');
        }, 100);

        setTimeout(() => {
            toast.addClass('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    // Component Builder Functions
    function addComponent(type) {
        const componentId = 'component_' + Date.now();
        let component = {
            id: componentId,
            type: type,
            text: '',
            format: 'TEXT'
        };

        // Initialize buttons array only for BUTTONS component
        if (type === 'BUTTONS') {
            component.buttons = [];
        }

        // Check for existing components of same type (some are unique)
        if (type === 'HEADER' && templateComponents.find(c => c.type === 'HEADER')) {
            showToast('Uyarı', 'Şablonda sadece bir Header olabilir.', 'warning');
            return;
        }
        if (type === 'BODY' && templateComponents.find(c => c.type === 'BODY')) {
            showToast('Uyarı', 'Şablonda sadece bir Body olabilir.', 'warning');
            return;
        }
        if (type === 'FOOTER' && templateComponents.find(c => c.type === 'FOOTER')) {
            showToast('Uyarı', 'Şablonda sadece bir Footer olabilir.', 'warning');
            return;
        }
        if (type === 'BUTTONS' && templateComponents.find(c => c.type === 'BUTTONS')) {
            showToast('Uyarı', 'Şablonda sadece bir Buttons bileşeni olabilir.', 'warning');
            return;
        }

        templateComponents.push(component);
        renderComponent(component);
        updatePreview();
        validateTemplate();
    }

    function renderComponent(component) {
        const componentHtml = createComponentBuilderHtml(component);
        $('#componentsBuilder').append(componentHtml);

        // Add event listeners for this component
        bindComponentEvents(component.id);
    }

    function createComponentBuilderHtml(component) {
        let html = `<div class="component-builder border border-gray-200 rounded-lg p-3" data-component-id="${component.id}">`;

        // Component header
        html += `<div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-700">${component.type}</span>
                ${getComponentIcon(component.type)}
            </div>
            <button type="button" class="remove-component text-red-600 hover:text-red-800 text-sm">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>`;

        // Component-specific content
        if (component.type === 'HEADER') {
            html += createHeaderComponentHtml(component);
        } else if (component.type === 'BODY') {
            html += createBodyComponentHtml(component);
        } else if (component.type === 'FOOTER') {
            html += createFooterComponentHtml(component);
        } else if (component.type === 'BUTTONS') {
            html += createButtonsComponentHtml(component);
        }

        html += '</div>';
        return html;
    }

    function createHeaderComponentHtml(component) {
        return `
            <div class="space-y-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Format</label>
                    <select class="component-format w-full px-2 py-1 text-sm border border-gray-300 rounded">
                        <option value="TEXT" ${component.format === 'TEXT' ? 'selected' : ''}>Metin</option>
                        <option value="IMAGE" ${component.format === 'IMAGE' ? 'selected' : ''}>Resim</option>
                        <option value="VIDEO" ${component.format === 'VIDEO' ? 'selected' : ''}>Video</option>
                        <option value="DOCUMENT" ${component.format === 'DOCUMENT' ? 'selected' : ''}>Döküman</option>
                    </select>
                </div>
                ${component.format === 'TEXT' ? `
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Header Metni</label>
                        <input type="text" class="component-text w-full px-2 py-1 text-sm border border-gray-300 rounded"
                               placeholder="Header metni" value="${component.text || ''}" maxlength="60">
                        <p class="text-xs text-gray-500 mt-1">Maksimum 60 karakter</p>
                    </div>
                ` : `
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Medya URL</label>
                        <div class="flex space-x-2">
                            <input type="url" class="component-text flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                                   placeholder="https://example.com/media.jpg" value="${component.text || ''}">
                            <button type="button" class="upload-media-btn px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600">
                                Yükle
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">URL girin veya dosya yükleyin</p>
                    </div>
                `}
            </div>
        `;
    }

    function createBodyComponentHtml(component) {
        return `
            <div class="space-y-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Body Metni *</label>
                    <textarea class="component-text w-full px-2 py-1 text-sm border border-gray-300 rounded"
                              rows="4" placeholder="Mesaj içeriği... {{1}}, {{2}} gibi değişkenler kullanabilirsiniz"
                              maxlength="1024">${component.text || ''}</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimum 1024 karakter. Değişkenler: {{1}}, {{2}}, {{3}}...</p>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded p-2">
                    <p class="text-xs text-blue-700">
                        <strong>Değişken Kullanımı:</strong> {{1}} ilk değişken, {{2}} ikinci değişken şeklinde kullanın.
                        Her değişken için aşağıda açıklama ekleyebilirsiniz.
                    </p>
                </div>
            </div>
        `;
    }

    function createFooterComponentHtml(component) {
        return `
            <div>
                <label class="block text-xs font-medium text-gray-700 mb-1">Footer Metni</label>
                <input type="text" class="component-text w-full px-2 py-1 text-sm border border-gray-300 rounded"
                       placeholder="Footer metni (opsiyonel)" value="${component.text || ''}" maxlength="60">
                <p class="text-xs text-gray-500 mt-1">Maksimum 60 karakter</p>
            </div>
        `;
    }

    function createButtonsComponentHtml(component) {
        let html = `
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <label class="block text-xs font-medium text-gray-700">Butonlar</label>
                    <button type="button" class="add-button px-2 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200">
                        + Buton Ekle
                    </button>
                </div>
                <div class="buttons-container space-y-2">
        `;

        if (component.buttons && component.buttons.length > 0) {
            component.buttons.forEach((button, index) => {
                html += createButtonHtml(button, index);
            });
        }

        html += `
                </div>
                <p class="text-xs text-gray-500">Maksimum 3 buton ekleyebilirsiniz</p>
            </div>
        `;

        return html;
    }

    function createButtonHtml(button, index) {
        return `
            <div class="button-item border border-gray-200 rounded p-2" data-button-index="${index}">
                <div class="flex items-center justify-between mb-2">
                    <select class="button-type text-xs border border-gray-300 rounded px-1 py-1">
                        <option value="QUICK_REPLY" ${button.type === 'QUICK_REPLY' ? 'selected' : ''}>Hızlı Yanıt</option>
                        <option value="URL" ${button.type === 'URL' ? 'selected' : ''}>URL</option>
                        <option value="PHONE_NUMBER" ${button.type === 'PHONE_NUMBER' ? 'selected' : ''}>Telefon</option>
                    </select>
                    <button type="button" class="remove-button text-red-600 hover:text-red-800 text-xs">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-2">
                    <input type="text" class="button-text w-full px-2 py-1 text-xs border border-gray-300 rounded"
                           placeholder="Buton metni" value="${button.text || ''}" maxlength="25">
                    ${button.type === 'URL' ? `
                        <input type="url" class="button-url w-full px-2 py-1 text-xs border border-gray-300 rounded"
                               placeholder="https://example.com" value="${button.url || ''}">
                    ` : ''}
                    ${button.type === 'PHONE_NUMBER' ? `
                        <input type="tel" class="button-phone w-full px-2 py-1 text-xs border border-gray-300 rounded"
                               placeholder="+90 ************" value="${button.phoneNumber || ''}">
                    ` : ''}
                </div>
            </div>
        `;
    }

    function getComponentIcon(type) {
        const icons = {
            'HEADER': '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"></path></svg>',
            'BODY': '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 3a1 1 0 000 2h10a1 1 0 100-2H5zm0 4a1 1 0 100 2h10a1 1 0 100-2H5z" clip-rule="evenodd"></path></svg>',
            'FOOTER': '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z"></path></svg>',
            'BUTTONS': '<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"></path></svg>'
        };
        return icons[type] || '';
    }

    // Event Binding Functions
    function bindComponentEvents(componentId) {
        const $component = $(`.component-builder[data-component-id="${componentId}"]`);

        // Remove component
        $component.find('.remove-component').on('click', function() {
            removeComponent(componentId);
        });

        // Component text changes
        $component.find('.component-text').on('input', function() {
            updateComponentData(componentId);
        });

        // Component format changes (for HEADER component)
        $component.find('.component-format').on('change', function() {
            updateComponentData(componentId);
            // Re-render component if format changes (for HEADER)
            const component = templateComponents.find(c => c.id === componentId);
            if (component && component.type === 'HEADER') {
                const $currentComponent = $(`.component-builder[data-component-id="${componentId}"]`);
                $currentComponent.replaceWith(createComponentBuilderHtml(component));
                bindComponentEvents(componentId);
            }
        });

        // Button management for BUTTONS component
        $component.find('.add-button').on('click', function() {
            addButtonToComponent(componentId);
        });

        // Button events
        bindButtonEvents($component);

        // Media upload button
        $component.find('.upload-media-btn').on('click', function() {
            currentMediaComponentId = componentId;
            showMediaUploadModal();
        });
    }

    function bindButtonEvents($component) {
        $component.find('.button-item').each(function() {
            const $buttonItem = $(this);

            // Remove button
            $buttonItem.find('.remove-button').off('click').on('click', function() {
                const componentId = $component.data('component-id');
                const buttonIndex = $buttonItem.data('button-index');
                removeButtonFromComponent(componentId, buttonIndex);
            });

            // Button type change
            $buttonItem.find('.button-type').off('change').on('change', function() {
                const componentId = $component.data('component-id');
                updateButtonType(componentId, $buttonItem);
            });

            // Button text/url/phone changes
            $buttonItem.find('.button-text, .button-url, .button-phone').off('input').on('input', function() {
                const componentId = $component.data('component-id');
                updateComponentData(componentId);
            });
        });
    }

    function removeComponent(componentId) {
        templateComponents = templateComponents.filter(c => c.id !== componentId);
        $(`.component-builder[data-component-id="${componentId}"]`).remove();
        updatePreview();
        validateTemplate();
    }

    function updateComponentData(componentId) {
        const component = templateComponents.find(c => c.id === componentId);
        if (!component) return;

        const $component = $(`.component-builder[data-component-id="${componentId}"]`);

        // Update basic component data
        const newText = $component.find('.component-text').val() || '';
        const newFormat = $component.find('.component-format').val() || 'TEXT';

        component.text = newText;
        component.format = newFormat;

        // Debug format update
        if (component.type === 'HEADER') {
            console.log(`Updating HEADER component ${componentId}:`, {
                oldFormat: component.format,
                newFormat: newFormat,
                oldText: component.text,
                newText: newText,
                elementValue: $component.find('.component-format').val()
            });
        }

        // Update buttons if this is a BUTTONS component
        if (component.type === 'BUTTONS') {
            // Initialize buttons array if it doesn't exist
            if (!component.buttons) {
                component.buttons = [];
            } else {
                component.buttons = []; // Clear existing buttons
            }

            $component.find('.button-item').each(function() {
                const $button = $(this);
                const button = {
                    type: $button.find('.button-type').val(),
                    text: $button.find('.button-text').val() || ''
                };

                if (button.type === 'URL') {
                    button.url = $button.find('.button-url').val() || '';
                } else if (button.type === 'PHONE_NUMBER') {
                    button.phoneNumber = $button.find('.button-phone').val() || '';
                }

                component.buttons.push(button);
            });
        }

        // Extract variables from body text
        if (component.type === 'BODY') {
            extractVariablesFromText(component.text);
        }

        updatePreview();
        validateTemplate();
    }

    function addButtonToComponent(componentId) {
        const component = templateComponents.find(c => c.id === componentId);
        if (!component) return;

        // Initialize buttons array if it doesn't exist
        if (!component.buttons) {
            component.buttons = [];
        }

        if (component.buttons.length >= 3) {
            showToast('Uyarı', 'Maksimum 3 buton ekleyebilirsiniz.', 'warning');
            return;
        }

        const newButton = {
            type: 'QUICK_REPLY',
            text: ''
        };

        component.buttons.push(newButton);

        // Re-render the component
        const $component = $(`.component-builder[data-component-id="${componentId}"]`);
        $component.replaceWith(createComponentBuilderHtml(component));
        bindComponentEvents(componentId);

        updatePreview();
    }

    function removeButtonFromComponent(componentId, buttonIndex) {
        const component = templateComponents.find(c => c.id === componentId);
        if (!component) return;

        component.buttons.splice(buttonIndex, 1);

        // Re-render the component
        const $component = $(`.component-builder[data-component-id="${componentId}"]`);
        $component.replaceWith(createComponentBuilderHtml(component));
        bindComponentEvents(componentId);

        updatePreview();
    }

    function updateButtonType(componentId, $buttonItem) {
        const newType = $buttonItem.find('.button-type').val();
        const buttonIndex = $buttonItem.data('button-index');

        const component = templateComponents.find(c => c.id === componentId);
        if (!component || !component.buttons[buttonIndex]) return;

        component.buttons[buttonIndex].type = newType;

        // Re-render the component to show/hide appropriate fields
        const $component = $(`.component-builder[data-component-id="${componentId}"]`);
        $component.replaceWith(createComponentBuilderHtml(component));
        bindComponentEvents(componentId);

        updatePreview();
    }

    // Variable Management
    function extractVariablesFromText(text) {
        const variableRegex = /\{\{(\d+)\}\}/g;
        const foundVariables = new Set();
        let match;

        while ((match = variableRegex.exec(text)) !== null) {
            foundVariables.add(parseInt(match[1]));
        }

        // Update templateVariables
        const sortedVariables = Array.from(foundVariables).sort((a, b) => a - b);

        // Remove variables that are no longer used
        Object.keys(templateVariables).forEach(key => {
            if (!sortedVariables.includes(parseInt(key))) {
                delete templateVariables[key];
            }
        });

        // Add new variables
        sortedVariables.forEach(varNum => {
            if (!templateVariables[varNum]) {
                templateVariables[varNum] = {
                    name: `Değişken ${varNum}`,
                    description: '',
                    type: '', // Will be selected by user
                    example: ''
                };
            }
        });

        renderVariableDefinitions();
    }

    function renderVariableDefinitions() {
        const $container = $('#variableDefinitions');
        $container.empty();

        const sortedVars = Object.keys(templateVariables).sort((a, b) => parseInt(a) - parseInt(b));

        if (sortedVars.length === 0) {
            $container.html('<p class="text-xs text-gray-500">Henüz değişken tanımlanmamış</p>');
            return;
        }

        sortedVars.forEach(varNum => {
            const variable = templateVariables[varNum];
            const html = `
                <div class="variable-definition border border-gray-200 rounded p-3 mb-2" data-var-num="${varNum}">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">{{${varNum}}}</span>
                        <span class="text-xs text-gray-500">Değişken ${varNum}</span>
                    </div>
                    <div class="space-y-2">
                        <select class="variable-type w-full px-2 py-1 text-sm border border-gray-300 rounded ${!variable.type ? 'border-red-300 bg-red-50' : ''}" data-var-num="${varNum}">
                            <option value="">Değişken türü seçin *</option>
                            ${generateVariableOptions(variable.type)}
                        </select>
                        <div class="variable-preview text-xs text-gray-600 bg-gray-50 px-2 py-1 rounded" style="display: ${variable.type ? 'block' : 'none'}">
                            Örnek: <span class="font-medium">${variable.type ? predefinedVariables[variable.type]?.example || '' : ''}</span>
                        </div>
                    </div>
                </div>
            `;
            $container.append(html);
        });

        // Bind events for variable type selection
        bindVariableEvents();
    }

    function generateVariableOptions(selectedType) {
        let options = '';

        // Sort predefined variables by order
        const sortedVariables = Object.entries(predefinedVariables)
            .sort(([,a], [,b]) => a.order - b.order);

        sortedVariables.forEach(([key, variable]) => {
            const selected = selectedType === key ? 'selected' : '';
            options += `<option value="${key}" ${selected}>${variable.label}</option>`;
        });

        return options;
    }

    function bindVariableEvents() {
        $('.variable-type').off('change').on('change', function() {
            const varNum = $(this).data('var-num');
            const selectedType = $(this).val();
            const $definition = $(this).closest('.variable-definition');
            const $preview = $definition.find('.variable-preview');
            const $select = $(this);

            // Update template variable
            if (templateVariables[varNum]) {
                templateVariables[varNum].type = selectedType;
                templateVariables[varNum].description = selectedType ? predefinedVariables[selectedType]?.label || '' : '';
            }

            // Update select styling based on selection
            if (selectedType) {
                $select.removeClass('border-red-300 bg-red-50').addClass('border-green-300 bg-green-50');
            } else {
                $select.removeClass('border-green-300 bg-green-50').addClass('border-red-300 bg-red-50');
            }

            // Update preview
            if (selectedType && predefinedVariables[selectedType]) {
                $preview.find('span').text(predefinedVariables[selectedType].example);
                $preview.show();
            } else {
                $preview.hide();
            }

            // Update WhatsApp preview
            updatePreview();

            // Trigger validation to check if all variables are selected
            validateTemplate();
        });
    }

    // WhatsApp Preview Functions
    function updatePreview() {
        const $preview = $('#whatsappPreview');
        let previewHtml = '';

        if (templateComponents.length === 0) {
            $preview.html('<div class="text-center text-gray-500 text-sm">Şablon bileşenlerini ekleyin</div>');
            return;
        }

        // Sort components in WhatsApp order: HEADER, BODY, FOOTER, BUTTONS
        const sortedComponents = [...templateComponents].sort((a, b) => {
            const order = { 'HEADER': 1, 'BODY': 2, 'FOOTER': 3, 'BUTTONS': 4 };
            return (order[a.type] || 5) - (order[b.type] || 5);
        });

        sortedComponents.forEach(component => {
            previewHtml += createPreviewComponentHtml(component);
        });

        $preview.html(previewHtml);
    }

    function createPreviewComponentHtml(component) {
        let html = '';

        if (component.type === 'HEADER') {
            if (component.format === 'TEXT' && component.text) {
                html += `<div class="font-semibold text-sm text-gray-900 mb-2">${escapeHtml(component.text)}</div>`;
            } else if (component.format !== 'TEXT' && component.text) {
                html += `<div class="mb-2">
                    <div class="bg-gray-200 rounded p-2 text-center text-xs text-gray-600">
                        ${component.format} Media<br>
                        <span class="text-xs">${component.text}</span>
                    </div>
                </div>`;
            }
        } else if (component.type === 'BODY') {
            if (component.text) {
                html += `<div class="text-sm text-gray-800 mb-2 whitespace-pre-wrap">${escapeHtml(component.text)}</div>`;
            }
        } else if (component.type === 'FOOTER') {
            if (component.text) {
                html += `<div class="text-xs text-gray-600 mt-2">${escapeHtml(component.text)}</div>`;
            }
        } else if (component.type === 'BUTTONS') {
            if (component.buttons && component.buttons.length > 0) {
                html += '<div class="mt-3 space-y-1">';
                component.buttons.forEach(button => {
                    if (button && button.text) {
                        let buttonClass = 'block w-full text-center py-2 px-3 text-sm border rounded';
                        if (button.type === 'URL') {
                            buttonClass += ' bg-blue-50 border-blue-200 text-blue-800';
                        } else if (button.type === 'PHONE_NUMBER') {
                            buttonClass += ' bg-green-50 border-green-200 text-green-800';
                        } else {
                            buttonClass += ' bg-gray-50 border-gray-200 text-gray-800';
                        }
                        html += `<div class="${buttonClass}">${escapeHtml(button.text)}</div>`;
                    }
                });
                html += '</div>';
            }
        }

        return html;
    }

    function updatePreviewWithSampleData() {
        // Generate sample data based on selected variable types
        const sampleData = {};
        Object.keys(templateVariables).forEach(varNum => {
            const variable = templateVariables[varNum];
            if (variable && variable.type && predefinedVariables[variable.type]) {
                sampleData[varNum] = predefinedVariables[variable.type].example;
            } else {
                // Fallback to default values
                const fallbackData = {
                    '1': 'Ahmet Yılmaz',
                    '2': 'ORD-2024-001',
                    '3': '₺299,90',
                    '4': 'Kadıköy, İstanbul',
                    '5': '15.01.2024',
                    '6': 'ABC123'
                };
                sampleData[varNum] = fallbackData[varNum] || `Örnek Değer ${varNum}`;
            }
        });

        const $preview = $('#whatsappPreview');
        let previewHtml = '';

        if (templateComponents.length === 0) {
            $preview.html('<div class="text-center text-gray-500 text-sm">Şablon bileşenlerini ekleyin</div>');
            return;
        }

        const sortedComponents = [...templateComponents].sort((a, b) => {
            const order = { 'HEADER': 1, 'BODY': 2, 'FOOTER': 3, 'BUTTONS': 4 };
            return (order[a.type] || 5) - (order[b.type] || 5);
        });

        sortedComponents.forEach(component => {
            let componentText = component.text || '';

            // Replace variables with sample data
            if (component.type === 'BODY') {
                Object.keys(sampleData).forEach(varNum => {
                    componentText = componentText.replace(new RegExp(`\\{\\{${varNum}\\}\\}`, 'g'), sampleData[varNum]);
                });
            }

            const tempComponent = { ...component, text: componentText };
            previewHtml += createPreviewComponentHtml(tempComponent);
        });

        $preview.html(previewHtml);
    }

    // Template Validation
    function validateTemplate() {
        const validationMessages = [];

        // Check if template has required components
        const hasBody = templateComponents.some(c => c.type === 'BODY');
        if (!hasBody) {
            validationMessages.push('Şablon en az bir BODY bileşeni içermelidir');
        }

        // Validate body text
        const bodyComponent = templateComponents.find(c => c.type === 'BODY');
        if (bodyComponent && !bodyComponent.text.trim()) {
            validationMessages.push('BODY bileşeni boş olamaz');
        }

        // Validate header
        const headerComponent = templateComponents.find(c => c.type === 'HEADER');
        if (headerComponent) {
            if (headerComponent.format === 'TEXT' && !headerComponent.text.trim()) {
                validationMessages.push('TEXT formatındaki HEADER boş olamaz');
            } else if (headerComponent.format !== 'TEXT' && !headerComponent.text.trim()) {
                validationMessages.push('Medya HEADER için geçerli URL gereklidir');
            }
        }

        // Validate buttons
        const buttonsComponent = templateComponents.find(c => c.type === 'BUTTONS');
        if (buttonsComponent) {
            if (!buttonsComponent.buttons || buttonsComponent.buttons.length === 0) {
                validationMessages.push('BUTTONS bileşeni en az bir buton içermelidir');
            } else {
                buttonsComponent.buttons.forEach((button, index) => {
                    if (!button.text || !button.text.trim()) {
                        validationMessages.push(`Buton ${index + 1} metni boş olamaz`);
                    }
                    if (button.type === 'URL' && (!button.url || !button.url.trim())) {
                        validationMessages.push(`Buton ${index + 1} için geçerli URL gereklidir`);
                    }
                    if (button.type === 'PHONE_NUMBER' && (!button.phoneNumber || !button.phoneNumber.trim())) {
                        validationMessages.push(`Buton ${index + 1} için geçerli telefon numarası gereklidir`);
                    }
                });
            }
        }

        // Validate template name
        const templateName = $('#createTemplateName').val();
        if (templateName && !/^[a-z0-9_]+$/.test(templateName)) {
            validationMessages.push('Şablon adı sadece küçük harf, rakam ve alt çizgi içerebilir');
        }

        // Validate variable selections
        const variableErrors = validateVariableSelections();
        validationMessages.push(...variableErrors);

        // Show/hide validation messages
        const $validation = $('#templateValidation');
        const $messages = $('#validationMessages');

        if (validationMessages.length > 0) {
            $messages.empty();
            validationMessages.forEach(message => {
                $messages.append(`<li>${message}</li>`);
            });
            $validation.removeClass('hidden');
        } else {
            $validation.addClass('hidden');
        }

        return validationMessages.length === 0;
    }

    function validateVariableSelections() {
        const errors = [];

        // Check if there are any variables defined
        const variableCount = Object.keys(templateVariables).length;
        if (variableCount === 0) {
            return errors; // No variables, no validation needed
        }

        // Check each variable has a type selected
        Object.keys(templateVariables).forEach(varNum => {
            const variable = templateVariables[varNum];
            if (!variable.type || variable.type.trim() === '') {
                errors.push(`{{${varNum}}} değişkeni için tür seçimi yapılmalıdır`);
            }
        });

        return errors;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Media Upload Functions
    function showMediaUploadModal() {
        $('#mediaUploadModal').removeClass('hidden');
        $('#mediaFileInput').val('');
        $('#mediaPreview').addClass('hidden');
        $('#uploadMediaBtn').prop('disabled', true);
        $('body').addClass('overflow-hidden');
    }

    function hideMediaUploadModal() {
        $('#mediaUploadModal').addClass('hidden');
        currentMediaComponentId = null;
        $('body').removeClass('overflow-hidden');
    }

    function handleFileSelection(input) {
        const file = input.files[0];
        if (!file) {
            $('#mediaPreview').addClass('hidden');
            $('#uploadMediaBtn').prop('disabled', true);
            return;
        }

        // Validate file
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            showToast('Hata', 'Desteklenmeyen dosya türü.', 'error');
            input.value = '';
            return;
        }

        // Size validation
        const maxSize = file.type.startsWith('image/') ? 5 * 1024 * 1024 : // 5MB for images
                       file.type.startsWith('video/') ? 16 * 1024 * 1024 : // 16MB for videos
                       100 * 1024 * 1024; // 100MB for documents

        if (file.size > maxSize) {
            const maxSizeMB = Math.round(maxSize / (1024 * 1024));
            showToast('Hata', `Dosya boyutu ${maxSizeMB}MB'dan büyük olamaz.`, 'error');
            input.value = '';
            return;
        }

        // Show preview
        showFilePreview(file);
        $('#uploadMediaBtn').prop('disabled', false);
    }

    function showFilePreview(file) {
        const $preview = $('#mediaPreviewContent');
        $preview.empty();

        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $preview.html(`
                    <img src="${e.target.result}" alt="Preview" class="max-w-full max-h-32 mx-auto rounded">
                    <p class="text-xs text-gray-600 mt-2">${file.name} (${formatFileSize(file.size)})</p>
                `);
            };
            reader.readAsDataURL(file);
        } else if (file.type.startsWith('video/')) {
            $preview.html(`
                <div class="bg-gray-100 rounded p-4">
                    <svg class="w-12 h-12 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                    </svg>
                    <p class="text-sm text-gray-700 mt-2">${file.name}</p>
                    <p class="text-xs text-gray-600">${formatFileSize(file.size)}</p>
                </div>
            `);
        } else {
            $preview.html(`
                <div class="bg-gray-100 rounded p-4">
                    <svg class="w-12 h-12 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-sm text-gray-700 mt-2">${file.name}</p>
                    <p class="text-xs text-gray-600">${formatFileSize(file.size)}</p>
                </div>
            `);
        }

        $('#mediaPreview').removeClass('hidden');
    }

    function uploadSelectedMedia() {
        const fileInput = document.getElementById('mediaFileInput');
        const file = fileInput.files[0];

        if (!file) {
            showToast('Hata', 'Dosya seçilmedi.', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        showLoading();

        $.ajax({
            url: '/WhatsAppTemplate/UploadMedia',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                // Update the component with the uploaded file URL
                if (currentMediaComponentId) {
                    const component = templateComponents.find(c => c.id === currentMediaComponentId);
                    if (component) {
                        component.text = window.location.origin + response.url;

                        // Update the input field
                        const $component = $(`.component-builder[data-component-id="${currentMediaComponentId}"]`);
                        $component.find('.component-text').val(component.text);

                        updatePreview();
                    }
                }

                showToast('Başarılı', 'Dosya başarıyla yüklendi.', 'success');
                hideMediaUploadModal();
            } else {
                showToast('Hata', response.message || 'Dosya yüklenirken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Dosya yüklenirken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });        
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Sample Templates Functions
    function showSampleTemplatesModal() {
        $('#sampleTemplatesModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
        loadSampleTemplates();
    }

    function hideSampleTemplatesModal() {
        $('#sampleTemplatesModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }



    function loadSampleTemplates() {
        showLoading();

        $.get('/WhatsAppTemplate/GetSampleTemplates')
            .done(function(response) {
                if (response.success) {
                    renderSampleTemplates(response.templates);
                } else {
                    showToast('Hata', response.message || 'Hazır şablonlar yüklenirken hata oluştu.', 'error');
                }
            })
            .fail(function() {
                showToast('Hata', 'Hazır şablonlar yüklenirken hata oluştu.', 'error');
            })
            .always(function() {
                hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });           
    }

    function renderSampleTemplates(templates) {
        const $container = $('#sampleTemplatesContainer');
        $container.empty();

        if (!templates || templates.length === 0) {
            $container.html('<div class="col-span-full text-center text-gray-500 py-8">Hazır şablon bulunamadı.</div>');
            return;
        }

        templates.forEach(template => {
            const categoryColor = getCategoryColor(template.category);
            const componentCount = template.components ? template.components.length : 0;

            const templateHtml = `
                <div class="sample-template-card border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                     data-sample-id="${template.id}">
                    <div class="flex items-start justify-between mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900 text-sm">${escapeHtml(template.name)}</h4>
                            <p class="text-xs text-gray-600 mt-1">${escapeHtml(template.description)}</p>
                        </div>
                        <span class="text-xs font-medium px-2 py-1 rounded-full ${categoryColor}">
                            ${template.category}
                        </span>
                    </div>

                    <div class="mb-3">
                        <div class="text-xs text-gray-500 mb-2">${componentCount} bileşen</div>
                        <div class="space-y-1">
                            ${template.components.map(comp => `
                                <div class="text-xs px-2 py-1 bg-gray-100 rounded inline-block mr-1">
                                    ${comp.type}
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-xs text-gray-500">${template.language.toUpperCase()}</span>
                        <button class="use-sample-btn text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                                data-sample-id="${template.id}">
                            Düzenle
                        </button>
                    </div>
                </div>
            `;

            $container.append(templateHtml);
        });

        // Bind click events
        $('.use-sample-btn').on('click', function(e) {
            e.stopPropagation();
            const sampleId = $(this).data('sample-id');
            loadSampleToBuilder(sampleId);
        });

        $('.sample-template-card').on('click', function() {
            const sampleId = $(this).data('sample-id');
            loadSampleToBuilder(sampleId);
        });
    }

    function getCategoryColor(category) {
        switch (category) {
            case 'MARKETING':
                return 'bg-purple-100 text-purple-800';
            case 'UTILITY':
                return 'bg-blue-100 text-blue-800';
            case 'AUTHENTICATION':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    function loadSampleToBuilder(sampleId) {
        hideSampleTemplatesModal();

        showLoading();

        $.get(`/WhatsAppTemplate/GetSampleTemplate?id=${sampleId}`)
            .done(function(response) {
                if (response.success) {
                    loadSampleIntoComponentBuilder(response.template);
                    showTemplateModal(); // Show the main create template modal
                } else {
                    showToast('Hata', response.message || 'Şablon yüklenirken hata oluştu.', 'error');
                }
            })
            .fail(function() {
                showToast('Hata', 'Şablon yüklenirken hata oluştu.', 'error');
            })
            .always(function() {
                hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });           
    }

    function showCreateFromSampleModal(sampleId) {
        hideSampleTemplatesModal();

        showLoading();

        $.get(`/WhatsAppTemplate/GetSampleTemplate?id=${sampleId}`)
            .done(function(response) {
                if (response.success) {
                    populateCreateFromSampleModal(response.template);
                    $('#createFromSampleModal').removeClass('hidden');
                    $('body').addClass('overflow-hidden');
                } else {
                    showToast('Hata', response.message || 'Şablon yüklenirken hata oluştu.', 'error');
                }
            })
            .fail(function() {
                showToast('Hata', 'Şablon yüklenirken hata oluştu.', 'error');
            })
            .always(function() {
                hideLoading();
        })
        .fail(function(xhr, status, error) {
            console.error("AJAX error:", xhr, status, error);
            showToast("Hata", "Şablon silinirken bağlantı hatası oluştu.", "error");
        })
        .always(function() {
            hideLoading();
        });           
    }

    function populateCreateFromSampleModal(template) {
        $('#selectedSampleId').val(template.id);
        $('#selectedSampleName').text(template.name);
        $('#sampleTemplateName').val('');

        // Generate preview
        const previewHtml = generateSamplePreview(template);
        $('#samplePreviewContainer').html(previewHtml);
    }

    function generateSamplePreview(template) {
        let html = '<div class="bg-white rounded-lg shadow-sm max-w-xs mx-auto border">';

        // WhatsApp Header
        html += `
            <div class="bg-green-500 text-white px-4 py-2 rounded-t-lg">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">Şirket Adı</span>
                </div>
            </div>
        `;

        // Message Content
        html += '<div class="p-4">';

        if (template.components) {
            const sortedComponents = [...template.components].sort((a, b) => {
                const order = { 'HEADER': 1, 'BODY': 2, 'FOOTER': 3, 'BUTTONS': 4 };
                return (order[a.type] || 5) - (order[b.type] || 5);
            });

            sortedComponents.forEach(component => {
                if (component.type === 'HEADER') {
                    if (component.format === 'TEXT' && component.text) {
                        html += `<div class="font-semibold text-sm text-gray-900 mb-2">${escapeHtml(component.text)}</div>`;
                    } else if (component.format !== 'TEXT' && component.text) {
                        html += `<div class="mb-2">
                            <div class="bg-gray-200 rounded p-2 text-center text-xs text-gray-600">
                                ${component.format} Media<br>
                                <span class="text-xs">${component.text}</span>
                            </div>
                        </div>`;
                    }
                } else if (component.type === 'BODY') {
                    if (component.text) {
                        // Replace variables with sample data
                        let bodyText = component.text;
                        bodyText = bodyText.replace(/\{\{1\}\}/g, 'Ahmet Yılmaz');
                        bodyText = bodyText.replace(/\{\{2\}\}/g, 'ORD-2024-001');
                        bodyText = bodyText.replace(/\{\{3\}\}/g, '₺299,90');
                        bodyText = bodyText.replace(/\{\{4\}\}/g, '15.01.2024');
                        bodyText = bodyText.replace(/\{\{5\}\}/g, 'Kadıköy, İstanbul');
                        bodyText = bodyText.replace(/\{\{6\}\}/g, 'ABC123');

                        html += `<div class="text-sm text-gray-800 mb-2 whitespace-pre-wrap">${escapeHtml(bodyText)}</div>`;
                    }
                } else if (component.type === 'FOOTER') {
                    if (component.text) {
                        html += `<div class="text-xs text-gray-600 mt-2">${escapeHtml(component.text)}</div>`;
                    }
                } else if (component.type === 'BUTTONS') {
                    if (component.buttons && component.buttons.length > 0) {
                        html += '<div class="mt-3 space-y-1">';
                        component.buttons.forEach(button => {
                            if (button.text) {
                                let buttonClass = 'block w-full text-center py-2 px-3 text-sm border rounded';
                                if (button.type === 'URL') {
                                    buttonClass += ' bg-blue-50 border-blue-200 text-blue-800';
                                } else if (button.type === 'PHONE_NUMBER') {
                                    buttonClass += ' bg-green-50 border-green-200 text-green-800';
                                } else {
                                    buttonClass += ' bg-gray-50 border-gray-200 text-gray-800';
                                }
                                html += `<div class="${buttonClass}">${escapeHtml(button.text)}</div>`;
                            }
                        });
                        html += '</div>';
                    }
                }
            });
        }

        html += '</div></div>';
        return html;
    }



    // Template View Functions
    function convertTemplateToComponents(template) {
        templateComponents = [];
        templateVariables = {};

        if (template.components) {
            template.components.forEach((comp, index) => {
                const component = {
                    id: `view_component_${index}`,
                    type: comp.type,
                    text: comp.text || '',
                    format: comp.format || 'TEXT'
                };

                if (comp.type === 'BUTTONS' && comp.buttons) {
                    component.buttons = comp.buttons.map(btn => ({
                        type: btn.type,
                        text: btn.text,
                        url: btn.url,
                        phoneNumber: btn.phoneNumber
                    }));
                }

                templateComponents.push(component);
            });
        }
    }

    function populateViewComponents(components) {
        const container = $('#viewComponentsBuilder');
        container.empty();

        if (!components || components.length === 0) {
            container.html('<p class="text-gray-500 text-sm">Bu şablonda component bulunmuyor.</p>');
            return;
        }

        components.forEach(component => {
            const componentHtml = createViewComponentBuilderHtml(component);
            container.append(componentHtml);
        });
    }

    function createViewComponentBuilderHtml(component) {
        let html = `<div class="component-builder-view border border-gray-200 rounded-lg p-3 bg-gray-50" data-component-id="${component.id}">`;

        // Component header
        html += `<div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-700">${component.type}</span>
                ${getComponentIcon(component.type)}
            </div>
            <span class="text-xs text-gray-500">Sadece Görüntüleme</span>
        </div>`;

        // Component-specific content (read-only)
        if (component.type === 'HEADER') {
            html += createViewHeaderComponentHtml(component);
        } else if (component.type === 'BODY') {
            html += createViewBodyComponentHtml(component);
        } else if (component.type === 'FOOTER') {
            html += createViewFooterComponentHtml(component);
        } else if (component.type === 'BUTTONS') {
            html += createViewButtonsComponentHtml(component);
        }

        html += '</div>';
        return html;
    }

    function createViewHeaderComponentHtml(component) {
        return `
            <div class="space-y-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Format</label>
                    <div class="w-full px-2 py-1 text-sm bg-white border border-gray-300 rounded">
                        ${component.format || 'TEXT'}
                    </div>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">
                        ${component.format === 'TEXT' ? 'Header Metni' : 'Medya URL'}
                    </label>
                    <div class="w-full px-2 py-1 text-sm bg-white border border-gray-300 rounded min-h-8">
                        ${component.text || 'Boş'}
                    </div>
                </div>
            </div>
        `;
    }

    function createViewBodyComponentHtml(component) {
        return `
            <div class="space-y-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Body Metni</label>
                    <div class="w-full px-2 py-1 text-sm bg-white border border-gray-300 rounded min-h-16 whitespace-pre-wrap">
                        ${component.text || 'Boş'}
                    </div>
                </div>
            </div>
        `;
    }

    function createViewFooterComponentHtml(component) {
        return `
            <div>
                <label class="block text-xs font-medium text-gray-700 mb-1">Footer Metni</label>
                <div class="w-full px-2 py-1 text-sm bg-white border border-gray-300 rounded min-h-8">
                    ${component.text || 'Boş'}
                </div>
            </div>
        `;
    }

    function createViewButtonsComponentHtml(component) {
        let html = `
            <div class="space-y-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-2">Butonlar</label>
                    <div class="buttons-container space-y-2">
        `;

        if (component.buttons && component.buttons.length > 0) {
            component.buttons.forEach((button, index) => {
                html += createViewButtonHtml(button, index);
            });
        } else {
            html += '<div class="text-xs text-gray-500 p-2 bg-white border border-gray-300 rounded">Buton yok</div>';
        }

        html += `
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    function createViewButtonHtml(button, index) {
        return `
            <div class="button-item-view border border-gray-200 rounded p-2 bg-white">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xs font-medium text-gray-700">Buton ${index + 1}</span>
                    <span class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">${button.type}</span>
                </div>
                <div class="space-y-2">
                    <div>
                        <label class="block text-xs text-gray-600">Metin:</label>
                        <div class="text-xs text-gray-800">${button.text || 'Boş'}</div>
                    </div>
                    ${button.type === 'URL' && button.url ? `
                        <div>
                            <label class="block text-xs text-gray-600">URL:</label>
                            <div class="text-xs text-blue-600 break-all">${button.url}</div>
                        </div>
                    ` : ''}
                    ${button.type === 'PHONE_NUMBER' && button.phoneNumber ? `
                        <div>
                            <label class="block text-xs text-gray-600">Telefon:</label>
                            <div class="text-xs text-green-600">${button.phoneNumber}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    function updateViewPreview() {
        const $preview = $('#viewWhatsappPreview');
        let previewHtml = '';

        if (templateComponents.length === 0) {
            $preview.html('<div class="text-center text-gray-500 text-sm">Şablon bileşeni bulunamadı</div>');
            return;
        }

        // Sort components in WhatsApp order: HEADER, BODY, FOOTER, BUTTONS
        const sortedComponents = [...templateComponents].sort((a, b) => {
            const order = { 'HEADER': 1, 'BODY': 2, 'FOOTER': 3, 'BUTTONS': 4 };
            return (order[a.type] || 5) - (order[b.type] || 5);
        });

        sortedComponents.forEach(component => {
            previewHtml += createPreviewComponentHtml(component);
        });

        $preview.html(previewHtml);
    }

    function extractAndShowVariables() {
        const bodyComponent = templateComponents.find(c => c.type === 'BODY');
        if (bodyComponent && bodyComponent.text) {
            extractVariablesFromText(bodyComponent.text);
            renderViewVariableDefinitions();
        } else {
            $('#viewVariableDefinitions').html('<p class="text-xs text-gray-500">Değişken bulunamadı</p>');
        }
    }

    function renderViewVariableDefinitions() {
        const $container = $('#viewVariableDefinitions');
        $container.empty();

        const sortedVars = Object.keys(templateVariables).sort((a, b) => parseInt(a) - parseInt(b));

        if (sortedVars.length === 0) {
            $container.html('<p class="text-xs text-gray-500">Değişken bulunamadı</p>');
            return;
        }

        sortedVars.forEach(varNum => {
            const html = `
                <div class="variable-definition-view border border-gray-200 rounded p-2 bg-white">
                    <div class="flex items-center justify-between">
                        <span class="text-xs font-medium text-gray-700">{{${varNum}}}</span>
                        <span class="text-xs text-gray-500">Değişken ${varNum}</span>
                    </div>
                </div>
            `;
            $container.append(html);
        });
    }

    // Add view preview with sample data handler
    $('#viewPreviewWithSampleData').on('click', function() {
        updateViewPreviewWithSampleData();
    });

    function updateViewPreviewWithSampleData() {
        const sampleData = {
            '1': 'Ahmet Yılmaz',
            '2': 'ORD-2024-001',
            '3': '₺299,90',
            '4': '15.01.2024',
            '5': 'Kadıköy, İstanbul',
            '6': 'ABC123'
        };

        const $preview = $('#viewWhatsappPreview');
        let previewHtml = '';

        if (templateComponents.length === 0) {
            $preview.html('<div class="text-center text-gray-500 text-sm">Şablon bileşeni bulunamadı</div>');
            return;
        }

        const sortedComponents = [...templateComponents].sort((a, b) => {
            const order = { 'HEADER': 1, 'BODY': 2, 'FOOTER': 3, 'BUTTONS': 4 };
            return (order[a.type] || 5) - (order[b.type] || 5);
        });

        sortedComponents.forEach(component => {
            let componentText = component.text || '';

            // Replace variables with sample data
            if (component.type === 'BODY') {
                Object.keys(sampleData).forEach(varNum => {
                    componentText = componentText.replace(new RegExp(`\\{\\{${varNum}\\}\\}`, 'g'), sampleData[varNum]);
                });
            }

            const tempComponent = { ...component, text: componentText };
            previewHtml += createPreviewComponentHtml(tempComponent);
        });

        $preview.html(previewHtml);
    }

    // Load sample template into component builder
    function loadSampleIntoComponentBuilder(template) {
        // Clear existing components
        templateComponents = [];
        templateVariables = {};
        // Set template basic info with predefined name (read-only)
        $('#createTemplateName').val(template.id);
        $('#createTemplateCategory').val(template.category);
        $('#createTemplateLanguage').val(template.language);

        // Make template name read-only for sample templates
        $('#createTemplateName').prop('readonly', true);
        $('#createTemplateName').addClass('bg-gray-100 cursor-not-allowed');

        // Add info message about fixed name
        $('#templateNameError').remove(); // Remove any existing error
        if (!$('#templateNameInfo').length) {
            $('#createTemplateName').after(`
                <p id="templateNameInfo" class="text-xs text-blue-600 mt-1">
                    <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    Hazır şablon ismi sabit olarak belirlenmiştir
                </p>
            `);
        }

        // Convert sample components to builder format
        if (template.components && template.components.length > 0) {
            template.components.forEach((comp, index) => {
                const component = {
                    id: `component_${Date.now()}_${index}`,
                    type: comp.type,
                    text: comp.text || '',
                    format: comp.format || 'TEXT'
                };

                // Handle buttons for BUTTONS component
                if (comp.type === 'BUTTONS' && comp.buttons) {
                    component.buttons = comp.buttons.map(btn => ({
                        type: btn.type,
                        text: btn.text,
                        url: btn.url,
                        phoneNumber: btn.phoneNumber
                    }));
                }

                templateComponents.push(component);
            });
        }

        // Update UI
        updateComponentsBuilder();
        updatePreview();
        extractVariablesFromComponents();
        renderVariableDefinitions();

        // Show success message
        showToast('Başarılı', `"${template.name}" şablonu yüklendi. İstediğiniz değişiklikleri yapabilirsiniz.`, 'success');

        // Trigger validation
        validateTemplate();
    }

    function updateComponentsBuilder() {
        const $container = $('#componentsBuilder');
        $container.empty();

        if (templateComponents.length === 0) {
            $container.html('<p class="text-gray-500 text-sm">Henüz component eklenmedi. Yukarıdaki butonları kullanarak component ekleyin.</p>');
            return;
        }

        templateComponents.forEach(component => {
            const componentHtml = createComponentBuilderHtml(component);
            $container.append(componentHtml);
            bindComponentEvents(component.id);
        });
    }

    function extractVariablesFromComponents() {
        templateVariables = {};

        // Extract variables from BODY component
        const bodyComponent = templateComponents.find(c => c.type === 'BODY');
        if (bodyComponent && bodyComponent.text) {
            extractVariablesFromText(bodyComponent.text);
        }
    }

    // Show template modal in create mode
    function showTemplateModal() {
        $('#templateModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');

        // Show create form, hide view form
        $('#createTemplateForm').removeClass('hidden');
        $('#viewTemplateForm').addClass('hidden');

        // Update modal title
        $('#templateModalLabel span').text('Yeni WhatsApp Şablon');
    }

    // Enable template name editing (for new templates)
    function enableTemplateNameEditing() {
        $('#createTemplateName').prop('readonly', false);
        $('#createTemplateName').removeClass('bg-gray-100 cursor-not-allowed');
        $('#createTemplateName').addClass('bg-white');
        $('#templateNameInfo').remove(); // Remove sample template info message
    }

    // Disable template name editing (for sample templates)
    function disableTemplateNameEditing() {
        $('#createTemplateName').prop('readonly', true);
        $('#createTemplateName').addClass('bg-gray-100 cursor-not-allowed');
        $('#createTemplateName').removeClass('bg-white');
    }
});
