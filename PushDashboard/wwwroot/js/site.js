// Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
// for details on configuring this project to bundle and minify static web assets.

// Write your JavaScript code.

// Global confirmation dialog function
function showConfirmationDialog(title, message, confirmText = 'Onayla', cancelText = 'İptal', onConfirm = null, onCancel = null) {
  // Create modal backdrop
  const backdrop = document.createElement('div');
  backdrop.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
  backdrop.id = 'confirmation-modal-backdrop';

  // Create modal content
  const modal = document.createElement('div');
  modal.className = 'relative top-20 mx-auto p-5 border max-w-lg shadow-lg rounded-md bg-white';

  modal.innerHTML = `
    <div class="mt-3">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 class="text-lg leading-6 font-medium text-gray-900 text-center mb-4">${title}</h3>
      <div class="mt-2 px-7 py-3">
        <div class="text-sm text-gray-500">${message}</div>
      </div>
      <div class="flex justify-end space-x-3 px-4 py-3">
        <button id="confirmation-modal-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
          ${cancelText}
        </button>
        <button id="confirmation-modal-confirm" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
          ${confirmText}
        </button>
      </div>
    </div>
  `;

  backdrop.appendChild(modal);
  document.body.appendChild(backdrop);

  // Event listeners
  document.getElementById('confirmation-modal-confirm').addEventListener('click', () => {
    if (onConfirm) onConfirm();
    document.body.removeChild(backdrop);
  });

  document.getElementById('confirmation-modal-cancel').addEventListener('click', () => {
    if (onCancel) onCancel();
    document.body.removeChild(backdrop);
  });

  backdrop.addEventListener('click', (e) => {
    if (e.target === backdrop) {
      if (onCancel) onCancel();
      document.body.removeChild(backdrop);
    }
  });

  // ESC key to close
  const handleEscape = (e) => {
    if (e.key === 'Escape') {
      if (onCancel) onCancel();
      document.body.removeChild(backdrop);
      document.removeEventListener('keydown', handleEscape);
    }
  };

  document.addEventListener('keydown', handleEscape);
}

// Make function globally available
window.showConfirmationDialog = showConfirmationDialog;

// Force modal function - cannot be closed by user
function showForceModal(title, message, confirmText, onConfirm, type = 'warning') {
  // Create modal backdrop
  const backdrop = document.createElement('div');
  backdrop.className = 'fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-[9999]';
  backdrop.id = 'force-modal-backdrop';

  // Create modal content
  const modal = document.createElement('div');
  modal.className = 'relative top-20 mx-auto p-5 border w-96 shadow-2xl rounded-md bg-white';

  const colors = {
    danger: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600'
  };

  modal.innerHTML = `
    <div class="mt-3 text-center">
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full ${type === 'danger' ? 'bg-red-100' : type === 'warning' ? 'bg-yellow-100' : 'bg-blue-100'}">
        <svg class="h-6 w-6 ${colors[type] || colors.info}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">${title}</h3>
      <div class="mt-2 px-7 py-3">
        <p class="text-sm text-gray-500">${message}</p>
      </div>
      <div class="items-center px-4 py-3">
        <button id="force-modal-confirm" class="px-4 py-2 bg-red-500 hover:bg-red-700 text-white text-base font-medium rounded-md w-full">
          ${confirmText}
        </button>
      </div>
    </div>
  `;

  backdrop.appendChild(modal);
  document.body.appendChild(backdrop);

  // Only allow confirm action - no cancel, no ESC, no backdrop click
  document.getElementById('force-modal-confirm').addEventListener('click', () => {
    onConfirm();
    document.body.removeChild(backdrop);
    // Remove ESC key listener
    document.removeEventListener('keydown', preventEscape);
  });

  // Prevent ESC key from closing
  const preventEscape = (e) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  document.addEventListener('keydown', preventEscape);
}
