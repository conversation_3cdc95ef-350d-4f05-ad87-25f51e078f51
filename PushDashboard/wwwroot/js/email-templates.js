$(document).ready(function() {
    let currentTemplateId = null;
    let currentTemplate = null;

    // Template item click handler
    $('.template-item').on('click', function() {
        const templateId = $(this).data('template-id');
        const companyTemplateId = $(this).data('company-template-id');
        loadTemplate(templateId, companyTemplateId);
    });

    // Create template button click handler
    $('#createTemplateBtn').on('click', function(e) {
        e.preventDefault();
        showCreateTemplateModal();
    });

    // Modal close handlers
    $('#closeModalBtn, #cancelBtn, #modalBackdrop').on('click', function() {
        closeTemplateModal();
    });

    $('#closePreviewBtn, #closePreviewModalBtn, #previewModalBackdrop').on('click', function() {
        closePreviewModal();
    });

    $('#closeCreateModalBtn, #cancelCreateBtn').on('click', function() {
        closeCreateTemplateModal();
    });

    // Create template modal backdrop click
    $('#createTemplateModal').on('click', function(e) {
        if (e.target === this) {
            closeCreateTemplateModal();
        }
    });

    // Prevent modal close when clicking inside modal content
    $('#templateModal .inline-block, #previewModal .inline-block, #createTemplateModal > div').on('click', function(e) {
        e.stopPropagation();
    });

    // Load template data
    function loadTemplate(templateId, companyTemplateId) {
        showLoading();

        var url = `/EmailTemplate/GetTemplate/?templateId=${templateId}`;
        if (companyTemplateId) {
            url += `&companyTemplateId=${companyTemplateId}`;
        }

        $.get(url)
            .done(function(response) {
                if (response.success) {
                    // For custom templates, use a positive ID for currentTemplateId
                    currentTemplateId = response.template.id || templateId;
                    currentTemplate = response.template;
                    populateTemplateForm(response.template);
                    showTemplateModal();
                } else {
                    showToast('Hata', response.message || 'Şablon yüklenirken hata oluştu.', 'error');
                }
            })
            .fail(function() {
                showToast('Hata', 'Şablon yüklenirken hata oluştu.', 'error');
            })
            .always(function() {
                hideLoading();
            });
    }

    // Populate form with template data
    function populateTemplateForm(template) {
        $('#templateId').val(template.id);
        $('#templateName').text(template.name);

        // Use custom content if available, otherwise use default
        const subject = template.customSubject || template.defaultSubject || '';
        const content = template.customContent || template.defaultContent || '';

        $('#templateSubject').val(subject);
        $('#templateContent').val(content);

        // Store companyTemplateId for saving
        if (template.companyTemplateId) {
            $('#templateForm').data('company-template-id', template.companyTemplateId);
        } else {
            $('#templateForm').removeData('company-template-id');
        }

        // Show/hide reset button based on template type
        if (template.isCustomTemplate) {
            $('#resetBtn').hide(); // Hide reset button for custom templates
        } else {
            $('#resetBtn').show(); // Show reset button for base templates
        }

        // Populate variables
        populateVariables(template.variables || []);

        // Update modal title
        const customizedText = template.isCustomized ? ' (Özelleştirilmiş)' : '';
        $('#templateModalLabel span').text(`${template.name}${customizedText}`);
    }

    // Populate available variables
    function populateVariables(variables) {
        const variablesList = $('#variablesList');
        variablesList.empty();

        if (variables.length === 0) {
            variablesList.html('<span class="text-muted">Bu şablon için özel değişken bulunmuyor.</span>');
            return;
        }

        variables.forEach(function(variable) {
            const tag = $(`<span class="variable-tag">{{${variable}}}</span>`);
            tag.on('click', function() {
                insertVariableToContent(variable);
            });
            variablesList.append(tag);
        });
    }

    // Insert variable to content at cursor position
    function insertVariableToContent(variable) {
        const textarea = document.getElementById('templateContent');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        const before = text.substring(0, start);
        const after = text.substring(end, text.length);

        textarea.value = before + `{{${variable}}}` + after;
        textarea.selectionStart = textarea.selectionEnd = start + variable.length + 4;
        textarea.focus();
    }

    // Save template
    $('#saveTemplateBtn').on('click', function() {
        const formData = {
            templateId: parseInt($('#templateId').val()),
            subject: $('#templateSubject').val().trim(),
            content: $('#templateContent').val().trim()
        };

        // Add companyTemplateId if this is a custom template
        const companyTemplateId = $('#templateForm').data('company-template-id');
        if (companyTemplateId) {
            formData.companyTemplateId = companyTemplateId;
        }

        if (!formData.subject) {
            showToast('Uyarı', 'Email konusu boş olamaz.', 'warning');
            $('#templateSubject').focus();
            return;
        }

        if (!formData.content) {
            showToast('Uyarı', 'Email içeriği boş olamaz.', 'warning');
            $('#templateContent').focus();
            return;
        }

        showLoading();

        $.ajax({
            url: '/EmailTemplate/SaveTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast('Başarılı', response.message || 'Şablon başarıyla kaydedildi.', 'success');
                closeTemplateModal();

                // Update template item to show it's customized
                $(`.template-item[data-template-id="${currentTemplateId}"]`).addClass('active');
            } else {
                showToast('Hata', response.message || 'Şablon kaydedilirken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Şablon kaydedilirken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        });
    });

    // Preview template
    $('#previewBtn').on('click', function() {
        if (!currentTemplateId) return;

        showLoading();

        // Get current form data for preview
        const previewData = {
            templateId: currentTemplateId,
            subject: $('#templateSubject').val().trim(),
            content: $('#templateContent').val().trim()
        };

        // Add companyTemplateId if this is a custom template
        const companyTemplateId = $('#templateForm').data('company-template-id');
        if (companyTemplateId) {
            previewData.companyTemplateId = companyTemplateId;
        }

        $.ajax({
            url: '/EmailTemplate/PreviewTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(previewData)
        })
        .done(function(response) {
            if (response.success) {
                $('#previewContent').html(response.preview);
                showPreviewModal();
            } else {
                showToast('Hata', response.message || 'Önizleme oluşturulurken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Önizleme oluşturulurken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        });
    });

    // Reset template
    $('#resetBtn').on('click', function() {
        if (!currentTemplateId) return;

        if (!confirm('Bu şablonu varsayılan haline sıfırlamak istediğinizden emin misiniz? Tüm özelleştirmeleriniz kaybolacak.')) {
            return;
        }

        showLoading();

        $.ajax({
            url: '/EmailTemplate/ResetTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ templateId: currentTemplateId }),
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast('Başarılı', response.message || 'Şablon varsayılan haline sıfırlandı.', 'success');

                // Reload template data
                loadTemplate(currentTemplateId);

                // Remove active class from template item
                $(`.template-item[data-template-id="${currentTemplateId}"]`).removeClass('active');
            } else {
                showToast('Hata', response.message || 'Şablon sıfırlanırken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Şablon sıfırlanırken hata oluştu.', 'error');
        })
        .always(function() {
            hideLoading();
        });
    });

    // Modal functions
    function showTemplateModal() {
        $('#templateModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeTemplateModal() {
        $('#templateModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    function showPreviewModal() {
        $('#previewModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closePreviewModal() {
        $('#previewModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    function showCreateTemplateModal() {
        $('#createTemplateModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
        // Clear form
        $('#createTemplateForm')[0].reset();
        // Populate available variables
        populateCreateTemplateVariables();
    }

    function closeCreateTemplateModal() {
        $('#createTemplateModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    // Populate available variables for create template modal
    function populateCreateTemplateVariables() {
        const variablesList = $('#createVariablesList');
        variablesList.empty();

        // Default variables available for all templates
        const defaultVariables = [
            { name: 'customerName', description: 'Müşteri Adı' },
            { name: 'customerEmail', description: 'Müşteri E-posta' },
            { name: 'companyName', description: 'Şirket Adı' },
            { name: 'companyAddress', description: 'Şirket Adresi' },
            { name: 'registrationDate', description: 'Kayıt Tarihi' },
            { name: 'updateDate', description: 'Güncelleme Tarihi' },
            { name: 'updatedFields', description: 'Güncellenen Alanlar' },
            { name: 'itemCount', description: 'Ürün Sayısı' },
            { name: 'basketTotal', description: 'Sepet Toplamı' },
            { name: 'basketUrl', description: 'Sepet URL\'si' },
            { name: 'orderNumber', description: 'Sipariş Numarası' },
            { name: 'orderDate', description: 'Sipariş Tarihi' },
            { name: 'orderTotal', description: 'Sipariş Toplamı' },
            { name: 'deliveryAddress', description: 'Teslimat Adresi' },
            { name: 'trackingUrl', description: 'Kargo Takip URL\'si' }
        ];

        defaultVariables.forEach(function(variable) {
            const tag = $(`
                <div class="variable-tag mb-2 cursor-pointer" data-variable="${variable.name}">
                    <div class="font-medium text-xs">{{${variable.name}}}</div>
                    <div class="text-xs text-gray-500">${variable.description}</div>
                </div>
            `);

            tag.on('click', function() {
                insertVariableToActiveField(variable.name);
            });

            variablesList.append(tag);
        });
    }

    // Insert variable to create template content at cursor position
    function insertVariableToCreateContent(variable) {
        const textarea = document.getElementById('createTemplateContent');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        const before = text.substring(0, start);
        const after = text.substring(end, text.length);

        textarea.value = before + `{{${variable}}}` + after;
        textarea.selectionStart = textarea.selectionEnd = start + variable.length + 4;
        textarea.focus();
    }

    // Insert variable to create template subject at cursor position
    function insertVariableToCreateSubject(variable) {
        const input = document.getElementById('createTemplateSubject');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;
        const before = text.substring(0, start);
        const after = text.substring(end, text.length);

        input.value = before + `{{${variable}}}` + after;
        input.selectionStart = input.selectionEnd = start + variable.length + 4;
        input.focus();
    }

    // Insert variable to the currently active field (subject or content)
    function insertVariableToActiveField(variable) {
        const activeElement = document.activeElement;

        if (activeElement && activeElement.id === 'createTemplateSubject') {
            insertVariableToCreateSubject(variable);
        } else {
            // Default to content area
            insertVariableToCreateContent(variable);
        }
    }

    // Create template form submission
    $('#createTemplateForm').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            name: $('#createTemplateName').val().trim(),
            category: $('#createTemplateCategory').val(),
            description: $('#createTemplateDescription').val().trim(),
            subject: $('#createTemplateSubject').val().trim(),
            content: $('#createTemplateContent').val().trim()
        };

        // Validation
        if (!formData.name) {
            showToast('Uyarı', 'Şablon adı boş olamaz.', 'warning');
            $('#createTemplateName').focus();
            return;
        }

        if (!formData.category) {
            showToast('Uyarı', 'Kategori seçimi gereklidir.', 'warning');
            $('#createTemplateCategory').focus();
            return;
        }

        if (!formData.description) {
            showToast('Uyarı', 'Açıklama boş olamaz.', 'warning');
            $('#createTemplateDescription').focus();
            return;
        }

        if (!formData.subject) {
            showToast('Uyarı', 'Email konusu boş olamaz.', 'warning');
            $('#createTemplateSubject').focus();
            return;
        }

        if (!formData.content) {
            showToast('Uyarı', 'Email içeriği boş olamaz.', 'warning');
            $('#createTemplateContent').focus();
            return;
        }

        showCreateLoading();

        $.ajax({
            url: '/EmailTemplate/CreateTemplate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast('Başarılı', response.message || 'Şablon başarıyla oluşturuldu.', 'success');
                closeCreateTemplateModal();

                // Reload page to show new template
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                showToast('Hata', response.message || 'Şablon oluşturulurken hata oluştu.', 'error');
            }
        })
        .fail(function() {
            showToast('Hata', 'Şablon oluşturulurken hata oluştu.', 'error');
        })
        .always(function() {
            hideCreateLoading();
        });
    });

    // Utility functions
    function showLoading() {
        // Show loading spinner or disable buttons
        $('#saveTemplateBtn, #previewBtn, #resetBtn').prop('disabled', true);
    }

    function hideLoading() {
        // Hide loading spinner or enable buttons
        $('#saveTemplateBtn, #previewBtn, #resetBtn').prop('disabled', false);
    }

    function showCreateLoading() {
        $('#saveCreateBtn').prop('disabled', true).html('<svg class="w-4 h-4 inline mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Oluşturuluyor...');
    }

    function hideCreateLoading() {
        $('#saveCreateBtn').prop('disabled', false).html('<svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>Şablonu Oluştur');
    }

    function showToast(title, message, type) {
        // Use existing toast notification system
        if (typeof window.showToast === 'function') {
            window.showToast(title, message, type);
        } else {
            // Fallback to alert
            alert(`${title}: ${message}`);
        }
    }

    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (settings.type === 'POST' && !settings.crossDomain) {
                const token = $('input[name="__RequestVerificationToken"]').val();
                if (token) {
                    xhr.setRequestHeader('RequestVerificationToken', token);
                }
            }
        }
    });
});
