@model PushDashboard.ViewModels.WhatsAppTemplateIndexViewModel
@{
    ViewData["Title"] = "WhatsApp Şablonları";
}

<div class="mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">WhatsApp Şablonları</h2>
            <p class="text-gray-600">WhatsApp mesaj <PERSON>ablonlarınızı yönetin ve özelleştirin</p>
        </div>
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                    @Model.GroupedFacebookTemplates.Values.SelectMany(x => x).Count() şablon
                </span>
            </div>
            <div class="flex items-center space-x-2">
                <button id="showSampleTemplatesBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    Hazır Şablonlar
                </button>
                <button id="createTemplateBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Yeni Şablon
                </button>
            </div>
        </div>
    </div>
</div>

@if (!Model.GroupedFacebookTemplates.Any())
{
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <div class="text-gray-400 mb-4">
            <svg class="w-16 h-16 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Henüz WhatsApp şablonu yok</h3>
        <p class="text-gray-600">Facebook'ta onaylanmış WhatsApp şablonlarınız burada görünecek.</p>
        <button id="createFirstTemplateBtn" class="mt-4 bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-medium">
            İlk Şablonunuzu Oluşturun
        </button>
    </div>
}
else
{
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach (var group in Model.GroupedFacebookTemplates)
        {
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            @if (group.Key == "MARKETING")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                                </svg>
                            }
                            else if (group.Key == "UTILITY")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            }
                            else if (group.Key == "AUTHENTICATION")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            }
                            <h3 class="text-lg font-semibold">@group.Key</h3>
                        </div>
                        <span class="bg-white bg-opacity-20 text-white text-sm font-medium px-2 py-1 rounded-full">
                            @group.Value.Count
                        </span>
                    </div>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach (var template in group.Value)
                    {
                        <div class="template-item p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                             data-template-id="@template.Id" data-template-name="@template.Name">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900 mb-1">@template.Name</h4>
                                    <p class="text-sm text-gray-600 mb-2">@template.Language.ToUpper() - @template.Category</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="@(template.Status == "APPROVED" ? "bg-green-100 text-green-800" :
                                                      template.Status == "PENDING" ? "bg-yellow-100 text-yellow-800" :
                                                      template.Status == "REJECTED" ? "bg-red-100 text-red-800" :
                                                      "bg-gray-100 text-gray-800") text-xs font-medium px-2 py-1 rounded-full">
                                            @template.Status
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-2 flex space-x-1">
                                    <button class="view-template-btn text-blue-600 hover:text-blue-800 p-1" data-template-id="@template.Id" title="Şablonu Görüntüle">
                                        <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </button>
                                    <button class="delete-template-btn text-red-600 hover:text-red-800 p-1" data-template-id="@template.Id" data-template-name="@template.Name" title="Şablonu Sil">
                                        <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
}

<!-- WhatsApp Template Modal -->
<div id="templateModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="templateModalLabel" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900" id="templateModalLabel">
                    <svg class="w-5 h-5 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span id="templateName">WhatsApp Şablon</span>
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeModalBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6">
                <!-- Create Template Form -->
                <div id="createTemplateForm" class="hidden">
                    <form id="createForm">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Left Column - Basic Info -->
                            <div>
                                <div class="mb-4">
                                    <label for="createTemplateName" class="block text-sm font-medium text-gray-700 mb-2">
                                        Şablon Adı *
                                    </label>
                                    <input type="text" id="createTemplateName" name="name"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                           placeholder="ornek_sablon_adi" required>
                                    <p class="text-xs text-gray-500 mt-1">Küçük harf, rakam ve alt çizgi kullanın</p>
                                </div>

                                <div class="mb-4">
                                    <label for="createTemplateCategory" class="block text-sm font-medium text-gray-700 mb-2">
                                        Kategori *
                                    </label>
                                    <select id="createTemplateCategory" name="category"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                        <option value="MARKETING">Marketing - Pazarlama mesajları</option>
                                        <option value="UTILITY">Utility - İşlemsel mesajlar</option>
                                        <option value="AUTHENTICATION">Authentication - Doğrulama kodları</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label for="createTemplateLanguage" class="block text-sm font-medium text-gray-700 mb-2">
                                        Dil *
                                    </label>
                                    <select id="createTemplateLanguage" name="language"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                        <option value="tr">Türkçe (tr)</option>
                                        <option value="en">English (en)</option>
                                        <option value="en_US">English US (en_US)</option>
                                        <option value="ar">العربية (ar)</option>
                                        <option value="de">Deutsch (de)</option>
                                        <option value="es">Español (es)</option>
                                        <option value="fr">Français (fr)</option>
                                    </select>
                                </div>

                                <!-- Variable Management -->
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Değişken Tanımları</h4>
                                    <div id="variableDefinitions" class="space-y-2 max-h-32 overflow-y-auto">
                                        <!-- Variables will be added here dynamically -->
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Body'de {{1}}, {{2}} kullandığınızda otomatik eklenir</p>
                                </div>
                            </div>

                            <!-- Middle Column - Component Builder -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Şablon Bileşenleri</h4>

                                <!-- Component Buttons -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <button type="button" id="addHeaderBtn" class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                                        + Header
                                    </button>
                                    <button type="button" id="addBodyBtn" class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200">
                                        + Body
                                    </button>
                                    <button type="button" id="addFooterBtn" class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200">
                                        + Footer
                                    </button>
                                    <button type="button" id="addButtonsBtn" class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                                        + Buttons
                                    </button>
                                </div>

                                <!-- Components Container -->
                                <div id="componentsBuilder" class="space-y-3 max-h-96 overflow-y-auto">
                                    <!-- Components will be added here -->
                                </div>

                                <!-- Template Validation -->
                                <div id="templateValidation" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md hidden">
                                    <h5 class="text-sm font-medium text-yellow-800 mb-1">Doğrulama Uyarıları</h5>
                                    <ul id="validationMessages" class="text-xs text-yellow-700 space-y-1">
                                        <!-- Validation messages will appear here -->
                                    </ul>
                                </div>
                            </div>

                            <!-- Right Column - WhatsApp Preview -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-3">WhatsApp Önizleme</h4>
                                <div class="bg-gray-100 rounded-lg p-4">
                                    <div class="bg-white rounded-lg shadow-sm max-w-xs mx-auto">
                                        <!-- WhatsApp Header -->
                                        <div class="bg-green-500 text-white px-4 py-2 rounded-t-lg">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                                    </svg>
                                                </div>
                                                <span class="text-sm font-medium">Şirket Adı</span>
                                            </div>
                                        </div>

                                        <!-- Message Content -->
                                        <div id="whatsappPreview" class="p-4 min-h-32">
                                            <div class="text-center text-gray-500 text-sm">
                                                Şablon bileşenlerini ekleyin
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Preview Controls -->
                                <div class="mt-4 space-y-2">
                                    <button type="button" id="previewWithSampleData" class="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                        Örnek Verilerle Önizle
                                    </button>
                                    <div class="text-xs text-gray-500">
                                        <p>• Header: Resim/video yükleyebilirsiniz</p>
                                        <p>• Body: {{1}}, {{2}} değişkenleri kullanın</p>
                                        <p>• Footer: Opsiyonel kısa metin</p>
                                        <p>• Buttons: URL, telefon veya hızlı yanıt</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                            <button type="button" id="closeModalBtn2" class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                                İptal
                            </button>
                            <button type="button" id="createTemplateBtn2" class="px-4 py-2 text-sm bg-green-500 text-white rounded-md hover:bg-green-600">
                                Şablon Oluştur
                            </button>
                        </div>
                    </form>
                </div>

                <!-- View Template Form -->
                <div id="viewTemplateForm" class="hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column - Template Info -->
                        <div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-3">Şablon Bilgileri</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Ad:</span>
                                        <span class="text-sm font-medium" id="viewTemplateName">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Kategori:</span>
                                        <span class="text-sm font-medium" id="viewTemplateCategory">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Durum:</span>
                                        <span class="text-xs font-medium px-2 py-1 rounded-full" id="viewTemplateStatus">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Dil:</span>
                                        <span class="text-sm font-medium" id="viewTemplateLanguage">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Oluşturulma:</span>
                                        <span class="text-sm font-medium" id="viewTemplateCreated">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Kalite Puanı:</span>
                                        <span class="text-sm font-medium" id="viewTemplateQuality">-</span>
                                    </div>
                                </div>
                            </div>

                            <div id="viewRejectionReasonContainer" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4 hidden">
                                <h4 class="text-sm font-medium text-red-800 mb-2">Red Nedeni</h4>
                                <p class="text-sm text-red-700" id="viewRejectionReason">-</p>
                            </div>

                            <!-- Variable Definitions -->
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Değişken Tanımları</h4>
                                <div id="viewVariableDefinitions" class="space-y-2 max-h-32 overflow-y-auto">
                                    <!-- Variables will be shown here -->
                                </div>
                            </div>
                        </div>

                        <!-- Middle Column - Components -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Şablon Bileşenleri</h4>
                            <div id="viewComponentsBuilder" class="space-y-3 max-h-96 overflow-y-auto">
                                <!-- Components will be shown here in builder format -->
                            </div>
                        </div>

                        <!-- Right Column - WhatsApp Preview -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">WhatsApp Önizleme</h4>
                            <div class="bg-gray-100 rounded-lg p-4">
                                <div class="bg-white rounded-lg shadow-sm max-w-xs mx-auto">
                                    <!-- WhatsApp Header -->
                                    <div class="bg-green-500 text-white px-4 py-2 rounded-t-lg">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                                </svg>
                                            </div>
                                            <span class="text-sm font-medium">Şirket Adı</span>
                                        </div>
                                    </div>

                                    <!-- Message Content -->
                                    <div id="viewWhatsappPreview" class="p-4 min-h-32">
                                        <div class="text-center text-gray-500 text-sm">
                                            Şablon yükleniyor...
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview Controls -->
                            <div class="mt-4 space-y-2">
                                <button type="button" id="viewPreviewWithSampleData" class="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                    Örnek Verilerle Önizle
                                </button>
                                <div class="text-xs text-gray-500">
                                    <p>• Bu şablon Facebook tarafından onaylanmıştır</p>
                                    <p>• Şablon düzenlenemez, sadece görüntülenebilir</p>
                                    <p>• Yeni şablon oluşturmak için "Yeni Şablon" butonunu kullanın</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                        <button type="button" id="closeViewModalBtn" class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                            Kapat
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Media Upload Modal -->
<div id="mediaUploadModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="mediaUploadModalLabel" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900" id="mediaUploadModalLabel">
                    Medya Yükle
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeMediaModalBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Dosya Seç
                    </label>
                    <input type="file" id="mediaFileInput" accept="image/*,video/mp4,application/pdf"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">
                        Desteklenen formatlar: JPG, PNG, GIF (max 5MB), MP4 (max 16MB), PDF (max 100MB)
                    </p>
                </div>

                <div id="mediaPreview" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Önizleme</label>
                    <div id="mediaPreviewContent" class="border border-gray-200 rounded-lg p-4 text-center">
                        <!-- Preview content will be inserted here -->
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button type="button" id="closeMediaModalBtn2" class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                        İptal
                    </button>
                    <button type="button" id="uploadMediaBtn" class="px-4 py-2 text-sm bg-green-500 text-white rounded-md hover:bg-green-600" disabled>
                        Yükle
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Templates Modal -->
<div id="sampleTemplatesModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="sampleTemplatesModalLabel" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900" id="sampleTemplatesModalLabel">
                    Hazır WhatsApp Şablonları
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeSampleModalBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600">
                        Aşağıdaki hazır şablonlardan birini seçerek şablon editörünü açabilirsiniz.
                        Şablon ismi sabit olarak belirlenmiştir, sadece içeriği düzenleyebilirsiniz.
                    </p>
                </div>

                <div id="sampleTemplatesContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    <!-- Sample templates will be loaded here -->
                </div>

                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button type="button" id="closeSampleModalBtn2" class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                        Kapat
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    @Html.AntiForgeryToken()    <script src="~/js/facebook-whatsapp-templates.js"></script>
}
