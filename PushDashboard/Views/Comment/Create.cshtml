@model PushDashboard.ViewModels.CommentRequestCreateViewModel
@{
    ViewData["Title"] = "Yeni Yorum İsteği";
}

<main class="p-4 bg-gray-50">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="@Url.Action("Index")" class="text-gray-600 hover:text-gray-900">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 12H5"></path>
                    <path d="M12 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h2 class="text-2xl font-bold text-gray-800">Yeni <PERSON>steği</h2>
                <p class="text-gray-600">E-ticaret sitesinden yorum çekmek için yeni bir istek oluşturun</p>
            </div>
        </div>
    </div>

    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <form id="commentRequestForm" asp-action="Create" method="post" class="space-y-6">
                @Html.AntiForgeryToken()

                <div>
                    <label asp-for="Request.ProductUrl" class="block text-sm font-medium text-gray-700 mb-2">
                        Ürün URL'si
                    </label>
                    <input asp-for="Request.ProductUrl"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(ViewData.ModelState.IsValid? "" : "border-red-500")"
                           placeholder="https://www.trendyol.com/urun-linki">
                    <span asp-validation-for="Request.ProductUrl" class="text-red-500 text-sm"></span>
                    <p class="mt-1 text-sm text-gray-500">
                        Yorumlarını çekmek istediğiniz ürünün tam URL'sini girin.
                    </p>
                </div>

                <div>
                    <label asp-for="Request.ExternalProductId" class="block text-sm font-medium text-gray-700 mb-2">
                        External Product ID
                    </label>
                    <input asp-for="Request.ExternalProductId"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(ViewData.ModelState.IsValid ? "" : "border-red-500")"
                           placeholder="prd_64042818513">
                    <span asp-validation-for="Request.ExternalProductId" class="text-red-500 text-sm"></span>
                    <p class="mt-1 text-sm text-gray-500">
                        Ürünün benzersiz kimlik numarasını girin.
                    </p>
                </div>

                <div>
                    <label asp-for="Request.ExternalProductUrl" class="block text-sm font-medium text-gray-700 mb-2">
                        External Product URL <span class="text-gray-400">(Opsiyonel)</span>
                    </label>
                    <input asp-for="Request.ExternalProductUrl"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(ViewData.ModelState.IsValid ? "" : "border-red-500")"
                           placeholder="https://external-site.com/product/123">
                    <span asp-validation-for="Request.ExternalProductUrl" class="text-red-500 text-sm"></span>
                    <p class="mt-1 text-sm text-gray-500">
                        Harici ürün URL'si (isteğe bağlı).
                    </p>
                </div>

                <div>
                    <label asp-for="Request.ReviewSource" class="block text-sm font-medium text-gray-700 mb-2">
                        Yorum Kaynağı
                    </label>
                    <select asp-for="Request.ReviewSource"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(ViewData.ModelState.IsValid ? "" : "border-red-500")">
                        <option value="Trendyol">Trendyol</option>
                    </select>
                    <span asp-validation-for="Request.ReviewSource" class="text-red-500 text-sm"></span>
                    <p class="mt-1 text-sm text-gray-500">
                        Yorumların çekileceği e-ticaret platformunu seçin.
                    </p>
                </div>

                <div>
                    <label asp-for="Request.RequestedCommentsCount" class="block text-sm font-medium text-gray-700 mb-2">
                        Çekilecek Yorum Sayısı
                    </label>
                    <input asp-for="Request.RequestedCommentsCount"
                           type="number"
                           min="20"
                           max="1000"
                           id="commentCount"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(ViewData.ModelState.IsValid ? "" : "border-red-500")">
                    <span asp-validation-for="Request.RequestedCommentsCount" class="text-red-500 text-sm"></span>
                    <p class="mt-1 text-sm text-gray-500">
                        Çekilecek yorum sayısını belirtin (20-1000 arası).
                    </p>

                    <!-- Fiyat Bilgisi -->
                    <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-green-800">Tahmini Maliyet</p>
                                <p class="text-xs text-green-600" >Yorum başına <span  id="pricePerComment">₺0.00</span></p>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-green-800" id="estimatedCost">₺0.00</p>
                                <p class="text-xs text-green-600">Toplam</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Info Box -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">
                                Bilgilendirme
                            </h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>İstek oluşturulduktan sonra otomatik olarak işleme alınacaktır.</li>
                                    <li>İşlem süresi üründeki yorum sayısına bağlı olarak değişebilir.</li>
                                    <li>İşlem tamamlandığında yorumları görüntüleyebilirsiniz.</li>
                                    <li>Hata durumunda isteği yeniden deneyebilirsiniz.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="@Url.Action("Index")"
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        İptal
                    </a>
                    <button type="submit"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        İsteği Oluştur
                    </button>
                </div>
            </form>
        </div>

        <!-- Example URLs -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Örnek URL Formatları:</h4>
            <div class="space-y-2 text-sm text-gray-600">
                <div>
                    <strong>Trendyol:</strong>
                    <code class="bg-gray-200 px-2 py-1 rounded text-xs">https://www.trendyol.com/marka/urun-adi-p-12345678</code>
                </div>
                <div>
                    <strong>Hepsiburada:</strong>
                    <code class="bg-gray-200 px-2 py-1 rounded text-xs">https://www.hepsiburada.com/urun-adi-p-HB00000ABC123</code>
                </div>
                <div>
                    <strong>N11:</strong>
                    <code class="bg-gray-200 px-2 py-1 rounded text-xs">https://www.n11.com/urun/urun-adi-12345678</code>
                </div>
            </div>
        </div>
    </div>
</main>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Global confirmation dialog function - defined locally to ensure availability
        function showConfirmationDialog(title, message, confirmText = 'Onayla', cancelText = 'İptal', onConfirm = null, onCancel = null) {
            // Create modal backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            backdrop.id = 'confirmation-modal-backdrop';

            // Create modal content
            const modal = document.createElement('div');
            modal.className = 'relative top-20 mx-auto p-5 border max-w-lg shadow-lg rounded-md bg-white';

            modal.innerHTML = `
                <div class="mt-3">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 text-center mb-4">${title}</h3>
                    <div class="mt-2 px-7 py-3">
                        <div class="text-sm text-gray-500">${message}</div>
                    </div>
                    <div class="flex justify-end space-x-3 px-4 py-3">
                        <button id="confirmation-modal-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            ${cancelText}
                        </button>
                        <button id="confirmation-modal-confirm" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            `;

            backdrop.appendChild(modal);
            document.body.appendChild(backdrop);

            // Event listeners
            document.getElementById('confirmation-modal-confirm').addEventListener('click', () => {
                if (onConfirm) onConfirm();
                document.body.removeChild(backdrop);
            });

            document.getElementById('confirmation-modal-cancel').addEventListener('click', () => {
                if (onCancel) onCancel();
                document.body.removeChild(backdrop);
            });

            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    if (onCancel) onCancel();
                    document.body.removeChild(backdrop);
                }
            });

            // ESC key to close
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    if (onCancel) onCancel();
                    document.body.removeChild(backdrop);
                    document.removeEventListener('keydown', handleEscape);
                }
            };

            document.addEventListener('keydown', handleEscape);
        }
        $(document).ready(function() {
            const pricePerComment = 0.10;

            // Fiyat hesaplama ve bakiye kontrolü fonksiyonu
            function updateEstimatedCost() {
                const commentCount = parseInt($('#commentCount').val()) || 0;
                const totalCost = commentCount * pricePerComment;
                $('#estimatedCost').text('₺' + totalCost.toFixed(2));

                // Bakiye kontrolü yap
                if (commentCount > 0) {
                    checkBalance(commentCount);
                }
            }

            // Bakiye kontrolü fonksiyonu
            function checkBalance(commentCount) {
                $.ajax({
                    url: '@Url.Action("CheckBalance")',
                    type: 'GET',
                    data: { commentCount: commentCount },
                    success: function(response) {
                        if (response.success) {
                            const submitBtn = $('button[type="submit"]');

                            $('#pricePerComment').text('₺' + response.pricePerComment.toFixed(2));

                            if (response.hasBalance) {
                                const costDiv = $('.bg-red-50');
                                // Yeterli bakiye var
                                costDiv.removeClass('bg-red-50 border-red-200').addClass('bg-green-50 border-green-200');
                                costDiv.find('.text-red-800, .text-red-600').removeClass('text-red-800 text-red-600').addClass('text-green-800 text-green-600');
                                submitBtn.prop('disabled', false).text('İsteği Oluştur');
                                costDiv.find('.missing-amount').remove();

                            } else {
                                const costDiv = $('.bg-green-50');
                                // Yetersiz bakiye
                                costDiv.removeClass('bg-green-50 border-green-200').addClass('bg-red-50 border-red-200');
                                costDiv.find('.text-green-800, .text-green-600').removeClass('text-green-800 text-green-600').addClass('text-red-800 text-red-600');
                                submitBtn.prop('disabled', true).text('Yetersiz Bakiye');

                                // Eksik tutar bilgisini göster
                                const missingAmountText = costDiv.find('.missing-amount');
                                missingAmountText.remove()
                                if (missingAmountText.length === 0) {
                                    costDiv.find('.text-right').append(`<p class="text-xs text-red-600 missing-amount">Eksik: ₺${response.missingAmount.toFixed(2)}</p>`);
                                } else {
                                    missingAmountText.text(`Eksik: ₺${response.missingAmount.toFixed(2)}`);
                                }
                            }
                        }
                    },
                    error: function() {
                        console.error('Bakiye kontrolü yapılamadı');
                    }
                });
            }

            // Yorum sayısı değiştiğinde fiyatı güncelle
            $('#commentCount').on('input change', updateEstimatedCost);

            // Sayfa yüklendiğinde ilk hesaplama
            updateEstimatedCost();

            // Auto-extract product ID from URL
            $('#Request_ProductUrl').on('blur', function() {
                const url = $(this).val();
                const externalIdField = $('#Request_ExternalProductId');

                if (url && !externalIdField.val()) {
                    // Try to extract product ID from common e-commerce URL patterns
                    let productId = '';

                    // Trendyol pattern: ends with -p-{id}
                    const trendyolMatch = url.match(/-p-(\d+)$/);
                    if (trendyolMatch) {
                        productId = 'prd_' + trendyolMatch[1];
                    }

                    // Hepsiburada pattern: ends with -p-{id}
                    const hepsiburadaMatch = url.match(/-p-([A-Z0-9]+)$/);
                    if (hepsiburadaMatch) {
                        productId = 'prd_' + hepsiburadaMatch[1];
                    }

                    // N11 pattern: ends with /{id}
                    const n11Match = url.match(/\/(\d+)$/);
                    if (n11Match) {
                        productId = 'prd_' + n11Match[1];
                    }

                    if (productId) {
                        externalIdField.val(productId);
                    }
                }
            });

            // Form validation and confirmation
            $('#commentRequestForm').on('submit', function(e) {
                e.preventDefault();

                const url = $('#Request_ProductUrl').val();
                const externalId = $('#Request_ExternalProductId').val();
                const externalUrl = $('#Request_ExternalProductUrl').val();
                const reviewSource = $('#Request_ReviewSource').val();
                const count = parseInt($('#commentCount').val());

                // Validation
                if (!url || !externalId || !reviewSource || !count || count < 20 || count > 1000) {
                    showNotification('Lütfen tüm zorunlu alanları doğru şekilde doldurun. Yorum sayısı 20-1000 arasında olmalıdır.', 'error');
                    return false;
                }

                // Confirmation dialog
                const confirmationMessage = `
                    <div class="text-left">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">İsteği Oluşturmak İstediğinizden Emin misiniz?</h3>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p><strong>Ürün URL:</strong> ${url}</p>
                            <p><strong>External Product ID:</strong> ${externalId}</p>
                            ${externalUrl ? `<p><strong>External Product URL:</strong> ${externalUrl}</p>` : ''}
                            <p><strong>Yorum Kaynağı:</strong> ${reviewSource}</p>
                            <p><strong>Çekilecek Yorum Sayısı:</strong> ${count}</p>
                        </div>
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                            <p class="text-sm text-yellow-800">
                                <strong>Uyarı:</strong> İstek oluşturulduktan sonra otomatik olarak işleme alınacaktır.
                            </p>
                        </div>
                    </div>
                `;

                // Show custom confirmation dialog
                showConfirmationDialog(
                    'Yorum İsteği Onayı',
                    confirmationMessage,
                    'İsteği Oluştur',
                    'İptal',
                    () => {
                        // User confirmed, submit the form
                        const submitBtn = $('button[type="submit"]');
                        const originalText = submitBtn.text();
                        submitBtn.prop('disabled', true).text('İşleniyor...');

                        // Actually submit the form
                        $('#commentRequestForm')[0].submit();
                    }
                );
            });
        });
    </script>
}
