@model PushDashboard.ViewModels.CommentRequestDetailsViewModel
@{
    ViewData["Title"] = "Yorum Detayları";
}

<main class="p-4 bg-gray-50">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="@Url.Action("Index")" class="text-gray-600 hover:text-gray-900">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 12H5"></path>
                    <path d="M12 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div>
                <h2 class="text-2xl font-bold text-gray-800">Yorum Detayları</h2>
                <p class="text-gray-600">@Model.Request.ExternalProductId için çekilen yorumlar</p>
            </div>
        </div>
    </div>

    <!-- Request Info -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-500">Ürün URL'si</label>
                <p class="mt-1 text-sm text-gray-900 break-all">
                    <a href="@Model.Request.ProductUrl" target="_blank" class="text-primary hover:text-primary-dark">
                        @Model.Request.ShortProductUrl
                    </a>
                </p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">External Product ID</label>
                <p class="mt-1 text-sm text-gray-900">@Model.Request.ExternalProductId</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">Durum</label>
                <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @Model.Request.StatusBadgeClass">
                    @Model.Request.Status
                </span>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">Toplam Yorum</label>
                <p class="mt-1 text-sm text-gray-900">@Model.Details.TotalComments.ToString("N0")</p>
            </div>
        </div>
    </div>

    <!-- Comments -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="p-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">Yorumlar</h3>
                <div class="flex items-center space-x-4">
                    @if (Model.Request.HasComments)
                    {
                        <div class="flex space-x-2">
                            <button onclick="exportHtml(@Model.Request.Id)"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export HTML
                            </button>
                            <button onclick="generateExportUrl(@Model.Request.Id)"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                </svg>
                                Export URL
                            </button>
                        </div>
                    }
                    <div class="text-sm text-gray-500">
                        Sayfa @Model.Details.CurrentPage / @Model.Details.TotalPages
                    </div>
                </div>
            </div>
        </div>

        <div class="divide-y divide-gray-200">
            @foreach (var comment in Model.Details.Comments)
            {
                <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                    <div class="space-y-4">
                        <!-- Comment Header -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    @if (!string.IsNullOrEmpty(comment.user))
                                    {
                                        @comment.user.Substring(0, 1).ToUpper()
                                    }
                                    else
                                    {
                                        <text>?</text>
                                    }
                                </div>
                                <div>
                                    <div class="flex items-center space-x-2">
                                        <h4 class="font-semibold text-gray-900">@comment.user</h4>
                                        @if (!string.IsNullOrEmpty(comment.elit_customer))
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                                                ⭐ Elite
                                            </span>
                                        }
                                    </div>
                                    <p class="text-sm text-gray-500">@comment.date</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <!-- Rating Stars -->
                                <div class="flex items-center space-x-1">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <svg class="w-4 h-4 @(i <= comment.rating ? "text-yellow-400" : "text-gray-300")" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    }
                                    <span class="ml-2 text-sm font-medium text-gray-700">@comment.rating.ToString("F1")</span>
                                </div>
                            </div>
                        </div>

                        <!-- Comment Text -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800 leading-relaxed">@comment.comment</p>
                        </div>

                        <!-- Comment Photos -->
                        @if (comment.photos != null && comment.photos.Any())
                        {
                            <div class="space-y-2">
                                <h5 class="text-sm font-medium text-gray-700">Fotoğraflar (@comment.photos.Count)</h5>
                                <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                                    @foreach (var photo in comment.photos)
                                    {
                                        <div class="aspect-square bg-gray-100 rounded-md overflow-hidden cursor-pointer hover:opacity-75 transition-opacity duration-200"
                                             onclick="openImageModal('@photo')">
                                            <img src="@photo" alt="Yorum fotoğrafı" class="w-full h-full object-cover" loading="lazy" />
                                        </div>
                                    }
                                </div>
                            </div>
                        }

                        <!-- Comment Footer -->
                        <div class="flex items-center justify-between pt-2 border-t border-gray-200">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                @if (!string.IsNullOrEmpty(comment.like_count) && comment.like_count != "0")
                                {
                                    <div class="flex items-center space-x-1">
                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                        </svg>
                                        <span>@comment.like_count beğeni</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (Model.Details.TotalPages > 1)
        {
            <div class="px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        <span class="font-medium">@((Model.Details.CurrentPage - 1) * Model.Details.PageSize + 1)</span> -
                        <span class="font-medium">@Math.Min(Model.Details.CurrentPage * Model.Details.PageSize, Model.Details.TotalComments)</span> /
                        <span class="font-medium">@Model.Details.TotalComments.ToString("N0")</span> yorum
                    </div>
                    <div class="flex space-x-1">
                        @if (Model.Details.HasPreviousPage)
                        {
                            <a href="@Url.Action("Details", new { id = Model.Details.RequestId, page = Model.Details.CurrentPage - 1 })"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Önceki
                            </a>
                        }

                        @{
                            var startPage = Math.Max(1, Model.Details.CurrentPage - 2);
                            var endPage = Math.Min(Model.Details.TotalPages, Model.Details.CurrentPage + 2);
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            var isActive = i == Model.Details.CurrentPage;
                            <a href="@Url.Action("Details", new { id = Model.Details.RequestId, page = i })"
                               class="px-3 py-2 text-sm font-medium @(isActive ? "text-primary bg-primary/10 border-primary" : "text-gray-500 bg-white border-gray-300 hover:bg-gray-50") border rounded-md">
                                @i
                            </a>
                        }

                        @if (Model.Details.HasNextPage)
                        {
                            <a href="@Url.Action("Details", new { id = Model.Details.RequestId, page = Model.Details.CurrentPage + 1 })"
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Sonraki
                            </a>
                        }
                    </div>
                </div>
            </div>
        }

        @if (!Model.Details.Comments.Any())
        {
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz yorum yok</h3>
                <p class="mt-1 text-sm text-gray-500">Bu ürün için henüz yorum çekilmemiş.</p>
            </div>
        }
    </div>
</main>

@section Scripts {
<script>
// Global confirmation dialog function - defined locally to ensure availability
function showConfirmationDialog(title, message, confirmText = 'Onayla', cancelText = 'İptal', onConfirm = null, onCancel = null) {
    // Create modal backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    backdrop.id = 'confirmation-modal-backdrop';

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'relative top-20 mx-auto p-5 border max-w-lg shadow-lg rounded-md bg-white';

    modal.innerHTML = `
        <div class="mt-3">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-center mb-4">${title}</h3>
            <div class="mt-2 px-7 py-3">
                <div class="text-sm text-gray-500">${message}</div>
            </div>
            <div class="flex justify-end space-x-3 px-4 py-3">
                <button id="confirmation-modal-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    ${cancelText}
                </button>
                <button id="confirmation-modal-confirm" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    ${confirmText}
                </button>
            </div>
        </div>
    `;

    backdrop.appendChild(modal);
    document.body.appendChild(backdrop);

    // Event listeners
    document.getElementById('confirmation-modal-confirm').addEventListener('click', () => {
        if (onConfirm) onConfirm();
        document.body.removeChild(backdrop);
    });

    document.getElementById('confirmation-modal-cancel').addEventListener('click', () => {
        if (onCancel) onCancel();
        document.body.removeChild(backdrop);
    });

    backdrop.addEventListener('click', (e) => {
        if (e.target === backdrop) {
            if (onCancel) onCancel();
            document.body.removeChild(backdrop);
        }
    });

    // ESC key to close
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            if (onCancel) onCancel();
            document.body.removeChild(backdrop);
            document.removeEventListener('keydown', handleEscape);
        }
    };

    document.addEventListener('keydown', handleEscape);
}

$(document).ready(function() {
    // Auto-refresh if status is processing
    @if (Model.Request.Status == "İşleniyor")
    {
        <text>
        setTimeout(function() {
            location.reload();
        }, 10000); // Refresh every 10 seconds
        </text>
    }

    // Make comment info sections collapsible
    $('.comment-info').on('click', function() {
        $(this).toggleClass('expanded');
    });
});

function exportHtml(requestId) {
    // Open export HTML in new window
    const exportUrl = '@Url.Action("ExportHtml", "Comment")/' + requestId;
    window.open(exportUrl, '_blank');
}

function generateExportUrl(requestId) {
    $.ajax({
        url: '@Url.Action("ExportUrl", "Comment")',
        type: 'GET',
        data: { id: requestId },
        success: function(response) {
            if (response.success) {
                // Show the URL in a modal or copy to clipboard
                showExportUrlModal(response.url);
            } else {
                showNotification(response.message || 'Export URL oluşturulamadı.', 'error');
            }
        },
        error: function() {
            showNotification('Export URL oluşturulurken hata oluştu.', 'error');
        }
    });
}

function showExportUrlModal(url) {
    const modalContent = `
        <div class="text-left">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Export URL</h3>
            <p class="text-sm text-gray-600 mb-4">
                Bu URL'yi kullanarak yorumları herhangi bir web sitesinde iframe olarak gösterebilirsiniz:
            </p>
            <div class="bg-gray-50 p-3 rounded-md mb-4">
                <input type="text" value="${url}" readonly
                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white"
                       onclick="this.select()">
            </div>
            <div class="bg-blue-50 p-3 rounded-md mb-4">
                <p class="text-sm text-blue-800">
                    <strong>Iframe Kullanımı:</strong><br>
                    <code class="text-xs bg-white px-2 py-1 rounded">&lt;iframe src="${url}" width="100%" height="600"&gt;&lt;/iframe&gt;</code>
                </p>
            </div>
            <div class="flex space-x-2">
                <button onclick="copyToClipboard('${url}')"
                        class="flex-1 bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark">
                    URL'yi Kopyala
                </button>
                <button onclick="window.open('${url}', '_blank')"
                        class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    Önizleme
                </button>
            </div>
        </div>
    `;

    showConfirmationDialog(
        'Export URL',
        modalContent,
        'Kapat',
        null,
        () => {}
    );
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('URL panoya kopyalandı!', 'success');
    }, function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('URL panoya kopyalandı!', 'success');
    });
}

function openImageModal(imageUrl) {
    // Create modal backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4';
    backdrop.id = 'image-modal-backdrop';

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'relative max-w-4xl max-h-full bg-white rounded-lg shadow-xl overflow-hidden';

    modal.innerHTML = `
        <div class="relative">
            <button id="close-image-modal" class="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-all">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img src="${imageUrl}" alt="Yorum fotoğrafı" class="w-full h-auto max-h-[80vh] object-contain" />
        </div>
    `;

    backdrop.appendChild(modal);
    document.body.appendChild(backdrop);

    // Event listeners
    document.getElementById('close-image-modal').addEventListener('click', () => {
        document.body.removeChild(backdrop);
    });

    backdrop.addEventListener('click', (e) => {
        if (e.target === backdrop) {
            document.body.removeChild(backdrop);
        }
    });

    // ESC key to close
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(backdrop);
            document.removeEventListener('keydown', handleEscape);
        }
    };

    document.addEventListener('keydown', handleEscape);
}
</script>

<style>
.comment-info {
    max-height: 100px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    cursor: pointer;
}

.comment-info.expanded {
    max-height: none;
}

.comment-info:hover {
    background-color: #f9fafb;
}

/* Star Rating Styles */
.comment-rating {
    margin: 8px 0;
}

.ratings {
    display: flex;
    align-items: center;
    gap: 2px;
}

.ratings.readonly {
    pointer-events: none;
}

.star-w {
    position: relative;
    width: 16px;
    height: 16px;
    display: inline-block;
}

.star-w .empty {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.star-w .full {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    overflow: hidden;
}

.star {
    width: 16px;
    height: 16px;
    display: block;
    position: relative;
}

.star-w .empty .star {
    color: #e5e7eb;
}

.star-w .full .star {
    color: #fbbf24;
}

.star::before {
    content: "★";
    font-size: 16px;
    line-height: 1;
    display: block;
    text-align: center;
}

/* Comment Header Styles */
.comment-header {
    margin-bottom: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.comment-info-item {
    display: inline-block;
    margin-right: 12px;
    font-size: 12px;
    color: #6b7280;
}

.comment-info-item:last-child {
    margin-right: 0;
}

.comment-info-item b {
    color: #374151;
    font-weight: 600;
}

/* Enhanced comment display */
.comment-content {
    background: #f9fafb;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.comment-text {
    color: #374151;
    line-height: 1.5;
    margin-bottom: 8px;
}

.comment-meta {
    border-top: 1px solid #e5e7eb;
    padding-top: 8px;
    margin-top: 8px;
}
</style>
}
