@model PushDashboard.DTOs.CommentDetailsDto
@{
    Layout = null; // No layout for iframe usage
    ViewData["Title"] = "<PERSON><PERSON><PERSON><PERSON>";
}

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - @Model.ExternalProductId</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
        }
        .stats {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
        }
        .stats span {
            display: inline-block;
            margin: 0 10px;
            font-weight: bold;
            color: #0369a1;
            font-size: 14px;
        }
        .comment {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .comment-text {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 10px;
        }
        .comment-meta {
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .ratings {
            display: flex;
            align-items: center;
            gap: 2px;
            margin: 5px 0;
        }
        .star-w {
            position: relative;
            width: 16px;
            height: 16px;
            display: inline-block;
        }
        .star-w .empty {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .star-w .full {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            overflow: hidden;
        }
        .star {
            width: 16px;
            height: 16px;
            display: block;
            position: relative;
        }
        .star-w .empty .star {
            color: #e5e7eb;
        }
        .star-w .full .star {
            color: #fbbf24;
        }
        .star::before {
            content: '★';
            font-size: 16px;
            line-height: 1;
            display: block;
            text-align: center;
        }
        .comment-info-item {
            display: inline-block;
            margin-right: 12px;
            font-size: 12px;
            color: #6b7280;
        }
        .comment-info-item b {
            color: #374151;
            font-weight: 600;
        }
        .no-comments {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        .no-comments svg {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            color: #ccc;
        }
        .powered-by {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
        }
        .powered-by a {
            color: #0369a1;
            text-decoration: none;
        }
        .powered-by a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ürün Yorumları</h1>
            <p><strong>Ürün:</strong> @Model.ExternalProductId</p>
        </div>
        
        <div class="stats">
            <span>Toplam Yorum: @Model.TotalComments</span>
        </div>

        @if (Model.Comments.Any())
        {
            <div class="comments">
                @foreach (var comment in Model.Comments)
                {
                    <div class="comment">
                        <div class="comment-text">
                            @comment.comment
                        </div>
                        @if (!string.IsNullOrEmpty(comment.info))
                        {
                            <div class="comment-meta">
                                @Html.Raw(comment.info)
                            </div>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <div class="no-comments">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <h3>Henüz yorum yok</h3>
                <p>Bu ürün için henüz yorum bulunmuyor.</p>
            </div>
        }

        <div class="powered-by">
            Powered by <a href="#" target="_blank">PushDashboard</a>
        </div>
    </div>
</body>
</html>
