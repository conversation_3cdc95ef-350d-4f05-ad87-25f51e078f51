@model PushDashboard.ViewModels.CommentRequestIndexViewModel
@{
    ViewData["Title"] = "Yo<PERSON> Taşıma";
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50">
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-800"><PERSON><PERSON> Taşıma</h2>
                <p class="text-gray-600">E-ticaret sitelerinden yorum çekme isteklerinizi yönetin</p>
            </div>
            <a href="@Url.Action("Create")" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark flex items-center">
                <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg>
                Yeni İstek
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6" id="statsContainer">
        <!-- Stats will be loaded via AJAX -->
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="searchInput" placeholder="Ürün URL'si veya External ID ile ara..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">Tüm Durumlar</option>
                    <option value="Sırada">Sırada</option>
                    <option value="İşleniyor">İşleniyor</option>
                    <option value="Hazır">Hazır</option>
                    <option value="Hata">Hata</option>
                </select>
                <select id="sortBy" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="createdat">Oluşturma Tarihi</option>
                    <option value="producturl">Ürün URL'si</option>
                    <option value="status">Durum</option>
                    <option value="commentscount">Yorum Sayısı</option>
                </select>
                <select id="sortDirection" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="desc">Azalan</option>
                    <option value="asc">Artan</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Comment Requests Table -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold">Yorum İstekleri</h3>
        </div>

        <div id="loadingIndicator" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span class="ml-2 text-gray-600">Yükleniyor...</span>
        </div>

        <div id="commentRequestsContainer" class="hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ürün</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">External ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kaynak</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Yorum Sayısı</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Oluşturma</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody id="commentRequestsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div id="paginationContainer" class="px-6 py-3 border-t border-gray-200">
                <!-- Pagination will be loaded via AJAX -->
            </div>
        </div>

        <div id="emptyState" class="hidden text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz yorum isteği yok</h3>
            <p class="mt-1 text-sm text-gray-500">İlk yorum isteğinizi oluşturmak için başlayın.</p>
            <div class="mt-6">
                <a href="@Url.Action("Create")" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Yeni İstek Oluştur
                </a>
            </div>
        </div>
    </div>
</main>

@section Scripts {
<script>
let currentPage = 1;
let currentPageSize = 20;
let currentSearchTerm = '';
let currentStatus = '';
let currentSortBy = 'createdat';
let currentSortDirection = 'desc';
let autoRefreshInterval = null;

$(document).ready(function() {
    loadCommentRequests();

    // Search input with debounce
    let searchTimeout;
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentSearchTerm = $('#searchInput').val();
            currentPage = 1;
            loadCommentRequests();
        }, 500);
    });

    // Filter changes
    $('#statusFilter, #sortBy, #sortDirection').on('change', function() {
        currentStatus = $('#statusFilter').val();
        currentSortBy = $('#sortBy').val();
        currentSortDirection = $('#sortDirection').val();
        currentPage = 1;
        loadCommentRequests();
    });
});

function loadCommentRequests() {
    $('#loadingIndicator').removeClass('hidden');
    $('#commentRequestsContainer').addClass('hidden');
    $('#emptyState').addClass('hidden');

    $.ajax({
        url: '@Url.Action("GetCommentRequests")',
        type: 'GET',
        data: {
            page: currentPage,
            pageSize: currentPageSize,
            searchTerm: currentSearchTerm,
            status: currentStatus,
            sortBy: currentSortBy,
            sortDirection: currentSortDirection
        },
        success: function(response) {
            $('#loadingIndicator').addClass('hidden');

            if (response.success) {
                if (response.data.commentRequests.length > 0) {
                    renderCommentRequests(response.data.commentRequests);
                    renderStats(response.data.stats);
                    renderPagination(response.data.pagination);
                    $('#commentRequestsContainer').removeClass('hidden');

                    // Auto-refresh logic for processing requests
                   // setupAutoRefresh(response.data.commentRequests);
                } else {
                    $('#emptyState').removeClass('hidden');
                    clearAutoRefresh();
                }
            } else {
                showNotification(response.message || 'Veriler yüklenirken hata oluştu.', 'error');
                $('#emptyState').removeClass('hidden');
                clearAutoRefresh();
            }
        },
        error: function() {
            $('#loadingIndicator').addClass('hidden');
            $('#emptyState').removeClass('hidden');
            showNotification('Veriler yüklenirken hata oluştu.', 'error');
        }
    });
}

function renderStats(stats) {
    const statsHtml = `
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Toplam İstek</p>
                    <p class="text-2xl font-bold text-gray-900">${stats.totalRequests}</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Bekleyen</p>
                    <p class="text-2xl font-bold text-gray-900">${stats.pendingRequests}</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">İşleniyor</p>
                    <p class="text-2xl font-bold text-gray-900">${stats.processingRequests}</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Tamamlanan</p>
                    <p class="text-2xl font-bold text-gray-900">${stats.completedRequests}</p>
                </div>
            </div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Toplam Yorum</p>
                    <p class="text-2xl font-bold text-gray-900">${stats.totalComments.toLocaleString()}</p>
                </div>
            </div>
        </div>
    `;
    $('#statsContainer').html(statsHtml);
}

function renderCommentRequests(requests) {
    let html = '';
    requests.forEach(function(request) {
        const createdDate = new Date(request.createdAt).toLocaleDateString('tr-TR');
        const commentsCount = request.actualCommentsCount || request.requestedCommentsCount;
        const hasComments = request.hasComments;

        html += `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 max-w-xs truncate" title="${request.productUrl}">
                        ${request.shortProductUrl}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${request.externalProductId}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        ${request.reviewSource}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-col">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${request.statusBadgeClass}">
                            ${request.status}
                        </span>
                        ${request.status === 'İşleniyor' && request.progressCurrentStep ?
                            `<div class="text-xs text-gray-500 mt-1">${request.progressCurrentStep}</div>` : ''}
                        ${request.status === 'İşleniyor' && request.progressCommentsFound ?
                            `<div class="text-xs text-gray-500">${request.progressCommentsFound} yorum bulundu</div>` : ''}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${request.actualCommentsCount ? request.actualCommentsCount.toLocaleString() : '-'}
                    ${request.requestedCommentsCount ? ` / ${request.requestedCommentsCount.toLocaleString()}` : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${createdDate}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        ${hasComments ? `<a href="@Url.Action("Details")/${request.id}" class="text-primary hover:text-primary-dark">Yorumları Gör</a>` : ''}
                        ${request.status === 'Hata' ? `<button onclick="retryRequest(${request.id})" class="text-yellow-600 hover:text-yellow-900">Tekrar Dene</button>` : ''}
                        <button onclick="deleteRequest(${request.id})" class="text-red-600 hover:text-red-900">Sil</button>
                    </div>
                </td>
            </tr>
        `;
    });
    $('#commentRequestsTableBody').html(html);
}

function renderPagination(pagination) {
    if (pagination.totalPages <= 1) {
        $('#paginationContainer').html('');
        return;
    }

    let html = `
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                <span class="font-medium">${pagination.startItem}</span> - <span class="font-medium">${pagination.endItem}</span> / <span class="font-medium">${pagination.totalItems}</span> sonuç
            </div>
            <div class="flex space-x-1">
    `;

    // Previous button
    if (pagination.hasPreviousPage) {
        html += `<button onclick="changePage(${pagination.currentPage - 1})" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Önceki</button>`;
    }

    // Page numbers
    const startPage = Math.max(1, pagination.currentPage - 2);
    const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === pagination.currentPage;
        html += `<button onclick="changePage(${i})" class="px-3 py-2 text-sm font-medium ${isActive ? 'text-primary bg-primary/10 border-primary' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'} border rounded-md">${i}</button>`;
    }

    // Next button
    if (pagination.hasNextPage) {
        html += `<button onclick="changePage(${pagination.currentPage + 1})" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Sonraki</button>`;
    }

    html += `
            </div>
        </div>
    `;

    $('#paginationContainer').html(html);
}

function changePage(page) {
    currentPage = page;
    loadCommentRequests();
}

function deleteRequest(id) {
    if (!confirm('Bu yorum isteğini silmek istediğinizden emin misiniz?')) {
        return;
    }

    $.ajax({
        url: '@Url.Action("Delete")',
        type: 'POST',
        data: {
            id: id,
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                loadCommentRequests();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('İstek silinirken hata oluştu.', 'error');
        }
    });
}

function retryRequest(id) {
    $.ajax({
        url: '@Url.Action("Retry")',
        type: 'POST',
        data: {
            id: id,
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                loadCommentRequests();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('İstek yeniden işlenirken hata oluştu.', 'error');
        }
    });
}

function setupAutoRefresh(requests) {
    // Clear existing interval
    clearAutoRefresh();

    // Check if there are any processing requests
    const hasProcessingRequests = requests.some(r => r.status === 'İşleniyor' || r.status === 'Sırada');

    if (hasProcessingRequests) {
        // Set up auto-refresh every 10 seconds for processing requests
        autoRefreshInterval = setInterval(function() {
            loadCommentRequests();
        }, 100000);

        console.log('Auto-refresh enabled for processing requests');
    }
}

function clearAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        console.log('Auto-refresh disabled');
    }
}
</script>
}
