@model PushDashboard.ViewModels.EmailTemplateIndexViewModel
@{
    ViewData["Title"] = "Mail Şablonları";
}

<div class="mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">Mail Şablonları</h2>
            <p class="text-gray-600">Email şablonlarınızı yönetin ve özelleştirin</p>
        </div>
        <div class="flex items-center space-x-2">
            <button type="button" id="createTemplateBtn"
                    class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <PERSON><PERSON>ab<PERSON>
            </button>
            <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                @Model.GroupedTemplates.Values.SelectMany(x => x).Count() şablon
            </span>
        </div>
    </div>
</div>

@if (!Model.GroupedTemplates.Any())
{
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
        <svg class="w-12 h-12 text-blue-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-medium text-blue-900 mb-2">Henüz Mail Şablonu Yok</h3>
        <p class="text-blue-700">Henüz kullanılabilir mail şablonu bulunmuyor.</p>
    </div>
}
else
{
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach (var group in Model.GroupedTemplates)
        {
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-primary to-primary-dark text-white px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            @if (group.Key == "Müşteri")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                            }
                            else if (group.Key == "Sepet")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                                </svg>
                            }
                            else if (group.Key == "Sipariş")
                            {
                                <svg class="w-6 h-6 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                            }
                            <h3 class="text-lg font-semibold">@group.Key</h3>
                        </div>
                        <span class="bg-white bg-opacity-20 text-white text-sm font-medium px-2 py-1 rounded-full">
                            @group.Value.Count
                        </span>
                    </div>
                </div>
                <div class="divide-y divide-gray-200">
                    @foreach (var template in group.Value.OrderBy(t => t.SortOrder))
                    {
                        <div class="template-item p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                             data-template-id="@template.Id"
                             @if(template.SortOrder >= 1000 && template.ModuleId.HasValue)
                             {
                                 <text>data-company-template-id="@template.ModuleId.Value"</text>
                             }>
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900 mb-1">@template.Name</h4>
                                    <p class="text-sm text-gray-600 mb-2">@template.Description</p>
                                    <div class="flex items-center space-x-2">
                                        @if (template.ModuleId.HasValue)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1a2 2 0 012-2z" />
                                                </svg>
                                                Modüle Sahipsiniz
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                                Herkese Açık
                                            </span>
                                        }
                                    </div>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
}

<!-- Template Editor Modal -->
<div id="templateModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" id="modalBackdrop"></div>

        <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900" id="templateModalLabel">
                    <svg class="w-5 h-5 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span id="templateName">Şablon Düzenle</span>
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeModalBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6">
                <form id="templateForm">
                    <input type="hidden" id="templateId" name="templateId" />

                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                        <div class="lg:col-span-3">
                            <div class="space-y-6">
                                <div>
                                    <label for="templateSubject" class="block text-sm font-medium text-gray-700 mb-2">Email Konusu</label>
                                    <input type="text" id="templateSubject" name="subject" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                                    <p class="mt-1 text-sm text-gray-500">Değişkenler için {{değişkenAdı}} formatını kullanın</p>
                                </div>

                                <div>
                                    <label for="templateContent" class="block text-sm font-medium text-gray-700 mb-2">Email İçeriği</label>
                                    <textarea id="templateContent" name="content" rows="20" required
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary font-mono text-sm"></textarea>
                                    <p class="mt-1 text-sm text-gray-500">HTML formatında içerik girebilirsiniz</p>
                                </div>
                            </div>
                        </div>

                        <div class="lg:col-span-1">
                            <div class="space-y-4">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">İşlemler</h4>
                                    <div class="space-y-2">
                                        <button type="button" id="previewBtn"
                                                class="w-full px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            Önizleme
                                        </button>
                                        <button type="button" id="resetBtn"
                                                class="w-full px-4 py-2 text-sm font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                                            <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                            Sıfırla
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">Kullanılabilir Değişkenler</h4>
                                    <div id="variablesList" class="bg-gray-50 border border-gray-200 rounded-md p-3 max-h-64 overflow-y-auto">
                                        <!-- Variables will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                <button type="button" id="cancelBtn"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary">
                    <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    İptal
                </button>
                <button type="button" id="saveTemplateBtn"
                        class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                    <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                    </svg>
                    Kaydet
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div id="previewModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" id="previewModalBackdrop"></div>

        <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <svg class="w-5 h-5 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Şablon Önizlemesi
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closePreviewBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div class="mt-6">
                <div id="previewContent" class="border border-gray-200 rounded-lg p-6 bg-white max-h-96 overflow-y-auto">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>

            <div class="flex justify-end pt-6 border-t border-gray-200 mt-6">
                <button type="button" id="closePreviewModalBtn"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary">
                    <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Kapat
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Template Modal -->
<div id="createTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 xl:w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <svg class="w-5 h-5 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Yeni Mail Şablonu Oluştur
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeCreateModalBtn">
                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="createTemplateForm" class="mt-6">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- Left Column - Form Fields -->
                    <div class="lg:col-span-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="createTemplateName" class="block text-sm font-medium text-gray-700 mb-2">
                                    Şablon Adı *
                                </label>
                                <input type="text" id="createTemplateName" name="name"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                       placeholder="Şablon adını girin" required>
                            </div>

                            <div>
                                <label for="createTemplateCategory" class="block text-sm font-medium text-gray-700 mb-2">
                                    Kategori *
                                </label>
                                <select id="createTemplateCategory" name="category"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                                    <option value="">Kategori seçin</option>
                                    <option value="Müşteri">Müşteri</option>
                                    <option value="Sepet">Sepet</option>
                                    <option value="Sipariş">Sipariş</option>
                                    <option value="Genel">Genel</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="createTemplateDescription" class="block text-sm font-medium text-gray-700 mb-2">
                                Açıklama *
                            </label>
                            <input type="text" id="createTemplateDescription" name="description"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Şablon açıklamasını girin" required>
                        </div>

                        <div class="mb-4">
                            <label for="createTemplateSubject" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Konusu *
                            </label>
                            <input type="text" id="createTemplateSubject" name="subject"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Email konusunu girin" required>
                            <p class="text-xs text-gray-500 mt-1">
                                Değişkenler için {{değişkenAdı}} formatını kullanın
                            </p>
                        </div>

                        <div class="mb-4">
                            <label for="createTemplateContent" class="block text-sm font-medium text-gray-700 mb-2">
                                Email İçeriği *
                            </label>
                            <textarea id="createTemplateContent" name="content" rows="12"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-mono text-sm"
                                      placeholder="Email içeriğini girin (HTML desteklenir)" required></textarea>
                            <p class="text-xs text-gray-500 mt-1">
                                HTML formatında içerik girebilirsiniz. Değişkenler için {{değişkenAdı}} formatını kullanın.
                            </p>
                        </div>
                    </div>

                    <!-- Right Column - Variables -->
                    <div class="lg:col-span-1">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Kullanılabilir Değişkenler</h4>
                            <div id="createVariablesList" class="bg-gray-50 border border-gray-200 rounded-md p-3 max-h-80 overflow-y-auto">
                                <!-- Variables will be populated here -->
                            </div>
                            <p class="text-xs text-gray-500 mt-2">
                                Değişkenlere tıklayarak içeriğe ekleyebilirsiniz.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end pt-6 border-t border-gray-200 mt-6 space-x-3">
                    <button type="button" id="cancelCreateBtn"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary">
                        <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        İptal
                    </button>
                    <button type="submit" id="saveCreateBtn"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                        <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Şablonu Oluştur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/email-templates.js"></script>
}

