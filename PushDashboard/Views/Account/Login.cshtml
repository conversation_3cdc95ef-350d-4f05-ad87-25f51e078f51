@model PushDashboard.ViewModels.LoginViewModel

@{
    ViewData["Title"] = "Giri<PERSON> Yap";
    Layout = "_BlankLayout";
}

@section Scripts {
    @await Html.PartialAsync("Partials/_AuthScriptsPartial")
}

<div class="flex min-h-screen items-center justify-center p-4 bg-gray-50">
  <div class="w-full max-w-md">
    @await Html.PartialAsync("Partials/_AuthHeaderPartial")

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="bg-white rounded-lg shadow-md p-8">
      @await Html.PartialAsync("Partials/_LoginFormPartial", Model)
    </div>

    <!-- Kayıt Ol -->
    <div class="text-center mt-6">
      <p class="text-gray-600">
        Hesabınız yok mu?
        <a asp-action="Register" asp-route-returnUrl="@ViewData["ReturnUrl"]" class="text-primary font-medium hover:text-primary-dark">He<PERSON> ka<PERSON><PERSON></a>
      </p>
    </div>

    <!-- <PERSON><PERSON><PERSON> ve Destek -->
    <div class="text-center mt-8">
      <div class="flex justify-center space-x-4 text-sm text-gray-500">
        <a href="#" class="hover:text-gray-700">Yardım</a>
        <a href="#" class="hover:text-gray-700">Gizlilik Politikası</a>
        <a href="#" class="hover:text-gray-700">Kullanım Şartları</a>
      </div>
    </div>
  </div>
</div>
