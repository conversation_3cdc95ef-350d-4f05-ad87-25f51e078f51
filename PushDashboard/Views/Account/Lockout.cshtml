@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON>";
    Layout = "_BlankLayout";
}

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <div>
            <svg class="mx-auto h-16 w-16 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                He<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kilitlendi
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Çok fazla başarısız giriş denemesi yaptınız. Lütfen daha sonra tekrar deneyin.
            </p>
        </div>
        <div>
            <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Ana Sayfaya Dön
            </a>
        </div>
    </div>
</div>
