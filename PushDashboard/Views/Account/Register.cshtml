@model PushDashboard.ViewModels.RegisterViewModel

@{
    ViewData["Title"] = "<PERSON><PERSON>t <PERSON>l";
    Layout = "_BlankLayout";
}

<style>
  .password-strength {
    height: 4px;
    border-radius: 2px;
    transition: all 0.3s ease;
  }
  .strength-weak { background-color: #ef4444; width: 25%; }
  .strength-fair { background-color: #f97316; width: 50%; }
  .strength-good { background-color: #eab308; width: 75%; }
  .strength-strong { background-color: #22c55e; width: 100%; }
</style>

<div class="flex min-h-screen items-center justify-center p-4 bg-gray-50">
  <div class="w-full max-w-lg">
    <!-- <PERSON><PERSON> ve <PERSON>ık -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary-light mb-4">
        <svg class="w-10 h-10 text-primary-dark" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900">Pushonica</h1>
      <p class="text-gray-600 mt-2">Hesabınızı oluşturun ve başlayın</p>
    </div>

    <!-- Kayıt Kartı -->
    <div class="bg-white rounded-lg shadow-sm p-8">
      <div asp-validation-summary="All" class="text-red-500 text-sm mb-4"></div>

      <!-- Kayıt Formu -->
      <form class="space-y-4" asp-controller="Account" asp-action="Register" method="post">
        <input type="hidden" name="returnUrl" value="@ViewData["ReturnUrl"]" />

        <!-- Ad Soyad -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label asp-for="FirstName" class="block text-sm font-medium text-gray-700 mb-1">Ad</label>
            <input
              asp-for="FirstName"
              type="text"
              placeholder="Adınız"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            >
            <span asp-validation-for="FirstName" class="text-red-500 text-sm"></span>
          </div>
          <div>
            <label asp-for="LastName" class="block text-sm font-medium text-gray-700 mb-1">Soyad</label>
            <input
              asp-for="LastName"
              type="text"
              placeholder="Soyadınız"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            >
            <span asp-validation-for="LastName" class="text-red-500 text-sm"></span>
          </div>
        </div>

        <!-- E-posta -->
        <div>
          <label asp-for="Email" class="block text-sm font-medium text-gray-700 mb-1">E-posta</label>
          <input
            asp-for="Email"
            type="email"
            placeholder="<EMAIL>"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            required
          >
          <span asp-validation-for="Email" class="text-red-500 text-sm"></span>
        </div>

        <!-- Şirket -->
        <div>
          <label asp-for="CompanyName" class="block text-sm font-medium text-gray-700 mb-1">Firma Adı</label>
          <input
            asp-for="CompanyName"
            type="text"
            placeholder="Şirket adınız"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            required
          >
          <span asp-validation-for="CompanyName" class="text-red-500 text-sm"></span>
        </div>

        <!-- Şifre -->
        <div>
          <label asp-for="Password" class="block text-sm font-medium text-gray-700 mb-1">Şifre</label>
          <div class="relative">
            <input
              asp-for="Password"
              type="password"
              placeholder="••••••••"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
              minlength="8"
            >
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              id="toggle-password"
            >
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
          <!-- Şifre Gücü Göstergesi -->
          <div class="mt-2">
            <div class="bg-gray-200 rounded-full h-1">
              <div id="password-strength" class="password-strength bg-gray-200"></div>
            </div>
            <p id="password-text" class="text-xs text-gray-500 mt-1">En az 8 karakter, büyük harf, küçük harf ve rakam içermelidir</p>
          </div>
          <span asp-validation-for="Password" class="text-red-500 text-sm"></span>
        </div>

        <!-- Şifre Tekrar -->
        <div>
          <label asp-for="ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Şifre Tekrar</label>
          <div class="relative">
            <input
              asp-for="ConfirmPassword"
              type="password"
              placeholder="••••••••"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            >
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              id="toggle-confirm-password"
            >
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </button>
          </div>
          <p id="password-match" class="text-xs mt-1 hidden"></p>
          <span asp-validation-for="ConfirmPassword" class="text-red-500 text-sm"></span>
        </div>

        <!-- Sözleşmeler -->
        <div class="space-y-3">
          <label class="flex items-start">
            <input
              type="checkbox"
              id="terms"
              name="terms"
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
              required
            >
            <span class="ml-2 text-sm text-gray-700">
              <a href="#" class="text-primary hover:text-primary-dark">Kullanım Şartları</a> ve
              <a href="#" class="text-primary hover:text-primary-dark">Gizlilik Politikası</a>'nı okudum ve kabul ediyorum
            </span>
          </label>
          <label class="flex items-start">
            <input
              type="checkbox"
              id="marketing"
              name="marketing"
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
            >
            <span class="ml-2 text-sm text-gray-700">
              Pazarlama e-postaları ve kampanya bildirimlerini almak istiyorum
            </span>
          </label>
        </div>

        <!-- Kayıt Butonu -->
        <button
          type="submit"
          class="w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          id="submit-btn"
        >
          Hesap Oluştur
        </button>
      </form>
    </div>

    <!-- Giriş Yap -->
    <div class="text-center mt-6">
      <p class="text-gray-600">
        Zaten hesabınız var mı?
        <a asp-action="Login" asp-route-returnUrl="@ViewData["ReturnUrl"]" class="text-primary font-medium hover:text-primary-dark">Giriş yapın</a>
      </p>
    </div>

    <!-- Yardım ve Destek -->
    <div class="text-center mt-8">
      <div class="flex justify-center space-x-4 text-sm text-gray-500">
        <a href="#" class="hover:text-gray-700">Yardım</a>
        <a href="#" class="hover:text-gray-700">Gizlilik Politikası</a>
        <a href="#" class="hover:text-gray-700">Kullanım Şartları</a>
      </div>
    </div>
  </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")

    <script>
      // Şifre göster/gizle işlevselliği
      function togglePasswordVisibility(inputId, buttonId) {
        document.getElementById(buttonId).addEventListener('click', function() {
          const passwordInput = document.getElementById(inputId);
          const icon = this.querySelector('svg');

          if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.innerHTML = `
              <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
              <line x1="1" y1="1" x2="23" y2="23"></line>
            `;
          } else {
            passwordInput.type = 'password';
            icon.innerHTML = `
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
              <circle cx="12" cy="12" r="3"></circle>
            `;
          }
        });
      }

      togglePasswordVisibility('Password', 'toggle-password');
      togglePasswordVisibility('ConfirmPassword', 'toggle-confirm-password');

      // Şifre gücü kontrolü
      document.getElementById('Password').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('password-strength');
        const strengthText = document.getElementById('password-text');

        let strength = 0;
        let feedback = [];

        // Uzunluk kontrolü
        if (password.length >= 8) strength++;
        else feedback.push('en az 8 karakter');

        // Büyük harf kontrolü
        if (/[A-Z]/.test(password)) strength++;
        else feedback.push('büyük harf');

        // Küçük harf kontrolü
        if (/[a-z]/.test(password)) strength++;
        else feedback.push('küçük harf');

        // Rakam kontrolü
        if (/\d/.test(password)) strength++;
        else feedback.push('rakam');

        // Özel karakter kontrolü
        if (/[!#$%^&*(),.?":{}|<>]/.test(password)) strength++;

        // Görsel güncelleme
        strengthBar.className = 'password-strength';
        if (strength === 0) {
          strengthBar.classList.add('bg-gray-200');
          strengthText.textContent = 'En az 8 karakter, büyük harf, küçük harf ve rakam içermelidir';
          strengthText.className = 'text-xs text-gray-500 mt-1';
        } else if (strength <= 2) {
          strengthBar.classList.add('strength-weak');
          strengthText.textContent = `Zayıf şifre. Eksik: ${feedback.join(', ')}`;
          strengthText.className = 'text-xs text-red-600 mt-1';
        } else if (strength === 3) {
          strengthBar.classList.add('strength-fair');
          strengthText.textContent = `Orta şifre. Eksik: ${feedback.join(', ')}`;
          strengthText.className = 'text-xs text-orange-600 mt-1';
        } else if (strength === 4) {
          strengthBar.classList.add('strength-good');
          strengthText.textContent = 'İyi şifre';
          strengthText.className = 'text-xs text-yellow-600 mt-1';
        } else {
          strengthBar.classList.add('strength-strong');
          strengthText.textContent = 'Güçlü şifre';
          strengthText.className = 'text-xs text-green-600 mt-1';
        }
      });

      // Şifre eşleşme kontrolü
      function checkPasswordMatch() {
        const password = document.getElementById('Password').value;
        const confirmPassword = document.getElementById('ConfirmPassword').value;
        const matchText = document.getElementById('password-match');

        if (confirmPassword.length > 0) {
          if (password === confirmPassword) {
            matchText.textContent = 'Şifreler eşleşiyor';
            matchText.className = 'text-xs text-green-600 mt-1';
            matchText.classList.remove('hidden');
          } else {
            matchText.textContent = 'Şifreler eşleşmiyor';
            matchText.className = 'text-xs text-red-600 mt-1';
            matchText.classList.remove('hidden');
          }
        } else {
          matchText.classList.add('hidden');
        }
      }

      document.getElementById('Password').addEventListener('input', checkPasswordMatch);
      document.getElementById('ConfirmPassword').addEventListener('input', checkPasswordMatch);

      // Form gönderimi öncesi ek kontroller
      document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('Password').value;
        const confirmPassword = document.getElementById('ConfirmPassword').value;
        const terms = document.getElementById('terms').checked;

        if (password !== confirmPassword) {
          e.preventDefault();
          alert('Şifreler eşleşmiyor!');
          return;
        }

        if (!terms) {
          e.preventDefault();
          alert('Kullanım şartlarını kabul etmelisiniz!');
          return;
        }
      });
    </script>
}
