@model PushDashboard.ViewModels.ProfileViewModel

@{
    ViewData["Title"] = "Profil";
}

<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Profil Bilgilerim</h1>
        
        @if (TempData["StatusMessage"] != null)
        {
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <span>@TempData["StatusMessage"]</span>
            </div>
        }
        
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form asp-action="Profile" method="post">
                <div asp-validation-summary="ModelOnly" class="text-red-500 text-sm mb-4"></div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label asp-for="FirstName" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="FirstName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="FirstName" class="text-red-500 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="LastName" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="LastName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="LastName" class="text-red-500 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="Email" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="Email" readonly class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
                        <span asp-validation-for="Email" class="text-red-500 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="PhoneNumber" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="PhoneNumber" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="PhoneNumber" class="text-red-500 text-sm"></span>
                    </div>
                </div>
                
                <div class="mt-6 flex items-center justify-between">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
                        Kaydet
                    </button>
                    
                    <a asp-action="ChangePassword" class="text-primary hover:text-primary-dark">
                        Şifremi Değiştir
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
