@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON><PERSON>";
    Layout = "_BlankLayout";
}

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <div>
            <svg class="mx-auto h-16 w-16 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                <PERSON><PERSON><PERSON><PERSON>
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Bu sayfaya eri<PERSON>im yet<PERSON>z bulunmamaktadır.
            </p>
        </div>
        <div>
            <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Ana Sayfaya Dön
            </a>
        </div>
    </div>
</div>
