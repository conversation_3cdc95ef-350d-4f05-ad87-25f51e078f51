@model PushDashboard.ViewModels.TwoFactorVerifyViewModel
@{
    ViewData["Title"] = "<PERSON><PERSON>örlü Doğrulama";
    Layout = "_BlankLayout";
}

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full">
    <!-- Logo -->
    <div class="text-center mb-8">
      <div class="mx-auto h-12 w-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
          <circle cx="12" cy="16" r="1"></circle>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
        </svg>
      </div>
      <h2 class="text-2xl font-bold text-gray-900">İki Faktörlü Doğrulama</h2>
      <p class="text-gray-600 mt-2">Authenticator uygulamanızdan 6 haneli kodu girin</p>
    </div>

    <!-- 2FA Form -->
    <div class="bg-white rounded-xl shadow-lg p-8">
      @if (ViewData["Email"] != null)
      {
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
            </svg>
            <span class="text-blue-800 text-sm">@ViewData["Email"] olarak giriş yapılıyor</span>
          </div>
        </div>
      }

      <form asp-action="VerifyTwoFactor" method="post" class="space-y-6">
        <input type="hidden" asp-for="ReturnUrl" />
        
        <div asp-validation-summary="ModelOnly" class="text-red-500 text-sm mb-4"></div>

        <div>
          <label asp-for="Code" class="block text-sm font-medium text-gray-700 mb-2">
            Doğrulama Kodu
          </label>
          <input asp-for="Code" 
                 class="w-full px-4 py-3 border border-gray-300 rounded-lg text-center text-2xl font-mono tracking-widest focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                 placeholder="000000"
                 maxlength="6"
                 autocomplete="one-time-code"
                 autofocus>
          <span asp-validation-for="Code" class="text-red-500 text-sm"></span>
        </div>

        <div class="flex items-center">
          <input asp-for="RememberMachine" 
                 class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
          <label asp-for="RememberMachine" class="ml-2 block text-sm text-gray-700">
            Bu cihazı 30 gün boyunca hatırla
          </label>
        </div>

        <button type="submit" 
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
          Doğrula ve Giriş Yap
        </button>
      </form>

      <!-- Help Section -->
      <div class="mt-6 pt-6 border-t border-gray-200">
        <div class="text-center">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Koda erişemiyorum</h3>
          <div class="space-y-2 text-sm text-gray-600">
            <p>• Authenticator uygulamanızın açık olduğundan emin olun</p>
            <p>• Cihazınızın saatinin doğru olduğunu kontrol edin</p>
            <p>• Yeni bir kod oluşturulmasını bekleyin (30 saniye)</p>
          </div>
        </div>
        
        <div class="mt-4 text-center">
          <a asp-action="Login" 
             class="text-sm text-primary hover:text-primary-dark">
            ← Giriş sayfasına dön
          </a>
        </div>
      </div>
    </div>

    <!-- Security Notice -->
    <div class="mt-6 text-center">
      <div class="flex justify-center space-x-4 text-xs text-gray-500">
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          Güvenli Bağlantı
        </span>
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
          256-bit Şifreleme
        </span>
      </div>
    </div>
  </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const codeInput = document.querySelector('input[name="Code"]');
            
            if (codeInput) {
                // Auto-format input to numbers only
                codeInput.addEventListener('input', function() {
                    this.value = this.value.replace(/\D/g, '').substring(0, 6);
                    
                    // Auto-submit when 6 digits are entered
                    if (this.value.length === 6) {
                        // Small delay to allow user to see the complete code
                        setTimeout(() => {
                            this.form.submit();
                        }, 500);
                    }
                });
                
                // Handle paste events
                codeInput.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    const numbers = paste.replace(/\D/g, '').substring(0, 6);
                    this.value = numbers;
                    
                    if (numbers.length === 6) {
                        setTimeout(() => {
                            this.form.submit();
                        }, 500);
                    }
                });
                
                // Focus the input
                codeInput.focus();
            }
        });
    </script>
}
