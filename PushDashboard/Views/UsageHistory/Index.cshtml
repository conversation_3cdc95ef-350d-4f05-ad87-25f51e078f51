@using PushDashboard.Models.ViewModels
@model UsageHistoryViewModel
@{
    ViewData["Title"] = "Harcama Geçmişi";
}

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-3xl font-bold text-gray-900">Harcama Geçmişi</h1>
            <p class="text-gray-600 mt-1">@Model.CompanyName - Modül kullanım maliyetleri ve harcama takibi</p>
        </div>
        <div class="text-right">
            <div class="text-sm text-gray-500">Mevcut Bakiye</div>
            <div class="text-2xl font-bold text-primary">₺@Model.CurrentBalance.ToString("N2")</div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
        <!-- Toplam Harcama -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-semibold text-primary uppercase tracking-wide mb-1">
                        Toplam Harcama (30 Gün)
                    </p>
                    <p class="text-2xl font-bold text-gray-900">₺@Model.UsageStats.TotalSpent.ToString("N2")</p>
                </div>
                <div class="p-3 bg-primary/10 rounded-full">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Toplam Kullanım -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-semibold text-green-600 uppercase tracking-wide mb-1">
                        Toplam Kullanım
                    </p>
                    <p class="text-2xl font-bold text-gray-900">@Model.UsageStats.TotalUsages</p>
                </div>
                <div class="p-3 bg-green-100 rounded-full">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- En Çok Kullanılan -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-semibold text-blue-600 uppercase tracking-wide mb-1">
                        En Çok Kullanılan
                    </p>
                    <p class="text-2xl font-bold text-gray-900">
                        @(Model.UsageStats.UsagesByModule.OrderByDescending(x => x.Value).FirstOrDefault().Key ?? "Yok")
                    </p>
                </div>
                <div class="p-3 bg-blue-100 rounded-full">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Ortalama Maliyet -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs font-semibold text-yellow-600 uppercase tracking-wide mb-1">
                        Ortalama Maliyet
                    </p>
                    <p class="text-2xl font-bold text-gray-900">
                        ₺@(Model.UsageStats.TotalUsages > 0 ? (Model.UsageStats.TotalSpent / Model.UsageStats.TotalUsages).ToString("N2") : "0.00")
                    </p>
                </div>
                <div class="p-3 bg-yellow-100 rounded-full">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Filtreler</h3>
        </div>
        <div class="p-6">
            <form id="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="startDate" class="block text-sm font-medium text-gray-700 mb-2">Başlangıç Tarihi</label>
                    <input type="date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                           id="startDate"
                           value="@Model.StartDate.ToString("yyyy-MM-dd")">
                </div>
                <div>
                    <label for="endDate" class="block text-sm font-medium text-gray-700 mb-2">Bitiş Tarihi</label>
                    <input type="date"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                           id="endDate"
                           value="@Model.EndDate.ToString("yyyy-MM-dd")">
                </div>
                <div>
                    <label for="moduleFilter" class="block text-sm font-medium text-gray-700 mb-2">Modül</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                            id="moduleFilter">
                        <option value="">Tüm Modüller</option>
                        @foreach (var module in Model.UsageStats.SpentByModule.Keys)
                        {
                            <option value="@module">@module</option>
                        }
                    </select>
                </div>
                <div class="flex items-end space-x-3">
                    <button type="button"
                            class="flex-1 bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200"
                            onclick="loadUsageHistory()">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Filtrele
                    </button>
                    <button type="button"
                            class="flex-1 bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
                            onclick="resetFilters()">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Temizle
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Usage History Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Harcama Detayları</h3>
        </div>
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="usageHistoryTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modül</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kullanım Türü</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Açıklama</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Maliyet</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Önceki Bakiye</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sonraki Bakiye</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kanal</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kullanıcı</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                        </tr>
                    </thead>
                    <tbody id="usageHistoryTableBody" class="bg-white divide-y divide-gray-200">
                        @foreach (var usage in Model.UsageHistory)
                        {
                            <tr class="@(usage.IsSuccessful ? "hover:bg-gray-50" : "bg-red-50 hover:bg-red-100")">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @usage.CreatedAt.ToString("dd.MM.yyyy HH:mm")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                        @usage.Module.Name
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        @usage.UsageType
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="@usage.Description">
                                    @usage.Description
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900 text-right">
                                    ₺@usage.Cost.ToString("N2")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                    ₺@usage.BalanceBefore.ToString("N2")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                    ₺@usage.BalanceAfter.ToString("N2")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if (!string.IsNullOrEmpty(usage.Channel))
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            @usage.Channel
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-gray-400">-</span>
                                    }
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @usage.User.FirstName @usage.User.LastName
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if (usage.IsSuccessful)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            Başarılı
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800" title="@usage.ErrorMessage">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            Hatalı
                                        </span>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <!-- Sayfa Bilgisi -->
                    <div class="text-sm text-gray-700">
                        <span id="paginationInfo">Gösterilen: 1-20 / 50 kayıt</span>
                    </div>

                    <!-- Sayfa Boyutu Seçimi -->
                    <div class="flex items-center space-x-2">
                        <label for="pageSize" class="text-sm text-gray-700">Sayfa başına:</label>
                        <select id="pageSize" class="px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>

                    <!-- Sayfalama Butonları -->
                    <div class="flex items-center space-x-1" id="paginationControls">
                        <button id="firstPageBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <button id="prevPageBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>

                        <!-- Sayfa Numaraları -->
                        <div id="pageNumbers" class="flex items-center space-x-1">
                            <!-- Dinamik olarak oluşturulacak -->
                        </div>

                        <button id="nextPageBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                        <button id="lastPageBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Module Spending Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Modül Bazlı Harcamalar</h3>
            </div>
            <div class="p-6">
                <canvas id="moduleSpendingChart" class="w-full h-64"></canvas>
            </div>
        </div>

        <!-- Usage Type Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Kullanım Türü Bazlı Harcamalar</h3>
            </div>
            <div class="p-6">
                <canvas id="usageTypeChart" class="w-full h-64"></canvas>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Sayfalama değişkenleri
        let currentPage = 1;
        let pageSize = 20;
        let totalPages = 1;
        let totalRecords = 0;
        // Module Spending Chart
        const moduleData = @Html.Raw(Json.Serialize(Model.UsageStats.SpentByModule));
        const moduleLabels = Object.keys(moduleData);
        const moduleValues = Object.values(moduleData);

        const moduleCtx = document.getElementById('moduleSpendingChart').getContext('2d');
        window.moduleChart = new Chart(moduleCtx, {
            type: 'doughnut',
            data: {
                labels: moduleLabels,
                datasets: [{
                    data: moduleValues,
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Usage Type Chart
        const usageTypeData = @Html.Raw(Json.Serialize(Model.UsageStats.SpentByUsageType));
        const usageTypeLabels = Object.keys(usageTypeData);
        const usageTypeValues = Object.values(usageTypeData);

        const usageTypeCtx = document.getElementById('usageTypeChart').getContext('2d');
        window.usageTypeChart = new Chart(usageTypeCtx, {
            type: 'bar',
            data: {
                labels: usageTypeLabels,
                datasets: [{
                    label: 'Harcama (₺)',
                    data: usageTypeValues,
                    backgroundColor: '#4e73df'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Load usage history with filters and pagination
        function loadUsageHistory(page = 1) {
            currentPage = page;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const moduleFilter = document.getElementById('moduleFilter').value;
            pageSize = parseInt(document.getElementById('pageSize').value);

            // URL parametrelerini oluştur
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            if (moduleFilter) params.append('moduleName', moduleFilter);
            params.append('page', currentPage.toString());
            params.append('pageSize', pageSize.toString());

            fetch(`/UsageHistory/GetUsageHistory?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateUsageHistoryTable(data.data);
                        updatePagination(data.pagination);
                        updateStats(startDate, endDate); // İstatistikleri de güncelle
                    } else {
                        showToast('Hata', data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Hata', 'Veriler yüklenirken hata oluştu.', 'error');
                });
        }

        function updateUsageHistoryTable(data) {
            const tbody = document.getElementById('usageHistoryTableBody');
            tbody.innerHTML = '';

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p class="text-lg font-medium">Veri bulunamadı</p>
                                <p class="text-sm">Seçilen kriterlere uygun harcama kaydı bulunmuyor.</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            data.forEach(usage => {
                const successClass = usage.isSuccessful ? 'hover:bg-gray-50' : 'bg-red-50 hover:bg-red-100';
                const channelBadge = usage.channel ?
                    `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">${usage.channel}</span>` :
                    '<span class="text-gray-400">-</span>';
                const statusBadge = usage.isSuccessful ?
                    `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Başarılı
                    </span>` :
                    `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800" title="${usage.errorMessage || ''}">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Hatalı
                    </span>`;

                const row = `
                    <tr class="${successClass}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(usage.createdAt).toLocaleString('tr-TR')}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">${usage.moduleName}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">${usage.usageType}</span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${usage.description}">${usage.description}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900 text-right">₺${usage.cost.toFixed(2)}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">₺${usage.balanceBefore.toFixed(2)}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">₺${usage.balanceAfter.toFixed(2)}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${channelBadge}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${usage.userName}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // İstatistikleri güncelle
        function updateStats(startDate, endDate) {
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            fetch(`/UsageHistory/GetUsageStats?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatsCards(data.data);
                        updateCharts(data.data);
                    }
                })
                .catch(error => {
                    console.error('Error updating stats:', error);
                });
        }

        // İstatistik kartlarını güncelle
        function updateStatsCards(stats) {
            // Toplam harcama
            const totalSpentElement = document.querySelector('.grid .bg-white:nth-child(1) .text-2xl');
            if (totalSpentElement) {
                totalSpentElement.textContent = `₺${stats.totalSpent.toFixed(2)}`;
            }

            // Toplam kullanım
            const totalUsagesElement = document.querySelector('.grid .bg-white:nth-child(2) .text-2xl');
            if (totalUsagesElement) {
                totalUsagesElement.textContent = stats.totalUsages.toString();
            }

            // En çok kullanılan
            const mostUsedElement = document.querySelector('.grid .bg-white:nth-child(3) .text-2xl');
            if (mostUsedElement) {
                const mostUsedModule = Object.keys(stats.usagesByModule).reduce((a, b) =>
                    stats.usagesByModule[a] > stats.usagesByModule[b] ? a : b, 'Yok');
                mostUsedElement.textContent = mostUsedModule;
            }

            // Ortalama maliyet
            const avgCostElement = document.querySelector('.grid .bg-white:nth-child(4) .text-2xl');
            if (avgCostElement) {
                const avgCost = stats.totalUsages > 0 ? (stats.totalSpent / stats.totalUsages) : 0;
                avgCostElement.textContent = `₺${avgCost.toFixed(2)}`;
            }
        }

        // Grafikleri güncelle
        function updateCharts(stats) {
            // Modül grafiğini güncelle
            if (window.moduleChart) {
                window.moduleChart.data.labels = Object.keys(stats.spentByModule);
                window.moduleChart.data.datasets[0].data = Object.values(stats.spentByModule);
                window.moduleChart.update();
            }

            // Kullanım türü grafiğini güncelle
            if (window.usageTypeChart) {
                window.usageTypeChart.data.labels = Object.keys(stats.spentByUsageType);
                window.usageTypeChart.data.datasets[0].data = Object.values(stats.spentByUsageType);
                window.usageTypeChart.update();
            }
        }

        // Sayfalama güncelleme fonksiyonu
        function updatePagination(pagination) {
            currentPage = pagination.currentPage;
            pageSize = pagination.pageSize;
            totalPages = pagination.totalPages;
            totalRecords = pagination.totalRecords;

            // Sayfa bilgisini güncelle
            const startRecord = ((currentPage - 1) * pageSize) + 1;
            const endRecord = Math.min(currentPage * pageSize, totalRecords);
            document.getElementById('paginationInfo').textContent =
                `Gösterilen: ${startRecord}-${endRecord} / ${totalRecords} kayıt`;

            // Buton durumlarını güncelle
            document.getElementById('firstPageBtn').disabled = !pagination.hasPreviousPage;
            document.getElementById('prevPageBtn').disabled = !pagination.hasPreviousPage;
            document.getElementById('nextPageBtn').disabled = !pagination.hasNextPage;
            document.getElementById('lastPageBtn').disabled = !pagination.hasNextPage;

            // Sayfa numaralarını oluştur
            updatePageNumbers();
        }

        // Sayfa numaralarını oluştur
        function updatePageNumbers() {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            pageNumbersContainer.innerHTML = '';

            // Gösterilecek sayfa aralığını hesapla
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Başlangıç sayfasını ayarla
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // Sayfa numaralarını oluştur
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = `px-3 py-1 text-sm border rounded-md ${
                    i === currentPage
                        ? 'bg-primary text-white border-primary'
                        : 'border-gray-300 hover:bg-gray-100'
                }`;
                pageBtn.onclick = () => loadUsageHistory(i);
                pageNumbersContainer.appendChild(pageBtn);
            }
        }

        // Sayfalama event listener'ları
        document.getElementById('firstPageBtn').onclick = () => loadUsageHistory(1);
        document.getElementById('prevPageBtn').onclick = () => loadUsageHistory(currentPage - 1);
        document.getElementById('nextPageBtn').onclick = () => loadUsageHistory(currentPage + 1);
        document.getElementById('lastPageBtn').onclick = () => loadUsageHistory(totalPages);

        // Sayfa boyutu değiştiğinde
        document.getElementById('pageSize').onchange = () => {
            currentPage = 1; // İlk sayfaya dön
            loadUsageHistory(1);
        };

        function resetFilters() {
            document.getElementById('startDate').value = '@Model.StartDate.ToString("yyyy-MM-dd")';
            document.getElementById('endDate').value = '@Model.EndDate.ToString("yyyy-MM-dd")';
            document.getElementById('moduleFilter').value = '';
            currentPage = 1;
            loadUsageHistory(1);
        }

        function showToast(title, message, type) {
            // Basit toast notification
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
                type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
            }`;
            toast.innerHTML = `<strong>${title}</strong><br>${message}`;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        // Sayfa yüklendiğinde ilk sayfalama bilgilerini ayarla
        document.addEventListener('DOMContentLoaded', function() {
            // İlk yükleme için mevcut verilerin sayfalama bilgilerini hesapla
            const initialDataCount = @Model.UsageHistory.Count;
            totalRecords = initialDataCount; // Bu gerçek toplam değil, sadece başlangıç için
            totalPages = Math.ceil(totalRecords / pageSize);

            // İlk sayfalama UI'ını güncelle
            updatePagination({
                currentPage: 1,
                pageSize: 20,
                totalRecords: initialDataCount,
                totalPages: totalPages,
                hasNextPage: totalPages > 1,
                hasPreviousPage: false
            });

            // Gerçek sayfalama bilgilerini almak için bir kez yükle
            loadUsageHistory(1);
        });
    </script>
}
