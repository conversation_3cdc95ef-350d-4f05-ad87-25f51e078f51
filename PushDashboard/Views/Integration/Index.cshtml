@model PushDashboard.ViewModels.IntegrationIndexViewModel
@{
    ViewData["Title"] = "Entegrasyonlar";
}

@section Scripts
{
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script>
    // Global variable to track connection test status
    let connectionTestPassed = false;
    let currentIntegrationId = null;    // Integration functionality
    function addIntegration(integrationId, integrationName) {
      showIntegrationModal(integrationId, integrationName);
    }

    function showIntegrationModal(integrationId, integrationName) {
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
      modal.id = 'integration-modal';

      modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
              <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Entegrasyon Ekle</h3>
            <div class="mt-2 px-7 py-3">
              <p class="text-sm text-gray-500">
                <strong>${integrationName}</strong> entegrasyonunu hesabınıza eklemek istediğinizden emin misiniz?
              </p>
            </div>
            <div class="items-center px-4 py-3">
              <button id="confirm-integration"
                      class="px-4 py-2 bg-primary text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-primary-dark">
                Ekle
              </button>
              <button id="cancel-integration"
                      class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24">
                İptal
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      // Event listeners
      document.getElementById('confirm-integration').addEventListener('click', () => {
        performAddIntegration(integrationId, integrationName);
        document.body.removeChild(modal);
      });

      document.getElementById('cancel-integration').addEventListener('click', () => {
        document.body.removeChild(modal);
      });

      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          document.body.removeChild(modal);
        }
      });
    }

    async function performAddIntegration(integrationId, integrationName) {
      try {
        // Show loading notification
        showNotification('Entegrasyon ekleniyor...', 'info');

        // Get anti-forgery token
        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

        // Prepare integration data
        const integrationData = {
          integrationId: parseInt(integrationId)
        };

        // Make API call
        const response = await axios.post('/Integration/AddIntegration', integrationData, {
          headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
          }
        });

        if (response.data.success) {
          // Show success notification
          showNotification(response.data.message, 'success');

          console.log('Integration added successfully, reloading page...');

          // Reload page to show updated integration status
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showNotification(response.data.message, 'error');
        }

      } catch (error) {
        console.error('Integration add error:', error);
        if (error.response && error.response.data && error.response.data.message) {
          showNotification(error.response.data.message, 'error');
        } else {
          showNotification('Entegrasyon eklenirken bir hata oluştu.', 'error');
        }
      }
    }

    // Drawer işlevselliği - DOM yüklendikten sonra çalıştır
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, initializing drawer...');
      initializeDrawer();
    });

    function initializeDrawer() {
      const drawer = document.querySelector('.drawer');
      const drawerOverlay = document.querySelector('.drawer-overlay');
      const closeDrawerButton = document.getElementById('close-drawer');
      const drawerTitle = document.getElementById('drawer-title');
      const drawerContent = document.getElementById('drawer-content');

      console.log('Drawer elements:', { drawer, drawerOverlay, closeDrawerButton, drawerTitle, drawerContent });

      if (!drawer || !drawerOverlay || !closeDrawerButton || !drawerTitle || !drawerContent) {
        console.error('Drawer elements not found');
        return;
      }

      // Settings butonlarına event listener ekle
      attachSettingsListeners();

      // Kapat butonuna tıklandığında drawer'ı kapat
      closeDrawerButton.addEventListener('click', closeDrawer);

      // Overlay'e tıklandığında drawer'ı kapat
      drawerOverlay.addEventListener('click', closeDrawer);
    }

    function attachSettingsListeners() {
      const settingsButtons = document.querySelectorAll('.settings-btn');
      console.log('Found settings buttons:', settingsButtons.length);

      settingsButtons.forEach((button, index) => {
        console.log(`Attaching listener to button ${index}:`, button);
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const integrationName = button.getAttribute('data-integration-name');
          const integrationId = button.getAttribute('data-integration-id');
          console.log('Settings button clicked:', integrationName, integrationId);
          openDrawer(integrationName, integrationId);
        });
      });
    }

    // Drawer'ı açma fonksiyonu
    async function openDrawer(integrationName, integrationId) {
      const drawer = document.querySelector('.drawer');
      const drawerOverlay = document.querySelector('.drawer-overlay');
      const drawerTitle = document.getElementById('drawer-title');
      const drawerContent = document.getElementById('drawer-content');

      if (!drawer || !drawerOverlay || !drawerTitle || !drawerContent) {
        console.error('Drawer elements not found');
        return;
      }

      // Başlık ayarla
      drawerTitle.textContent = integrationName + ' Entegrasyon Ayarları';

      // Loading göster
      drawerContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span class="ml-2">Ayarlar yükleniyor...</span>
        </div>
      `;

      // Drawer'ı aç
      drawer.classList.add('open');
      drawerOverlay.classList.add('active');
      document.body.style.overflow = 'hidden';

      try {
        // Entegrasyon ayarlarını yükle
        await loadIntegrationSettings(integrationId, integrationName);
      } catch (error) {
        console.error('Error loading integration settings:', error);
        drawerContent.innerHTML = `
          <div class="text-center py-8">
            <div class="text-red-500 mb-2">Ayarlar yüklenirken hata oluştu</div>
            <button onclick="closeDrawer()" class="px-4 py-2 bg-gray-500 text-white rounded-md">Kapat</button>
          </div>
        `;
      }
    }

    async function loadIntegrationSettings(integrationId, integrationName) {
      // Reset connection test status when opening settings
      connectionTestPassed = false;
      currentIntegrationId = integrationId;      const drawerContent = document.getElementById('drawer-content');

      // Entegrasyon tipine göre form oluştur
      let formContent = '';

      if (integrationName === 'Ticimax') {
        // Ticimax entegrasyonu formu
        formContent = `
          <!-- Tab Navigation -->
          <div class="border-b border-gray-200 mb-4">
            <nav class="-mb-px flex space-x-8">
              <button type="button" class="ticimax-tab-button active border-b-2 border-primary text-primary py-2 px-1 text-sm font-medium" data-tab="connection">
                Bağlantı Ayarları
              </button>
              <button type="button" class="ticimax-tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 text-sm font-medium" data-tab="status-mapping" id="status-mapping-tab" style="display: none;">
                Sipariş Durumu Eşleştirme
              </button>
            </nav>
          </div>

          <!-- Connection Settings Tab -->
          <div id="connection-tab" class="ticimax-tab-content">
            <form id="integration-settings-form" data-integration-id="${integrationId}">
                <label class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                <input type="text" name="apiUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://api.ticimax.com/v1/">
                <label class="block text-sm font-medium text-gray-700 mb-1">API Anahtarı</label>
                <input type="password" name="apiKey" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="API anahtarınızı girin">
                <label class="block text-sm font-medium text-gray-700 mb-1">Senkronizasyon Sıklığı (saat)</label>
                <select name="syncFrequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option value="1">Her saatte bir</option>
                  <option value="2">Her 2 saatte bir</option>
                  <option value="4">Her 4 saatte bir</option>
                  <option value="6">Her 6 saatte bir</option>
                  <option value="12">Her 12 saatte bir</option>
                  <option value="24">Günde bir kez</option>
                </select>
                <label class="block text-sm font-medium text-gray-700 mb-2">Senkronizasyon Seçenekleri</label>
                <div class="space-y-2">
                    <input type="checkbox" name="syncCustomers" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                    <span class="ml-2 text-sm text-gray-700">Müşterileri senkronize et</span>
                    <input type="checkbox" name="syncCarts" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                    <span class="ml-2 text-sm text-gray-700">Sepetleri senkronize et</span>
              </div>
            </form>
          </div>

          <!-- Status Mapping Tab -->
          <div id="status-mapping-tab-content" class="ticimax-tab-content hidden">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <h4 class="text-sm font-medium text-blue-800">Sipariş Durumu Eşleştirme</h4>
                  <p class="text-sm text-blue-700 mt-1">Ticimax'tan gelen sipariş durumlarını kendi sistem durumlarınızla eşleştirin. Bu sayede bildirimler doğru durumlar için gönderilir.</p>
                </div>
              </div>
            </div>

            <div id="status-mapping-container">
              <!-- Status mappings will be loaded here -->
            </div>
          </div>
        `;
      } else if (integrationName === 'IKAS' || integrationName === 'Ideasoft' || integrationName === 'Tsoft') {
        // Diğer e-ticaret entegrasyonu formu
        formContent = `
          <form id="integration-settings-form" data-integration-id="${integrationId}">
              <label class="block text-sm font-medium text-gray-700 mb-1">API URL</label>
              <input type="text" name="apiUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://api.example.com/v1/">
              <label class="block text-sm font-medium text-gray-700 mb-1">API Anahtarı</label>
              <input type="password" name="apiKey" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="API anahtarınızı girin">
              <label class="block text-sm font-medium text-gray-700 mb-1">Mağaza ID</label>
              <input type="text" name="storeId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Mağaza ID'nizi girin">
              <label class="block text-sm font-medium text-gray-700 mb-1">Senkronizasyon Sıklığı (dakika)</label>
              <select name="syncFrequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="15">Her 15 dakikada bir</option>
                <option value="30">Her 30 dakikada bir</option>
                <option value="60">Her saatte bir</option>
                <option value="120">Her 2 saatte bir</option>
                <option value="1440">Günde bir kez</option>
              </select>
              <label class="block text-sm font-medium text-gray-700 mb-2">Senkronizasyon Seçenekleri</label>
              <div class="space-y-2">
                  <input type="checkbox" name="syncProducts" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                  <span class="ml-2 text-sm text-gray-700">Ürünleri senkronize et</span>
                  <input type="checkbox" name="syncOrders" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                  <span class="ml-2 text-sm text-gray-700">Siparişleri senkronize et</span>
                  <input type="checkbox" name="syncCustomers" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                  <span class="ml-2 text-sm text-gray-700">Müşterileri senkronize et</span>
            </div>
          </form>
        `;
      }      else if (integrationName === 'WhatsApp') {
        // WhatsApp entegrasyonu formu
        formContent = `
          <form id="integration-settings-form" data-integration-id="${integrationId}">
              <label class="block text-sm font-medium text-gray-700 mb-1">Access Token</label>
              <input type="password" name="accessToken" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="WhatsApp Business API Access Token">
              <p class="text-xs text-gray-500 mt-1">Facebook Business hesabınızdan alacağınız access token</p>
              <label class="block text-sm font-medium text-gray-700 mb-1">Business Account ID</label>
              <input type="text" name="businessAccountId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="***************">
              <p class="text-xs text-gray-500 mt-1">WhatsApp Business hesap ID'niz</p>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number ID</label>
              <input type="text" name="phoneNumberId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="***************">
              <p class="text-xs text-gray-500 mt-1">WhatsApp Business telefon numarası ID'si</p>
              <label class="block text-sm font-medium text-gray-700 mb-2">Ayarlar</label>
              <div class="space-y-2">
                  <input type="checkbox" name="autoNotifications" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                  <span class="ml-2 text-sm text-gray-700">Otomatik bildirimler</span>
            </div>
          </form>
        `;
      } else if (integrationName === 'Telegram') {
        // Telegram entegrasyonu formu
        formContent = `
          <form id="integration-settings-form" data-integration-id="${integrationId}">
              <label class="block text-sm font-medium text-gray-700 mb-1">Bot Token</label>
              <input type="password" name="botToken" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Bot token'ınızı girin">
              <label class="block text-sm font-medium text-gray-700 mb-1">Chat ID</label>
              <input type="text" name="chatId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Chat ID'nizi girin">
              <label class="block text-sm font-medium text-gray-700 mb-1">Webhook URL</label>
              <input type="url" name="webhookUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://yourapp.com/webhook">
              <label class="block text-sm font-medium text-gray-700 mb-2">Ayarlar</label>
              <div class="space-y-2">
                  <input type="checkbox" name="enableCommands" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                  <span class="ml-2 text-sm text-gray-700">Bot komutlarını etkinleştir</span>
                  <input type="checkbox" name="autoNotifications" class="rounded border-gray-300 text-primary focus:ring-primary">
                  <span class="ml-2 text-sm text-gray-700">Otomatik bildirimler</span>
            </div>
          </form>
        `;
      } else if (integrationName === 'Email SMTP') {
        // Email SMTP entegrasyonu formu
        formContent = `
          <form id="integration-settings-form" data-integration-id="${integrationId}">
              <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Sunucusu</label>
              <input type="text" name="smtpServer" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="smtp.gmail.com">
              <label class="block text-sm font-medium text-gray-700 mb-1">SMTP Port</label>
              <input type="number" name="smtpPort" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="587" value="587">
              <label class="block text-sm font-medium text-gray-700 mb-1">Kullanıcı Adı</label>
              <input type="email" name="username" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="<EMAIL>">
              <label class="block text-sm font-medium text-gray-700 mb-1">Şifre</label>
              <input type="password" name="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Email şifrenizi girin">
              <label class="block text-sm font-medium text-gray-700 mb-1">Gönderen Email</label>
              <input type="email" name="fromEmail" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="<EMAIL>">
              <label class="block text-sm font-medium text-gray-700 mb-1">Gönderen Adı</label>
              <input type="text" name="fromName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Şirket Adı">
                <input type="checkbox" name="enableSsl" class="rounded border-gray-300 text-primary focus:ring-primary" checked>
                <span class="ml-2 text-sm text-gray-700">SSL/TLS kullan</span>
          </form>
        `;
      }

      // Form ve butonları ekle
      drawerContent.innerHTML = `
        ${formContent}
        <div class="flex justify-end space-x-2 mt-6 pt-4 border-t">
          <button type="button" onclick="closeDrawer()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
            İptal
          </button>
          <button type="button" onclick="testIntegration(${integrationId})" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            Bağlantıyı Test Et
          </button>
          <button type="button" onclick="saveIntegrationSettings(${integrationId})" class="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed" disabled>
            Kaydet (Önce Bağlantıyı Test Edin)
          </button>
        </div>
      `;

      // Mevcut ayarları yükle
      await loadCurrentSettings(integrationId);

      // Ticimax için tab functionality ekle
      if (integrationName === 'Ticimax') {
        initializeTicimaxTabs(integrationId);
      }
    }

    // Drawer'ı kapatma fonksiyonu
    function closeDrawer() {
      const drawer = document.querySelector('.drawer');
      const drawerOverlay = document.querySelector('.drawer-overlay');

      if (drawer && drawerOverlay) {
        drawer.classList.remove('open');
        drawerOverlay.classList.remove('active');
        document.body.style.overflow = '';
      }
    }

    // Mevcut ayarları yükle
    async function loadCurrentSettings(integrationId) {
      try {
        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
        const response = await axios.get(`/Integration/GetSettings?integrationid=${integrationId}`, {
          headers: {
            'RequestVerificationToken': token
          }
        });

        if (response.data.success && response.data.settings) {
          const settings = response.data.settings;
          const form = document.getElementById('integration-settings-form');

          if (form) {
            // Form alanlarını doldur
            Object.keys(settings).forEach(key => {
              const input = form.querySelector(`[name="${key}"]`);
              if (input) {
                if (input.type === 'checkbox') {
                  input.checked = settings[key] === true || settings[key] === 'true';
                } else {
                  input.value = settings[key] || '';
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('Error loading current settings:', error);
      }
    }

    // Entegrasyon ayarlarını kaydet
    async function saveIntegrationSettings(integrationId) {
      try {
        const form = document.getElementById('integration-settings-form');
        if (!form) return;

        // Form verilerini topla
        const formData = new FormData(form);
        const settings = {};

        for (let [key, value] of formData.entries()) {
          const input = form.querySelector(`[name="${key}"]`);
          if (input && input.type === 'checkbox') {
            settings[key] = input.checked;
          } else {
            settings[key] = value;
          }
        }

        // Checkbox'ları ayrıca kontrol et (unchecked olanlar FormData'ya eklenmez)
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
          if (!settings.hasOwnProperty(checkbox.name)) {
            settings[checkbox.name] = false;
        // Check if connection test passed
        if (!connectionTestPassed) {
          showNotification('Lütfen önce bağlantıyı test edin ve başarılı olduğundan emin olun.', 'error');
          return;
        }          }
        });

        showNotification('Ayarlar kaydediliyor...', 'info');

        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
        const response = await axios.post('/Integration/SaveSettings', {
          integrationId: parseInt(integrationId),
          settings: settings
        }, {
          headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
          }
        });

        if (response.data.success) {
          showNotification('Ayarlar başarıyla kaydedildi!', 'success');
          closeDrawer();

          // Sayfayı yenile
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showNotification(response.data.message || 'Ayarlar kaydedilirken hata oluştu.', 'error');
        }

      } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('Ayarlar kaydedilirken hata oluştu.', 'error');
      }
    }

    // Entegrasyon bağlantısını test et
    async function testIntegration(integrationId) {
      try {
        const form = document.getElementById('integration-settings-form');
        if (!form) return;

        // Form verilerini topla
        const formData = new FormData(form);
        const settings = {};

        for (let [key, value] of formData.entries()) {
          const input = form.querySelector(`[name="${key}"]`);
          if (input && input.type === 'checkbox') {
            settings[key] = input.checked;
          } else {
            settings[key] = value;
          }
        }

        showNotification('Bağlantı test ediliyor...', 'info');

        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
        const response = await axios.post('/Integration/TestConnection', {
          integrationId: parseInt(integrationId),
          settings: settings
        }, {
          headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
          }
        });

        if (response.data.success) {
          connectionTestPassed = true;
          showNotification('Bağlantı testi başarılı! Artık ayarları kaydedebilirsiniz.', 'success');

          // Update save button to indicate test passed
          const saveButton = document.querySelector('button[onclick*="saveIntegrationSettings"]');
          if (saveButton) {
            saveButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
            saveButton.classList.add('bg-primary', 'hover:bg-primary-dark');
            saveButton.disabled = false;
            saveButton.innerHTML = '✓ Kaydet (Önce Bağlantıyı Test Edin) (Test Başarılı)';
          }
        } else {
          connectionTestPassed = false;
          showNotification(response.data.message || 'Bağlantı testi başarısız. Lütfen ayarları kontrol edin.', 'error');

          // Update save button to indicate test failed
          const saveButton = document.querySelector('button[onclick*="saveIntegrationSettings"]');
          if (saveButton) {
            saveButton.classList.remove('bg-primary', 'hover:bg-primary-dark');
            saveButton.classList.add('bg-gray-400', 'cursor-not-allowed');
            saveButton.disabled = true;
            saveButton.innerHTML = 'Kaydet (Önce Bağlantıyı Test Edin) (Test Başarısız)';
          }
        }
      } catch (error) {
        console.error('Error testing connection:', error);
        showNotification('Bağlantı test edilirken hata oluştu.', 'error');
      }
    }

    // Notification function
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

      const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
      };

      notification.className += ` ${colors[type] || colors.info}`;
      notification.innerHTML = `
        <div class="flex items-center">
          <span class="flex-1">${message}</span>
          <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      `;

      document.body.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (notification.parentElement) {
          notification.classList.add('translate-x-full');
          setTimeout(() => {
            if (notification.parentElement) {
              notification.remove();
            }
          }, 300);
        }
      }, 5000);
    }

    // Ticimax tab functionality
    function initializeTicimaxTabs(integrationId) {
      // Tab switching
      const tabButtons = document.querySelectorAll('.ticimax-tab-button');
      const tabContents = document.querySelectorAll('.ticimax-tab-content');

      tabButtons.forEach(button => {
        button.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');

          // Update button states
          tabButtons.forEach(btn => {
            btn.classList.remove('active', 'border-primary', 'text-primary');
            btn.classList.add('border-transparent', 'text-gray-500');
          });

          this.classList.add('active', 'border-primary', 'text-primary');
          this.classList.remove('border-transparent', 'text-gray-500');

          // Update content visibility
          tabContents.forEach(content => {
            content.classList.add('hidden');
          });

          document.getElementById(targetTab + '-tab' + (targetTab === 'connection' ? '' : '-content')).classList.remove('hidden');

          // Load status mappings if status-mapping tab is selected
          if (targetTab === 'status-mapping') {
            loadStatusMappings(integrationId);
          }
        });
      });

      // Check if user has Order Status Notifications module
      checkOrderStatusModule(integrationId);
    }

    // Check if user has Order Status Notifications module
    async function checkOrderStatusModule(integrationId) {
      try {
        const response = await fetch('/Integration/CheckOrderStatusModule');
        const result = await response.json();

        if (result.success && result.hasModule) {
          // Show status mapping tab
          document.getElementById('status-mapping-tab').style.display = 'block';
        } else {
          // Hide status mapping tab
          document.getElementById('status-mapping-tab').style.display = 'none';
        }
      } catch (error) {
        console.error('Error checking order status module:', error);
        // Hide tab on error
        document.getElementById('status-mapping-tab').style.display = 'none';
      }
    }

    // Load status mappings
    async function loadStatusMappings(integrationId) {
      try {
        const container = document.getElementById('status-mapping-container');
        container.innerHTML = '<div class="text-center py-4">Yükleniyor...</div>';

        const response = await fetch(`/Integration/GetStatusMappings?integrationId=${integrationId}`);
        const result = await response.json();

        if (result.success) {
          renderStatusMappings(result.data, integrationId);
        } else {
          container.innerHTML = `<div class="text-center py-4 text-red-600">${result.message || 'Durum eşleştirmeleri yüklenirken hata oluştu.'}</div>`;
        }
      } catch (error) {
        console.error('Error loading status mappings:', error);
        document.getElementById('status-mapping-container').innerHTML = '<div class="text-center py-4 text-red-600">Durum eşleştirmeleri yüklenirken hata oluştu.</div>';
      }
    }

    // Render status mappings
    function renderStatusMappings(mappings, integrationId) {
      const container = document.getElementById('status-mapping-container');

      console.log('Received mappings:', mappings);

      const ticimaxStatuses = [
        { code: '0', name: 'Yeni Sipariş', description: 'Henüz işleme alınmamış yeni sipariş' },
        { code: '1', name: 'Beklemede', description: 'Sipariş onay bekliyor' },
        { code: '2', name: 'Onaylandı', description: 'Sipariş onaylandı ve işleme alındı' },
        { code: '3', name: 'Hazırlanıyor', description: 'Sipariş hazırlanıyor' },
        { code: '4', name: 'Kargoya Verildi', description: 'Sipariş kargoya verildi' },
        { code: '5', name: 'Teslim Edildi', description: 'Sipariş müşteriye teslim edildi' },
        { code: '6', name: 'İptal Edildi', description: 'Sipariş iptal edildi' },
        { code: '7', name: 'İade Edildi', description: 'Sipariş iade edildi' },
        { code: '8', name: 'Kısmi Teslim', description: 'Siparişin bir kısmı teslim edildi' },
        { code: '9', name: 'Ödeme Bekliyor', description: 'Ödeme onayı bekleniyor' },
        { code: '10', name: 'Stok Bekliyor', description: 'Ürün stok bekliyor' },
        { code: '11', name: 'Tedarikçi Onayı Bekliyor', description: 'Tedarikçi onayı bekleniyor' },
        { code: '12', name: 'Kargo Hazırlığı', description: 'Kargo için hazırlanıyor' },
        { code: '13', name: 'Kargoda', description: 'Kargo yolda' },
        { code: '14', name: 'Dağıtımda', description: 'Yerel dağıtımda' },
        { code: '15', name: 'Teslim Edilemedi', description: 'Teslim edilemedi, tekrar denenecek' },
        { code: '16', name: 'Müşteri İptali', description: 'Müşteri tarafından iptal edildi' },
        { code: '17', name: 'Sistem İptali', description: 'Sistem tarafından iptal edildi' },
        { code: '18', name: 'Ödeme İptali', description: 'Ödeme problemi nedeniyle iptal' },
        { code: '19', name: 'Stok İptali', description: 'Stok yetersizliği nedeniyle iptal' },
        { code: '20', name: 'Kısmi İptal', description: 'Siparişin bir kısmı iptal edildi' },
        { code: '21', name: 'İade Talebi', description: 'İade talebi oluşturuldu' },
        { code: '22', name: 'İade Onaylandı', description: 'İade talebi onaylandı' },
        { code: '23', name: 'İade Reddedildi', description: 'İade talebi reddedildi' },
        { code: '24', name: 'İade Kargoda', description: 'İade ürün kargoda' },
        { code: '25', name: 'İade Teslim Alındı', description: 'İade ürün teslim alındı' },
        { code: '26', name: 'İade Tamamlandı', description: 'İade işlemi tamamlandı' },
        { code: '27', name: 'Değişim Talebi', description: 'Ürün değişim talebi' },
        { code: '28', name: 'Değişim Onaylandı', description: 'Değişim talebi onaylandı' },
        { code: '29', name: 'Değişim Kargoda', description: 'Değişim ürünü kargoda' },
        { code: '30', name: 'Değişim Tamamlandı', description: 'Değişim işlemi tamamlandı' }
      ];

      const systemStatuses = [
        { value: '1', text: 'Beklemede' },
        { value: '2', text: 'Onaylandı' },
        { value: '3', text: 'Hazırlanıyor' },
        { value: '4', text: 'Kargoya Verildi' },
        { value: '5', text: 'Teslim Edildi' },
        { value: '6', text: 'İptal Edildi' },
        { value: '7', text: 'İade Edildi' }
      ];

      let html = `
        <div class="space-y-4">
          <div class="bg-white rounded-lg border border-gray-200">
            <div class="p-4 border-b border-gray-100">
              <h5 class="font-medium text-gray-900">Durum Eşleştirmeleri</h5>
              <p class="text-sm text-gray-600 mt-1">Sol taraftaki sistem durumlarınız için sağ taraftaki Ticimax durumlarını seçin</p>
            </div>
            <div class="p-4">
      `;

      systemStatuses.forEach(systemStatus => {
        // Bu sistem durumu için hangi Ticimax durumu seçilmiş
        const selectedTicimaxStatus = Object.keys(mappings).find(key => mappings[key] === systemStatus.value) || '';

        html += `
          <div class="flex items-center space-x-4 py-3 border-b border-gray-100 last:border-b-0">
            <div class="flex-1">
              <div class="font-medium text-gray-900">${systemStatus.text}</div>
              <div class="text-sm text-gray-500">Sistem Kodu: ${systemStatus.value}</div>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </div>
            <div class="flex-1">
              <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      data-system-status="${systemStatus.value}"
                      onchange="updateStatusMapping(this, ${integrationId})">
                <option value="">Ticimax Durumu Seçin</option>
                ${ticimaxStatuses.map(ticimaxStatus =>
                  `<option value="${ticimaxStatus.code}" ${selectedTicimaxStatus === ticimaxStatus.code ? 'selected' : ''}>${ticimaxStatus.code} - ${ticimaxStatus.name}</option>`
                ).join('')}
              </select>
            </div>
          </div>
        `;
      });

      html += `
            </div>
          </div>
          <div class="flex justify-end">
            <button type="button" onclick="saveStatusMappings(${integrationId})" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors">
              Eşleştirmeleri Kaydet
            </button>
          </div>
        </div>
      `;

      container.innerHTML = html;
    }

    // Update status mapping
    function updateStatusMapping(selectElement, integrationId) {
      // This function is called when a mapping is changed
      // We will save all mappings when the save button is clicked
    }

    // Save status mappings
    async function saveStatusMappings(integrationId) {
      try {
        // Tüm eşleştirmeleri sıfırla ve yeniden oluştur
        const mappings = {};
        const selects = document.querySelectorAll('select[data-system-status]');

        console.log('Found selects:', selects.length);

        // Her sistem durumu için eşleştirmeyi kontrol et
        selects.forEach(select => {
          const systemStatus = select.getAttribute('data-system-status');
          const ticimaxStatus = select.value;
          console.log(`System: ${systemStatus}, Ticimax: ${ticimaxStatus}`);

          // Sadece seçilmiş (boş olmayan) değerleri kaydet
          if (ticimaxStatus && ticimaxStatus !== '') {
            mappings[ticimaxStatus] = systemStatus;
          }
        });

        console.log('Saving mappings:', mappings);

        const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

        const response = await fetch('/Integration/SaveStatusMappings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
          },
          body: JSON.stringify({
            integrationId: integrationId,
            statusMappings: mappings
          })
        });

        const result = await response.json();

        if (result.success) {
          showNotification(result.message || 'Durum eşleştirmeleri başarıyla kaydedildi.', 'success');
        } else {
          showNotification(result.message || 'Durum eşleştirmeleri kaydedilirken hata oluştu.', 'error');
        }
      } catch (error) {
        console.error('Error saving status mappings:', error);
        showNotification('Durum eşleştirmeleri kaydedilirken hata oluştu.', 'error');
      }
    }
  </script>
}

<main class="p-4 bg-gray-50">
    @Html.AntiForgeryToken()

    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800">Entegrasyonlar</h2>
      <p class="text-gray-600">E-ticaret altyapıları ve iletişim kanalları entegrasyonlarını yönetin</p>
    </div>

    <!-- Entegrasyonlar Listesi -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <h3 class="text-lg font-semibold">Mevcut Entegrasyonlar</h3>
      </div>

      @foreach (var category in Model.GroupedIntegrations)
      {
        <!-- @category.Key -->
        <div class="mb-8">
          <h4 class="text-md font-medium text-gray-700 mb-4">@category.Key</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

            @foreach (var integration in category.Value)
            {
              var companyIntegration = Model.CompanyIntegrations.ContainsKey(integration.Id) ? Model.CompanyIntegrations[integration.Id] : null;
              var isConfigured = companyIntegration?.IsConfigured ?? false;
              var isActive = companyIntegration?.IsActive ?? false;
              var hasIntegration = companyIntegration != null;

              <!-- @integration.Name -->
              <div class="border rounded-lg p-4 bg-white">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center">
                    <div class="w-12 h-12 rounded-lg @integration.BackgroundColor flex items-center justify-center mr-3">
                      @if (!string.IsNullOrEmpty(integration.IconClass) && integration.IconClass.Length == 1)
                      {
                        <span class="@integration.IconColor font-bold">@integration.IconClass</span>
                      }
                      else
                      {
                        <svg class="w-6 h-6 @integration.IconColor" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                      }
                    </div>
                    <div>
                      <h5 class="font-medium">@integration.Name</h5>
                      @if (hasIntegration)
                      {
                        @if (isConfigured && isActive)
                        {
                          <span class="text-sm text-green-600 flex items-center">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <path d="m9 11 3 3L22 4"></path>
                            </svg>
                            Aktif
                          </span>
                        }
                        else
                        {
                          <span class="text-sm text-orange-600 flex items-center">
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"></path>
                            </svg>
                            Ayarlar Eksik
                          </span>
                        }
                      }
                      else
                      {
                        <span class="text-sm text-gray-600 flex items-center">
                          <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 5v14"></path>
                            <path d="M5 12h14"></path>
                          </svg>
                          Kurulmadı
                        </span>
                      }
                    </div>
                  </div>
                </div>

                @if (hasIntegration)
                {
                  @if (isConfigured && isActive)
                  {
                    <!-- Fully configured integration with stats -->
                    <div class="text-sm text-gray-600 mb-4">
                      @if (companyIntegration.LastSyncAt.HasValue)
                      {
                        <p>Son senkronizasyon: <span class="font-medium">@GetTimeAgo(companyIntegration.LastSyncAt.Value)</span></p>
                      }
                      @if (!string.IsNullOrEmpty(companyIntegration.SyncStatsJson))
                      {
                        var stats = companyIntegration.SyncStats;
                        @if (stats.ContainsKey("syncedProducts"))
                        {
                          <p>Senkronize ürün: <span class="font-medium">@stats["syncedProducts"]</span></p>
                        }
                        @if (stats.ContainsKey("syncedOrders"))
                        {
                          <p>Senkronize sipariş: <span class="font-medium">@stats["syncedOrders"]</span></p>
                        }
                        @if (stats.ContainsKey("sentMessages"))
                        {
                          <p>Gönderilen mesaj: <span class="font-medium">@stats["sentMessages"]</span></p>
                        }
                        @if (stats.ContainsKey("responseRate"))
                        {
                          <p>Yanıt oranı: <span class="font-medium">@stats["responseRate"]</span></p>
                        }
                      }
                    </div>
                    <div class="flex justify-between">
                      <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 settings-btn"
                              data-integration-name="@integration.Name"
                              data-integration-id="@integration.Id">
                        Ayarlar
                      </button>
                      @if (integration.Type == "ecommerce")
                      {
                        <button class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-primary-dark">
                          Senkronize Et
                        </button>
                      }
                      else
                      {
                        <button class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-primary-dark">
                          Test Gönder
                        </button>
                      }
                    </div>
                  }
                  else
                  {
                    <!-- Integration added but not configured yet -->
                    <div class="text-sm text-gray-600 mb-4">
                      <p>@integration.Description</p>
                      <p class="text-orange-600 font-medium mt-2">⚠️ Ayarları tamamlanmamış</p>
                    </div>
                    <div class="flex justify-center">
                      <button class="px-3 py-1 text-sm bg-orange-500 text-white rounded-md hover:bg-orange-600 settings-btn"
                              data-integration-name="@integration.Name"
                              data-integration-id="@integration.Id">
                        Ayarları Tamamla
                      </button>
                    </div>
                  }
                }
                else
                {
                  <!-- Not added integration -->
                  <div class="text-sm text-gray-600 mb-4">
                    <p>@integration.Description</p>
                  </div>
                  <div class="flex justify-center">
                    <button onclick="addIntegration('@integration.Id', '@integration.Name')"
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark text-sm font-medium">
                      Entegre Et
                    </button>
                  </div>
                }
              </div>
            }
          </div>
        </div>
      }
    </div>
</main>

<!-- Drawer -->
<div class="drawer">
  <div class="drawer-header">
    <h3 id="drawer-title">Entegrasyon Ayarları</h3>
    <button id="close-drawer" class="close-button">
      <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 6L6 18"></path>
        <path d="M6 6l12 12"></path>
      </svg>
    </button>
  </div>
  <div class="drawer-content" id="drawer-content">
    <!-- Content will be loaded dynamically -->
  </div>
</div>

<!-- Drawer Overlay -->
<div class="drawer-overlay"></div>

<style>
  .drawer {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
  }

  .drawer.open {
    right: 0;
  }

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .drawer-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
  }

  .close-button:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .drawer-content {
    padding: 1.5rem;
  }

  .drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 999;
  }

  .drawer-overlay.active {
    opacity: 1;
    visibility: visible;
  }
</style>

@functions {
    private string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;

        if (timeSpan.TotalMinutes < 1)
            return "Az önce";
        else if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} dakika önce";
        else if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} saat önce";
        else
            return $"{(int)timeSpan.TotalDays} gün önce";
    }
}
