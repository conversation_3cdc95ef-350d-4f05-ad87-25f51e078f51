@model PushDashboard.ViewModels.CustomerIndexViewModel
@{
    ViewData["Title"] = "<PERSON><PERSON>şteriler";
}

<!-- Main Content Area -->
  <main class="p-4 bg-gray-50">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800"><PERSON><PERSON>şteriler</h2>
      <p class="text-gray-600">Tüm müşterilerinizi yönetin ve analiz edin</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-light text-blue">
            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-gray-500 text-sm">Toplam Müşteri</h3>
            <p class="text-2xl font-semibold" id="totalCustomers">-</p>
          </div>
        </div>
        <div class="mt-4 text-sm text-gray-500">
          <span class="font-medium">Son Senkronizasyon:</span> @Model.Stats.FormattedLastSync
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-light text-green">
            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path>
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-gray-500 text-sm">Aktif Müşteri</h3>
            <p class="text-2xl font-semibold" id="activeCustomers">-</p>
          </div>
        </div>
        <div class="mt-4 text-sm text-green">
          <span class="font-medium">%@Model.Stats.ActiveCustomerPercentage.ToString("F1")</span> aktiflik oranı
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-light text-purple">
            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-gray-500 text-sm">Yeni Müşteri</h3>
            <p class="text-2xl font-semibold" id="newCustomers">-</p>
          </div>
        </div>
        <div class="mt-4 text-sm text-gray-500">
          <span class="font-medium">Bu ay</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-light text-red">
            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="ml-4">
            <h3 class="text-gray-500 text-sm">Pasif Müşteri</h3>
            <p class="text-2xl font-semibold" id="inactiveCustomers">-</p>
          </div>
        </div>
        <div class="mt-4 text-sm text-red-500">
          <span class="font-medium">Aktif olmayan</span>
        </div>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div class="relative flex-1">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
          </div>
          <input type="text" id="searchInput" class="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="İsim, email, telefon ile ara...">
        </div>
        <div class="flex flex-wrap gap-2">
          <select id="statusFilter" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">Tüm Durumlar</option>
            <option value="active">Aktif</option>
            <option value="inactive">Pasif</option>
            <option value="new">Yeni</option>
          </select>
          <select id="sortSelect" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">Sıralama</option>
            <option value="name-asc">İsim (A-Z)</option>
            <option value="name-desc">İsim (Z-A)</option>
            <option value="membershipdate-asc">Üyelik Tarihi (Eskiden Yeniye)</option>
            <option value="membershipdate-desc">Üyelik Tarihi (Yeniden Eskiye)</option>
            <option value="lastlogindate-asc">Son Giriş (Eskiden Yeniye)</option>
            <option value="lastlogindate-desc">Son Giriş (Yeniden Eskiye)</option>
          </select>
          <button class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 6h18"></path>
              <path d="M7 12h10"></path>
              <path d="M10 18h4"></path>
            </svg>
          </button>
          <button id="addCustomerBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <line x1="19" y1="8" x2="19" y2="14"></line>
              <line x1="22" y1="11" x2="16" y2="11"></line>
            </svg>
            <span>Yeni Müşteri</span>
          </button>
          <button id="bulkImportBtn" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10,9 9,9 8,9"></polyline>
            </svg>
            <span>Excel İle Ekle</span>
          </button>
          <button id="syncCustomersBtn" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.61 1.98"></path>
              <path d="M17 3l4 4-4 4"></path>
            </svg>
            <span id="syncBtnText">Senkronize Et</span>
          </button>
          <button id="bulkMessagingBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center hidden">
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m3 3 3 9-3 9 19-9Z"></path>
              <path d="m6 12 13-13"></path>
            </svg>
            <span>Toplu Mesaj Gönder</span>
          </button>
        </div>
      </div>

      <!-- İşlem Takip Alanı -->
      <div id="importTrackingArea" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 hidden">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="m9 12 2 2 4-4"></path>
            </svg>
          </div>
          <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-blue-800">Excel İçe Aktarma İşlemi</h3>
            <div class="mt-2">
              <div class="flex items-center justify-between mb-2">
                <span id="importStatusText" class="text-sm text-blue-700">İşlem başlatıldı...</span>
                <button id="hideTrackingBtn" class="text-blue-600 hover:text-blue-800 text-sm">Gizle</button>
              </div>

              <!-- Progress Bar -->
              <div class="bg-blue-200 rounded-full h-2 mb-2">
                <div id="importProgressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
              </div>

              <!-- Stats -->
              <div id="importStatsArea" class="grid grid-cols-4 gap-4 text-xs text-blue-700">
                <div>
                  <span class="font-medium">Toplam:</span>
                  <span id="importTotalCount">-</span>
                </div>
                <div>
                  <span class="font-medium">İşlenen:</span>
                  <span id="importProcessedCount">-</span>
                </div>
                <div>
                  <span class="font-medium">Başarılı:</span>
                  <span id="importSuccessCount">-</span>
                </div>
                <div>
                  <span class="font-medium">Hata:</span>
                  <span id="importErrorCount">-</span>
                </div>
              </div>

              <!-- Action Buttons -->
              <div id="importActionButtons" class="mt-3 flex space-x-2 hidden">
                <button id="refreshCustomersBtn" class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                  Müşteri Listesini Yenile
                </button>
                <button id="viewImportDetailsBtn" class="px-3 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700">
                  Detayları Görüntüle
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customer Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                </div>
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Müşteri
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Durum
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Şehir
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Üyelik Tarihi
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Son Giriş
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200" id="customerTableBody">
            @foreach (var customer in Model.Customers)
            {
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-medium">
                        @(string.IsNullOrEmpty(customer.FullName) ? "?" : customer.FullName.Substring(0, 1).ToUpper())
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">@customer.FullName</div>
                      <div class="text-sm text-gray-500">@customer.Email</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full @customer.StatusBadgeClass">
                    @customer.StatusText
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  @(customer.City ?? "Belirtilmemiş")
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  @customer.FormattedMembershipDate
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  @customer.FormattedLastLoginDate
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex space-x-2">
                    <button class="text-primary hover:text-primary-dark" onclick="viewCustomer(@customer.Id)" title="Müşteri Detayları">
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    </button>
                    <button class="text-green-600 hover:text-green-800" onclick="viewCustomerBaskets(@customer.Id)" title="Müşteri Sepetleri">
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="8" cy="21" r="1"></circle>
                        <circle cx="19" cy="21" r="1"></circle>
                        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
      <div class="px-6 py-4 border-t flex justify-between items-center">
        <div class="text-sm text-gray-500">
          @Model.Pagination.StartRecord - @Model.Pagination.EndRecord arası gösteriliyor (Toplam @Model.Pagination.TotalRecords.ToString("N0") müşteri)
        </div>
        <div class="flex space-x-2">
          @if (Model.Pagination.HasPreviousPage)
          {
            <button onclick="loadPage(@(Model.Pagination.CurrentPage - 1))" class="px-3 py-1 border rounded text-sm hover:bg-gray-50">Önceki</button>
          }

          @for (int i = Math.Max(1, Model.Pagination.CurrentPage - 2); i <= Math.Min(Model.Pagination.TotalPages, Model.Pagination.CurrentPage + 2); i++)
          {
            if (i == Model.Pagination.CurrentPage)
            {
              <button class="px-3 py-1 bg-primary text-white rounded text-sm">@i</button>
            }
            else
            {
              <button onclick="loadPage(@i)" class="px-3 py-1 border rounded text-sm hover:bg-gray-50">@i</button>
            }
          }

          @if (Model.Pagination.HasNextPage)
          {
            <button onclick="loadPage(@(Model.Pagination.CurrentPage + 1))" class="px-3 py-1 border rounded text-sm hover:bg-gray-50">Sonraki</button>
          }
        </div>
      </div>
    </div>
  </main>

<!-- Sepet Modal -->
<div id="basketModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">Müşteri Sepetleri</h3>
        <button onclick="closeBasketModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4 max-h-96 overflow-y-auto" id="basketModalContent">
        <!-- Sepet listesi buraya yüklenecek -->
      </div>
    </div>
  </div>
</div>

<!-- Sepet Detay Modal -->
<div id="basketDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">Sepet Detayları</h3>
        <button onclick="closeBasketDetailModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4 max-h-96 overflow-y-auto" id="basketDetailModalContent">
        <!-- Sepet detayları buraya yüklenecek -->
      </div>
    </div>
  </div>
</div>

<!-- Customer Details Modal -->
<div id="customerDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <!-- Modal Header -->
    <div class="flex items-center justify-between pb-3 border-b">
      <h3 class="text-lg font-semibold text-gray-900">Müşteri Detayları</h3>
      <button onclick="closeCustomerModal()" class="text-gray-400 hover:text-gray-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Body -->
    <div class="mt-4">
      <div id="customerDetailsContent">
        <!-- Loading state -->
        <div id="modalLoading" class="flex items-center justify-center py-8">
          <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-gray-500">Yükleniyor...</span>
        </div>

        <!-- Customer details will be populated here -->
        <div id="customerDetailsData" class="hidden">
          <!-- Personal Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Kişisel Bilgiler
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Ad Soyad</label>
                <p id="customerFullName" class="text-sm text-gray-900 font-medium">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">E-posta</label>
                <p id="customerEmail" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Telefon</label>
                <p id="customerPhone" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Cep Telefonu</label>
                <p id="customerMobilePhone" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Doğum Tarihi</label>
                <p id="customerBirthDate" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Cinsiyet</label>
                <p id="customerGender" class="text-sm text-gray-900">-</p>
              </div>
            </div>
          </div>

          <!-- Address Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Adres Bilgileri
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Şehir</label>
                <p id="customerCity" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">İlçe</label>
                <p id="customerDistrict" class="text-sm text-gray-900">-</p>
              </div>
            </div>
          </div>

          <!-- Membership Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
              Üyelik Bilgileri
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Müşteri Kodu</label>
                <p id="customerCode" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Üyelik Tipi</label>
                <p id="customerMembershipType" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Üyelik Tarihi</label>
                <p id="customerMembershipDate" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Durum</label>
                <p id="customerStatus" class="text-sm">-</p>
              </div>
            </div>
          </div>

          <!-- Activity Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Aktivite Bilgileri
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Son Giriş Tarihi</label>
                <p id="customerLastLoginDate" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Son Giriş IP</label>
                <p id="customerLastLoginIp" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Son Güncelleme</label>
                <p id="customerLastUpdateDate" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Son Senkronizasyon</label>
                <p id="customerLastSyncDate" class="text-sm text-gray-900">-</p>
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Mali Bilgiler
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Puan Bakiyesi</label>
                <p id="customerPointBalance" class="text-sm text-gray-900 font-medium">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Kredi Limiti</label>
                <p id="customerCreditLimit" class="text-sm text-gray-900 font-medium">-</p>
              </div>
            </div>
          </div>

          <!-- Permissions and Agreements -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              İzinler ve Sözleşmeler
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">E-posta İzni</label>
                <p id="customerEmailPermission" class="text-sm">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">SMS İzni</label>
                <p id="customerSmsPermission" class="text-sm">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">KVKK Onayı</label>
                <p id="customerKvkkApproval" class="text-sm">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Üyelik Sözleşmesi</label>
                <p id="customerMembershipAgreement" class="text-sm">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Kapıda Ödeme</label>
                <p id="customerCashOnDelivery" class="text-sm">-</p>
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="mb-4">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2"></path>
              </svg>
              Mesleki Bilgiler
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Meslek</label>
                <p id="customerProfession" class="text-sm text-gray-900">-</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Öğrenim Durumu</label>
                <p id="customerEducationLevel" class="text-sm text-gray-900">-</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="flex items-center justify-end pt-3 border-t mt-4">
      <button onclick="closeCustomerModal()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 mr-2">
        Kapat
      </button>
    </div>
  </div>
</div>

<script>
let currentPage = 1;
let currentSearchTerm = '';
let currentStatus = '';
let currentSortBy = '';
let currentSortDirection = '';

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    // İlk veri yüklemesi
    loadCustomers();

    // Arama input'u
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            currentSearchTerm = e.target.value.trim();
            currentPage = 1;
            console.log('Search term:', currentSearchTerm); // Debug için
            loadCustomers();
        }, 500));
    }

    // Durum filtresi
    const statusSelect = document.getElementById('statusFilter');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            currentStatus = this.value;
            currentPage = 1;
            loadCustomers();
        });
    }

    // Sıralama
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const value = this.value;
            if (value) {
                const [sortBy, sortDirection] = value.split('-');
                currentSortBy = sortBy;
                currentSortDirection = sortDirection;
            } else {
                currentSortBy = '';
                currentSortDirection = '';
            }
            currentPage = 1;
            loadCustomers();
        });
    }

    // Senkronizasyon butonu
    const syncBtn = document.getElementById('syncCustomersBtn');
    if (syncBtn) {
        syncBtn.addEventListener('click', syncCustomers);
    }
});

// Debounce fonksiyonu
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Müşterileri yükle
function loadCustomers() {
    const params = new URLSearchParams({
        page: currentPage,
        pageSize: 50,
        searchTerm: currentSearchTerm || '',
        status: currentStatus || '',
        sortBy: currentSortBy || '',
        sortDirection: currentSortDirection || ''
    });

    console.log('Loading customers with params:', {
        page: currentPage,
        searchTerm: currentSearchTerm,
        status: currentStatus,
        sortBy: currentSortBy,
        sortDirection: currentSortDirection
    }); // Debug için

    // Loading göster
    showLoading(true);

    fetch(`/Customer/GetCustomers?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCustomerTable(data.data.customers);
                updatePagination(data.data.pagination);
                updateStats(data.data.stats);
            } else {
                showNotification('Hata: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Bir hata oluştu.', 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

// Sayfa değiştir
function loadPage(page) {
    currentPage = page;
    loadCustomers();
}

// Müşteri tablosunu güncelle
function updateCustomerTable(customers) {
    const tbody = document.getElementById('customerTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    customers.forEach(customer => {
        const row = createCustomerRow(customer);
        tbody.appendChild(row);
    });
}

// Müşteri satırı oluştur
function createCustomerRow(customer) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
            </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-medium">
                        ${customer.fullName && customer.fullName.length > 0 ? customer.fullName.charAt(0).toUpperCase() : '?'}
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">${customer.fullName}</div>
                    <div class="text-sm text-gray-500">${customer.email}</div>
                </div>
            </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${customer.statusBadgeClass}">
                ${customer.statusText}
            </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            ${customer.city || 'Belirtilmemiş'}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            ${customer.formattedMembershipDate}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            ${customer.formattedLastLoginDate}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <div class="flex space-x-2">
                <button onclick="viewCustomer(${customer.id})" class="text-primary hover:text-primary-dark" title="Müşteri Detayları">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                </button>
                <button onclick="viewCustomerBaskets(${customer.id})" class="text-green-600 hover:text-green-800" title="Müşteri Sepetleri">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="8" cy="21" r="1"></circle>
                        <circle cx="19" cy="21" r="1"></circle>
                        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                    </svg>
                </button>
            </div>
        </td>
    `;
    return row;
}

// Sayfalama güncelle
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.px-6.py-4.border-t.flex.justify-between.items-center');
    if (!paginationContainer) return;

    // Bilgi metni güncelle
    const infoText = paginationContainer.querySelector('.text-sm.text-gray-500');
    if (infoText) {
        infoText.textContent = `${pagination.startRecord} - ${pagination.endRecord} arası gösteriliyor (Toplam ${pagination.totalRecords.toLocaleString()} müşteri)`;
    }

    // Sayfa butonları güncelle
    const buttonContainer = paginationContainer.querySelector('.flex.space-x-2');
    if (buttonContainer) {
        buttonContainer.innerHTML = '';

        // Önceki butonu
        if (pagination.hasPreviousPage) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'px-3 py-1 border rounded text-sm hover:bg-gray-50';
            prevBtn.textContent = 'Önceki';
            prevBtn.onclick = () => loadPage(pagination.currentPage - 1);
            buttonContainer.appendChild(prevBtn);
        }

        // Sayfa numaraları
        const startPage = Math.max(1, pagination.currentPage - 2);
        const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            if (i === pagination.currentPage) {
                pageBtn.className = 'px-3 py-1 bg-primary text-white rounded text-sm';
            } else {
                pageBtn.className = 'px-3 py-1 border rounded text-sm hover:bg-gray-50';
                pageBtn.onclick = () => loadPage(i);
            }
            pageBtn.textContent = i;
            buttonContainer.appendChild(pageBtn);
        }

        // Sonraki butonu
        if (pagination.hasNextPage) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'px-3 py-1 border rounded text-sm hover:bg-gray-50';
            nextBtn.textContent = 'Sonraki';
            nextBtn.onclick = () => loadPage(pagination.currentPage + 1);
            buttonContainer.appendChild(nextBtn);
        }
    }
}

// İstatistikleri güncelle
function updateStats(stats) {
    const totalElement = document.getElementById('totalCustomers');
    const activeElement = document.getElementById('activeCustomers');
    const newElement = document.getElementById('newCustomers');
    const inactiveElement = document.getElementById('inactiveCustomers');

    if (totalElement) totalElement.textContent = stats.totalCustomers.toLocaleString();
    if (activeElement) activeElement.textContent = stats.activeCustomers.toLocaleString();
    if (newElement) newElement.textContent = stats.newCustomersThisMonth.toLocaleString();
    if (inactiveElement) inactiveElement.textContent = stats.inactiveCustomers.toLocaleString();
}

// Müşteri detaylarını görüntüle
function viewCustomer(customerId) {
    // Modal'ı aç ve loading göster
    openCustomerModal();

    fetch(`/Customer/GetCustomerDetails?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateCustomerModal(data.data);
            } else {
                showNotification('Hata: ' + data.message, 'error');
                closeCustomerModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Bir hata oluştu.', 'error');
            closeCustomerModal();
        });
}

// Modal'ı aç
function openCustomerModal() {
    const modal = document.getElementById('customerDetailsModal');
    const loading = document.getElementById('modalLoading');
    const data = document.getElementById('customerDetailsData');

    if (modal && loading && data) {
        modal.classList.remove('hidden');
        loading.classList.remove('hidden');
        data.classList.add('hidden');
        document.body.style.overflow = 'hidden'; // Scroll'u engelle
    }
}

// Modal'ı kapat
function closeCustomerModal() {
    const modal = document.getElementById('customerDetailsModal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto'; // Scroll'u geri aç
    }
}

// Modal dışına tıklanınca kapat
document.addEventListener('click', function(event) {
    const modal = document.getElementById('customerDetailsModal');
    if (event.target === modal) {
        closeCustomerModal();
    }
});

// ESC tuşu ile kapat
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeCustomerModal();
    }
});

// Müşteri verilerini modal'a doldur
function populateCustomerModal(customer) {
    const loading = document.getElementById('modalLoading');
    const data = document.getElementById('customerDetailsData');

    if (loading && data) {
        loading.classList.add('hidden');
        data.classList.remove('hidden');
    }

    // Kişisel Bilgiler
    setElementText('customerFullName', customer.fullName || '-');
    setElementText('customerEmail', customer.email || '-');
    setElementText('customerPhone', customer.phone || '-');
    setElementText('customerMobilePhone', customer.mobilePhone || '-');
    setElementText('customerBirthDate', formatDate(customer.birthDate) || '-');
    setElementText('customerGender', getGenderText(customer.genderId) || '-');

    // Adres Bilgileri
    setElementText('customerCity', customer.city || '-');
    setElementText('customerDistrict', customer.district || '-');

    // Üyelik Bilgileri
    setElementText('customerCode', customer.customerCode || '-');
    setElementText('customerMembershipType', customer.membershipType || '-');
    setElementText('customerMembershipDate', formatDate(customer.membershipDate) || '-');

    // Durum
    const statusElement = document.getElementById('customerStatus');
    if (statusElement) {
        statusElement.innerHTML = `<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${customer.statusBadgeClass}">${customer.statusText}</span>`;
    }

    // Aktivite Bilgileri
    setElementText('customerLastLoginDate', formatDate(customer.lastLoginDate) || '-');
    setElementText('customerLastLoginIp', customer.lastLoginIp || '-');
    setElementText('customerLastUpdateDate', formatDate(customer.lastUpdateDate) || '-');
    setElementText('customerLastSyncDate', formatDate(customer.lastSyncDate) || '-');

    // Mali Bilgiler
    setElementText('customerPointBalance', customer.pointBalance ? customer.pointBalance.toLocaleString() + ' Puan' : '0 Puan');
    setElementText('customerCreditLimit', customer.creditLimit ? customer.creditLimit.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) : '0,00 ₺');

    // İzinler ve Sözleşmeler
    setPermissionText('customerEmailPermission', customer.emailPermission);
    setPermissionText('customerSmsPermission', customer.smsPermission);
    setPermissionText('customerKvkkApproval', customer.kvkkApproval);
    setPermissionText('customerMembershipAgreement', customer.membershipAgreementApproval);
    setBlockedText('customerCashOnDelivery', customer.cashOnDeliveryBlocked);

    // Mesleki Bilgiler
    setElementText('customerProfession', customer.profession || '-');
    setElementText('customerEducationLevel', customer.educationLevel || '-');
}

// Yardımcı fonksiyonlar
function setElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

function setPermissionText(elementId, permission) {
    const element = document.getElementById(elementId);
    if (element) {
        if (permission) {
            element.innerHTML = '<span class="text-green-600 font-medium">✓ Var</span>';
        } else {
            element.innerHTML = '<span class="text-red-600 font-medium">✗ Yok</span>';
        }
    }
}

function setBlockedText(elementId, blocked) {
    const element = document.getElementById(elementId);
    if (element) {
        if (blocked) {
            element.innerHTML = '<span class="text-red-600 font-medium">✗ Yasaklı</span>';
        } else {
            element.innerHTML = '<span class="text-green-600 font-medium">✓ İzinli</span>';
        }
    }
}

function formatDate(dateString) {
    if (!dateString) return null;

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return null;

        return date.toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return null;
    }
}

function getGenderText(genderId) {
    switch (genderId) {
        case 1:
            return 'Erkek';
        case 2:
            return 'Kadın';
        default:
            return 'Belirtilmemiş';
    }
}

// Müşterileri senkronize et
function syncCustomers() {
    const syncBtn = document.getElementById('syncCustomersBtn');
    const syncBtnText = document.getElementById('syncBtnText');

    if (!syncBtn || !syncBtnText) return;

    // Butonu devre dışı bırak
    syncBtn.disabled = true;
    syncBtnText.textContent = 'Senkronize ediliyor...';
    syncBtn.classList.add('opacity-50', 'cursor-not-allowed');

    fetch('/Customer/SyncCustomers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Sayfayı yenile
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification('Hata: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Senkronizasyon sırasında bir hata oluştu.', 'error');
    })
    .finally(() => {
        // Butonu tekrar aktif et
        syncBtn.disabled = false;
        syncBtnText.textContent = 'Senkronize Et';
        syncBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    });
}

// Loading göster/gizle
function showLoading(show) {
    const tbody = document.getElementById('customerTableBody');
    if (!tbody) return;

    if (show) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center">
                    <div class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="text-gray-500">Yükleniyor...</span>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Bildirim göster
function showNotification(message, type = 'info') {
    // Toast notification oluştur
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;

    // Tip'e göre stil belirle
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-500';
            textColor = 'text-white';
            icon = '✅';
            break;
        case 'error':
            bgColor = 'bg-red-500';
            textColor = 'text-white';
            icon = '❌';
            break;
        case 'warning':
            bgColor = 'bg-yellow-500';
            textColor = 'text-white';
            icon = '⚠️';
            break;
        default:
            bgColor = 'bg-blue-500';
            textColor = 'text-white';
            icon = 'ℹ️';
    }

    toast.classList.add(bgColor, textColor);

    toast.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2">${icon}</span>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(toast);

    // Animasyon ile göster
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, 5000);
}

// Müşteri sepetlerini görüntüle
function viewCustomerBaskets(customerId) {
    openBasketModal();

    fetch(`/Basket/GetBasketsByCustomer?customerId=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBasketModal(data.data);
            } else {
                showNotification('Hata: ' + data.message, 'error');
                closeBasketModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Bir hata oluştu.', 'error');
            closeBasketModal();
        });
}

// Sepet modal'ını aç
function openBasketModal() {
    const modal = document.getElementById('basketModal');
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

// Sepet modal'ını kapat
function closeBasketModal() {
    const modal = document.getElementById('basketModal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Sepet detay modal'ını aç
function openBasketDetailModal() {
    const modal = document.getElementById('basketDetailModal');
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

// Sepet detay modal'ını kapat
function closeBasketDetailModal() {
    const modal = document.getElementById('basketDetailModal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
}

// Sepet modal'ını doldur
function populateBasketModal(baskets) {
    const content = document.getElementById('basketModalContent');

    if (!baskets || baskets.length === 0) {
        content.innerHTML = `
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="8" cy="21" r="1"></circle>
                    <circle cx="19" cy="21" r="1"></circle>
                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Sepet bulunamadı</h3>
                <p class="mt-1 text-sm text-gray-500">Bu müşteriye ait sepet bulunmuyor.</p>
            </div>
        `;
        return;
    }

    content.innerHTML = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sepet Tarihi</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ürün Sayısı</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam Tutar</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${baskets.map(basket => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${basket.formattedBasketDate}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${basket.productCount}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${basket.formattedTotalAmount}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${basket.membershipStatusBadgeClass}">
                                    ${basket.membershipStatusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <button onclick="viewBasketDetail('${basket.guidBasketId}')" class="text-primary hover:text-primary-dark">
                                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// Sepet detayını görüntüle
function viewBasketDetail(guidBasketId) {
    closeBasketModal();
    openBasketDetailModal();

    // Önce sepet bilgilerini yükle
    fetch(`/Basket/GetBasketDetails?guidBasketId=${encodeURIComponent(guidBasketId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBasketDetailModal(data.data);
                // Sonra ürünleri sayfalama ile yükle
                loadBasketItemsInModal(guidBasketId, 1);
            } else {
                showNotification('Hata: ' + data.message, 'error');
                closeBasketDetailModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Bir hata oluştu.', 'error');
            closeBasketDetailModal();
        });
}

// Sepet detay modal'ını doldur
function populateBasketDetailModal(basket) {
    const content = document.getElementById('basketDetailModalContent');

    content.innerHTML = `
        <div class="space-y-6">
            <!-- Sepet Bilgileri -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-medium text-gray-900 mb-3">Sepet Bilgileri</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Müşteri</label>
                        <div class="mt-1 flex items-center space-x-2">
                            <p class="text-sm text-gray-900">${basket.displayCustomerName || basket.customerName}</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${basket.membershipStatusBadgeClass || 'bg-gray-100 text-gray-800'}">
                                ${basket.membershipStatusText || (basket.customerName ? 'Üyelikli' : 'Üyeliksiz')}
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">E-posta</label>
                        <p class="mt-1 text-sm text-gray-900">${basket.displayCustomerEmail || basket.customerEmail}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sepet Tarihi</label>
                        <p class="mt-1 text-sm text-gray-900">${basket.formattedBasketDate}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Toplam Tutar</label>
                        <p class="mt-1 text-sm text-gray-900 font-semibold">${basket.formattedTotalAmount}</p>
                    </div>
                </div>
            </div>

            <!-- Sepet Ürünleri -->
            <div>
                <h4 class="text-lg font-medium text-gray-900 mb-3">Sepet Ürünleri</h4>
                <div id="basketItemsContainer">
                    <!-- Ürünler buraya yüklenecek -->
                </div>
            </div>
        </div>
    `;
}

// Sepet ürünlerini modal'da yükle
function loadBasketItemsInModal(guidBasketId, page = 1) {
    const itemsContainer = document.getElementById('basketItemsContainer');
    if (!itemsContainer) return;

    // Loading göster
    itemsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p class="mt-2 text-sm text-gray-500">Ürünler yükleniyor...</p>
        </div>
    `;

    fetch(`/Basket/GetBasketItemsPaged?guidBasketId=${encodeURIComponent(guidBasketId)}&page=${page}&pageSize=10`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBasketItemsInModal(data.data, guidBasketId);
            } else {
                itemsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-sm text-red-600">Ürünler yüklenirken hata oluştu: ${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading basket items:', error);
            itemsContainer.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-sm text-red-600">Ürünler yüklenirken bir hata oluştu.</p>
                </div>
            `;
        });
}

// Sepet ürünlerini modal'da populate et
function populateBasketItemsInModal(pagedData, guidBasketId) {
    const container = document.getElementById('basketItemsContainer');
    if (!container) return;

    if (pagedData.items && pagedData.items.length > 0) {
        container.innerHTML = `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ürün</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Adet</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim Fiyat</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${pagedData.items.map(item => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            ${item.productImage ?
                                                `<img class="h-10 w-10 rounded object-cover" src="${item.productImage}" alt="${item.productName}">` :
                                                `<div class="h-10 w-10 rounded bg-gray-200 flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                                        <circle cx="9" cy="9" r="2"></circle>
                                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                                                    </svg>
                                                </div>`
                                            }
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">${item.productName}</div>
                                            <div class="text-sm text-gray-500">${item.productCode}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${item.formattedQuantity}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${item.formattedUnitPrice}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                                    ${item.formattedTotalPrice}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <!-- Sayfalama -->
            ${pagedData.totalPages > 1 ? `
                <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                    <div class="flex flex-1 justify-between sm:hidden">
                        <button onclick="loadBasketItemsInModal('${guidBasketId}', ${pagedData.currentPage - 1})"
                                ${!pagedData.hasPreviousPage ? 'disabled' : ''}
                                class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            Önceki
                        </button>
                        <button onclick="loadBasketItemsInModal('${guidBasketId}', ${pagedData.currentPage + 1})"
                                ${!pagedData.hasNextPage ? 'disabled' : ''}
                                class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            Sonraki
                        </button>
                    </div>
                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Toplam <span class="font-medium">${pagedData.totalItems}</span> üründen
                                <span class="font-medium">${((pagedData.currentPage - 1) * pagedData.pageSize) + 1}</span> -
                                <span class="font-medium">${Math.min(pagedData.currentPage * pagedData.pageSize, pagedData.totalItems)}</span> arası gösteriliyor
                            </p>
                        </div>
                        <div>
                            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                <button onclick="loadBasketItemsInModal('${guidBasketId}', ${pagedData.currentPage - 1})"
                                        ${!pagedData.hasPreviousPage ? 'disabled' : ''}
                                        class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">Önceki</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                ${Array.from({length: pagedData.totalPages}, (_, i) => i + 1).map(pageNum => `
                                    <button onclick="loadBasketItemsInModal('${guidBasketId}', ${pageNum})"
                                            class="relative inline-flex items-center px-4 py-2 text-sm font-semibold ${pageNum === pagedData.currentPage ? 'bg-primary text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary' : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'}">
                                        ${pageNum}
                                    </button>
                                `).join('')}

                                <button onclick="loadBasketItemsInModal('${guidBasketId}', ${pagedData.currentPage + 1})"
                                        ${!pagedData.hasNextPage ? 'disabled' : ''}
                                        class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">Sonraki</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            ` : ''}
        `;
    } else {
        container.innerHTML = `
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="8" cy="21" r="1"></circle>
                    <circle cx="19" cy="21" r="1"></circle>
                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Sepet boş</h3>
                <p class="mt-1 text-sm text-gray-500">Bu sepette henüz ürün bulunmuyor.</p>
            </div>
        `;
    }
}
</script>

<!-- Toplu Mesaj Gönderme Modal -->
<div id="bulkMessagingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">Toplu Mesaj Gönderimi</h3>
        <button onclick="closeBulkMessagingModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Tab Navigation -->
      <div class="flex border-b border-gray-200 mt-4">
        <button id="newMessageTab" onclick="switchBulkMessageTab('new')" class="px-4 py-2 text-sm font-medium text-primary border-b-2 border-primary">
          Yeni Gönderim
        </button>
        <button id="jobHistoryTab" onclick="switchBulkMessageTab('history')" class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Gönderim Geçmişi
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4" id="bulkMessagingModalContent">
        <!-- New Message Tab Content -->
        <div id="newMessageContent">
          <!-- Loading state -->
          <div id="bulkMessagingLoading" class="flex items-center justify-center py-8">
            <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-500">Yükleniyor...</span>
          </div>

        <!-- Bulk Messaging Form -->
        <div id="bulkMessagingForm" class="hidden">
          <!-- Step 1: Basic Information -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">1. Temel Bilgiler</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Başlık *</label>
                <input type="text" id="bulkMessageTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Toplu mesaj başlığı">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                <input type="text" id="bulkMessageDescription" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Kısa açıklama (opsiyonel)">
              </div>
            </div>
          </div>

          <!-- Step 2: Customer Filters -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">2. Müşteri Filtreleri</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                <select id="customerStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option value="">Tüm Müşteriler</option>
                  <option value="true">Sadece Aktif</option>
                  <option value="false">Sadece Pasif</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email İzni</label>
                <select id="emailPermissionFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option value="">Tümü</option>
                  <option value="true">Email İzni Var</option>
                  <option value="false">Email İzni Yok</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">SMS İzni</label>
                <select id="smsPermissionFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option value="">Tümü</option>
                  <option value="true">SMS İzni Var</option>
                  <option value="false">SMS İzni Yok</option>
                </select>
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Arama</label>
              <input type="text" id="customerSearchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="İsim, email, telefon ile ara...">
            </div>
            <div class="mt-4 flex items-center justify-between">
              <button type="button" onclick="estimateRecipients()" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                Hedef Müşteri Sayısını Hesapla
              </button>
              <div id="recipientCount" class="text-sm text-gray-600"></div>
            </div>
          </div>

          <!-- Step 3: Channel Settings -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">3. İletişim Kanalları ve Şablonlar</h4>
            <div id="channelSettings">
              <!-- Kanallar buraya dinamik olarak yüklenecek -->
            </div>
          </div>

          <!-- Step 4: Preview -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3">4. Önizleme</h4>
            <div class="flex items-center justify-between mb-4">
              <button type="button" onclick="createPreview()" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                Önizleme Oluştur
              </button>
              <div id="previewCost" class="text-sm text-gray-600"></div>
            </div>
            <div id="previewContent" class="hidden">
              <!-- Önizleme içeriği buraya gelecek -->
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button type="button" onclick="closeBulkMessagingModal()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              İptal
            </button>
            <button type="button" id="sendBulkMessageBtn" onclick="sendBulkMessage()" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark" disabled>
              Gönderimi Başlat
            </button>
          </div>
        </div>

        <!-- Error State -->
        <div id="bulkMessagingError" class="hidden text-center py-8">
          <svg class="mx-auto h-12 w-12 text-red-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Hata Oluştu</h3>
          <p id="bulkMessagingErrorMessage" class="mt-1 text-sm text-gray-500"></p>
          <button onclick="initializeBulkMessaging()" class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
            Tekrar Dene
          </button>
        </div>
        </div>

        <!-- Job History Tab Content -->
        <div id="jobHistoryContent" class="hidden">
          <!-- Job History Header -->
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-md font-semibold text-gray-800">Toplu Mesaj Gönderim Geçmişi</h4>
            <button onclick="refreshJobHistory()" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Yenile
            </button>
          </div>

          <!-- Job History Loading -->
          <div id="jobHistoryLoading" class="flex items-center justify-center py-8">
            <svg class="animate-spin -ml-1 mr-3 h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-gray-500">Gönderim geçmişi yükleniyor...</span>
          </div>

          <!-- Job History List -->
          <div id="jobHistoryList" class="hidden space-y-4 max-h-96 overflow-y-auto">
            <!-- Job items will be populated here -->
          </div>

          <!-- No Jobs State -->
          <div id="noJobsState" class="hidden text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz Gönderim Yok</h3>
            <p class="mt-1 text-sm text-gray-500">Henüz hiç toplu mesaj gönderimi yapılmamış.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Toplu mesaj gönderimi için global değişkenler
let availableChannels = [];
let enabledChannels = [];
let currentPreview = null;
let currentTab = 'new';
let jobHistoryData = [];

// Sayfa yüklendiğinde toplu mesaj butonunu kontrol et
document.addEventListener('DOMContentLoaded', function() {
    checkBulkMessagingAccess();
});

// Toplu mesaj erişimini kontrol et
async function checkBulkMessagingAccess() {
    try {
        const response = await fetch('/BulkMessaging/CheckModuleAccess');
        const result = await response.json();

        if (result.success) {
            enabledChannels = result.enabledChannels || [];
            document.getElementById('bulkMessagingBtn').classList.remove('hidden');
        } else {
            console.log('Bulk messaging not available:', result.message);
        }
    } catch (error) {
        console.error('Error checking bulk messaging access:', error);
    }
}

// Toplu mesaj modal'ını aç
function openBulkMessagingModal() {
    document.getElementById('bulkMessagingModal').classList.remove('hidden');
    switchBulkMessageTab('new');
    initializeBulkMessaging();
}

// Toplu mesaj modal'ını kapat
function closeBulkMessagingModal() {
    document.getElementById('bulkMessagingModal').classList.add('hidden');
    resetBulkMessagingForm();
}

// Toplu mesaj formunu sıfırla
function resetBulkMessagingForm() {
    document.getElementById('bulkMessageTitle').value = '';
    document.getElementById('bulkMessageDescription').value = '';
    document.getElementById('customerStatusFilter').value = '';
    document.getElementById('emailPermissionFilter').value = '';
    document.getElementById('smsPermissionFilter').value = '';
    document.getElementById('customerSearchFilter').value = '';
    document.getElementById('recipientCount').textContent = '';
    document.getElementById('previewContent').classList.add('hidden');
    document.getElementById('previewCost').textContent = '';
    document.getElementById('sendBulkMessageBtn').disabled = true;
    currentPreview = null;
}

// Toplu mesaj sistemini başlat
async function initializeBulkMessaging() {
    showBulkMessagingLoading();

    try {
        // Mevcut kanalları yükle
        const channelsResponse = await fetch('/BulkMessaging/GetAvailableChannels');
        const channelsResult = await channelsResponse.json();

        if (!channelsResult.success) {
            throw new Error(channelsResult.message);
        }

        availableChannels = channelsResult.data || [];

        // Kanal ayarlarını oluştur
        createChannelSettings();

        // Formu göster
        showBulkMessagingForm();

    } catch (error) {
        showBulkMessagingError(error.message);
    }
}

// Loading durumunu göster
function showBulkMessagingLoading() {
    document.getElementById('bulkMessagingLoading').classList.remove('hidden');
    document.getElementById('bulkMessagingForm').classList.add('hidden');
    document.getElementById('bulkMessagingError').classList.add('hidden');
}

// Form durumunu göster
function showBulkMessagingForm() {
    document.getElementById('bulkMessagingLoading').classList.add('hidden');
    document.getElementById('bulkMessagingForm').classList.remove('hidden');
    document.getElementById('bulkMessagingError').classList.add('hidden');
}

// Hata durumunu göster
function showBulkMessagingError(message) {
    document.getElementById('bulkMessagingLoading').classList.add('hidden');
    document.getElementById('bulkMessagingForm').classList.add('hidden');
    document.getElementById('bulkMessagingError').classList.remove('hidden');
    document.getElementById('bulkMessagingErrorMessage').textContent = message;
}

// Kanal ayarlarını oluştur
function createChannelSettings() {
    const container = document.getElementById('channelSettings');

    if (availableChannels.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4 text-gray-500">
                <p>Henüz aktif iletişim kanalı bulunamadı.</p>
                <p class="text-sm">Lütfen önce Email veya WhatsApp entegrasyonlarınızı yapılandırın.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = availableChannels.map(channel => `
        <div class="border border-gray-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <input type="checkbox" id="channel_${channel.channelType}"
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                           ${enabledChannels.includes(channel.channelType) ? 'checked' : ''}
                           onchange="toggleChannel('${channel.channelType}')">
                    <label for="channel_${channel.channelType}" class="ml-2 text-sm font-medium text-gray-900">
                        ${channel.displayName}
                    </label>
                </div>
                <span class="text-sm text-gray-500">${channel.formattedCostPerMessage} / mesaj</span>
            </div>

            <div id="channelConfig_${channel.channelType}" class="${enabledChannels.includes(channel.channelType) ? '' : 'hidden'}">
                <div class="grid grid-cols-1 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Şablon Seçin</label>
                        <select id="template_${channel.channelType}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">Şablon seçin...</option>
                            ${channel.availableTemplates.map(template => `
                                <option value="${template.name}">${template.displayName}</option>
                            `).join('')}
                        </select>
                    </div>
                    ${channel.channelType === 'email' ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Konu (Opsiyonel)</label>
                            <input type="text" id="subject_${channel.channelType}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Email konusu">
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

// Kanalı aç/kapat
function toggleChannel(channelType) {
    const checkbox = document.getElementById(`channel_${channelType}`);
    const config = document.getElementById(`channelConfig_${channelType}`);

    if (checkbox.checked) {
        config.classList.remove('hidden');
    } else {
        config.classList.add('hidden');
    }
}

// Hedef müşteri sayısını hesapla
async function estimateRecipients() {
    try {
        const filters = getCustomerFilters();

        const response = await fetch('/BulkMessaging/EstimateRecipients', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('recipientCount').textContent = `Hedef müşteri sayısı: ${result.count.toLocaleString()}`;
        } else {
            document.getElementById('recipientCount').textContent = `Hata: ${result.message}`;
        }
    } catch (error) {
        document.getElementById('recipientCount').textContent = `Hata: ${error.message}`;
    }
}

// Müşteri filtrelerini al
function getCustomerFilters() {
    const statusValue = document.getElementById('customerStatusFilter').value;
    const emailPermissionValue = document.getElementById('emailPermissionFilter').value;
    const smsPermissionValue = document.getElementById('smsPermissionFilter').value;

    return {
        isActive: statusValue === '' ? null : statusValue === 'true',
        emailPermission: emailPermissionValue === '' ? null : emailPermissionValue === 'true',
        smsPermission: smsPermissionValue === '' ? null : smsPermissionValue === 'true',
        searchTerm: document.getElementById('customerSearchFilter').value || null,
        cities: [],
        membershipDateFrom: null,
        membershipDateTo: null,
        lastLoginDateFrom: null,
        lastLoginDateTo: null
    };
}

// Kanal ayarlarını al
function getChannelSettings() {
    const settings = [];

    availableChannels.forEach(channel => {
        const checkbox = document.getElementById(`channel_${channel.channelType}`);
        if (checkbox && checkbox.checked) {
            const templateSelect = document.getElementById(`template_${channel.channelType}`);
            const subjectInput = document.getElementById(`subject_${channel.channelType}`);

            settings.push({
                channelType: channel.channelType,
                isEnabled: true,
                templateName: templateSelect.value,
                subject: subjectInput ? subjectInput.value : null
            });
        }
    });

    return settings;
}

// Önizleme oluştur
async function createPreview() {
    try {
        const title = document.getElementById('bulkMessageTitle').value;
        if (!title.trim()) {
            alert('Lütfen başlık girin.');
            return;
        }

        const channelSettings = getChannelSettings();
        if (channelSettings.length === 0) {
            alert('Lütfen en az bir iletişim kanalı seçin ve şablon belirtin.');
            return;
        }

        const model = {
            title: title,
            description: document.getElementById('bulkMessageDescription').value,
            customerFilters: getCustomerFilters(),
            channelSettings: channelSettings,
            enablePreview: true
        };

        const response = await fetch('/BulkMessaging/CreatePreview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(model)
        });

        const result = await response.json();

        if (result.success) {
            currentPreview = result.data;
            displayPreview(result.data);
            document.getElementById('sendBulkMessageBtn').disabled = !result.data.canAfford;
        } else {
            alert(`Önizleme oluşturulamadı: ${result.message}`);
        }
    } catch (error) {
        alert(`Hata: ${error.message}`);
    }
}

// Önizlemeyi göster
function displayPreview(preview) {
    const container = document.getElementById('previewContent');

    container.innerHTML = `
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">${preview.estimatedRecipients.toLocaleString()}</div>
                    <div class="text-sm text-gray-500">Hedef Müşteri</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">₺${preview.estimatedCost.toFixed(2)}</div>
                    <div class="text-sm text-gray-500">Tahmini Maliyet</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold ${preview.canAfford ? 'text-green-600' : 'text-red-600'}">${preview.canAfford ? 'Yeterli' : 'Yetersiz'}</div>
                    <div class="text-sm text-gray-500">Bakiye Durumu</div>
                </div>
            </div>

            ${preview.warningMessage ? `
                <div class="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                    <div class="flex">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        <div class="ml-3">
                            <p class="text-sm text-red-800">${preview.warningMessage}</p>
                        </div>
                    </div>
                </div>
            ` : ''}

            <div class="space-y-3">
                <h5 class="font-medium text-gray-900">Kanal Detayları:</h5>
                ${preview.channelPreviews.map(channel => `
                    <div class="bg-white border border-gray-200 rounded-md p-3">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="font-medium">${channel.channelType === 'email' ? 'Email' : 'WhatsApp'}</span>
                                <span class="text-sm text-gray-500 ml-2">${channel.templateName}</span>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium">${channel.formattedTotalCost}</div>
                                <div class="text-xs text-gray-500">${channel.estimatedRecipients} alıcı</div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            ${preview.sampleCustomers.length > 0 ? `
                <div class="mt-4">
                    <h5 class="font-medium text-gray-900 mb-2">Örnek Müşteriler (İlk 5):</h5>
                    <div class="space-y-2">
                        ${preview.sampleCustomers.map(customer => `
                            <div class="bg-white border border-gray-200 rounded-md p-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium">${customer.fullName}</span>
                                    <span class="text-gray-500">${customer.availableChannels.join(', ')}</span>
                                </div>
                                <div class="text-gray-500">${customer.email}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    container.classList.remove('hidden');
    document.getElementById('previewCost').textContent = `Tahmini Maliyet: ₺${preview.estimatedCost.toFixed(2)}`;
}

// Toplu mesaj gönder
async function sendBulkMessage() {
    if (!currentPreview || !currentPreview.canAfford) {
        alert('Lütfen önce önizleme oluşturun ve bakiyenizin yeterli olduğundan emin olun.');
        return;
    }

    if (!confirm(`${currentPreview.estimatedRecipients} müşteriye toplu mesaj gönderilecek. Tahmini maliyet: ₺${currentPreview.estimatedCost.toFixed(2)}. Devam etmek istiyor musunuz?`)) {
        return;
    }

    try {
        const model = {
            title: document.getElementById('bulkMessageTitle').value,
            description: document.getElementById('bulkMessageDescription').value,
            customerFilters: getCustomerFilters(),
            channelSettings: getChannelSettings(),
            enablePreview: true
        };

        const response = await fetch('/BulkMessaging/CreateBulkMessage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(model)
        });

        const result = await response.json();

        if (result.success) {
            // Gönderimi başlat
            const startResponse = await fetch(`/BulkMessaging/StartBulkMessage?bulkMessageId=${result.bulkMessageId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const startResult = await startResponse.json();

            if (startResult.success) {
                alert('Toplu mesaj gönderimi başarıyla başlatıldı! İlerleme durumunu Store sayfasından takip edebilirsiniz.');
                closeBulkMessagingModal();
            } else {
                alert(`Gönderim başlatılamadı: ${startResult.message}`);
            }
        } else {
            alert(`Toplu mesaj oluşturulamadı: ${result.message}`);
        }
    } catch (error) {
        alert(`Hata: ${error.message}`);
    }
}

// Tab switching fonksiyonu
function switchBulkMessageTab(tab) {
    currentTab = tab;

    // Tab butonlarını güncelle
    const newTab = document.getElementById('newMessageTab');
    const historyTab = document.getElementById('jobHistoryTab');
    const newContent = document.getElementById('newMessageContent');
    const historyContent = document.getElementById('jobHistoryContent');

    if (tab === 'new') {
        newTab.classList.add('text-primary', 'border-b-2', 'border-primary');
        newTab.classList.remove('text-gray-500');
        historyTab.classList.remove('text-primary', 'border-b-2', 'border-primary');
        historyTab.classList.add('text-gray-500');

        newContent.classList.remove('hidden');
        historyContent.classList.add('hidden');
    } else {
        historyTab.classList.add('text-primary', 'border-b-2', 'border-primary');
        historyTab.classList.remove('text-gray-500');
        newTab.classList.remove('text-primary', 'border-b-2', 'border-primary');
        newTab.classList.add('text-gray-500');

        historyContent.classList.remove('hidden');
        newContent.classList.add('hidden');

        // Job history'yi yükle
        loadJobHistory();
    }
}

// Job history yükleme
async function loadJobHistory() {
    showJobHistoryLoading();

    try {
        const response = await fetch('/BulkMessaging/GetJobHistory');
        const result = await response.json();

        if (result.success) {
            jobHistoryData = result.data || [];
            displayJobHistory();
        } else {
            showJobHistoryError(result.message);
        }
    } catch (error) {
        console.error('Error loading job history:', error);
        showJobHistoryError('Gönderim geçmişi yüklenirken hata oluştu.');
    }
}

// Job history yenileme
function refreshJobHistory() {
    if (currentTab === 'history') {
        loadJobHistory();
    }
}

// Job history loading durumu
function showJobHistoryLoading() {
    document.getElementById('jobHistoryLoading').classList.remove('hidden');
    document.getElementById('jobHistoryList').classList.add('hidden');
    document.getElementById('noJobsState').classList.add('hidden');
}

// Job history gösterme
function displayJobHistory() {
    document.getElementById('jobHistoryLoading').classList.add('hidden');

    if (jobHistoryData.length === 0) {
        document.getElementById('noJobsState').classList.remove('hidden');
        document.getElementById('jobHistoryList').classList.add('hidden');
        return;
    }

    const container = document.getElementById('jobHistoryList');
    container.innerHTML = jobHistoryData.map(job => createJobHistoryItem(job)).join('');
    container.classList.remove('hidden');
    document.getElementById('noJobsState').classList.add('hidden');
}

// Job history item oluşturma
function createJobHistoryItem(job) {
    const statusColor = getJobStatusColor(job.status);
    const statusIcon = getJobStatusIcon(job.status);
    const progressPercentage = job.totalRecipients > 0 ? Math.round((job.processedRecipients / job.totalRecipients) * 100) : 0;

    return `
        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        ${statusIcon}
                    </div>
                    <div>
                        <h5 class="text-sm font-medium text-gray-900">${job.title}</h5>
                        <p class="text-xs text-gray-500">${job.description || 'Açıklama yok'}</p>
                    </div>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                        ${job.status}
                    </span>
                    <p class="text-xs text-gray-500 mt-1">${formatDate(job.createdAt)}</p>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm">
                <div>
                    <span class="text-gray-500">Hedef:</span>
                    <span class="font-medium ml-1">${job.totalRecipients.toLocaleString()}</span>
                </div>
                <div>
                    <span class="text-gray-500">İşlenen:</span>
                    <span class="font-medium ml-1">${job.processedRecipients.toLocaleString()}</span>
                </div>
                <div>
                    <span class="text-gray-500">Başarılı:</span>
                    <span class="font-medium ml-1 text-green-600">${job.successfulSends.toLocaleString()}</span>
                </div>
                <div>
                    <span class="text-gray-500">Hata:</span>
                    <span class="font-medium ml-1 text-red-600">${job.failedSends.toLocaleString()}</span>
                </div>
            </div>

            ${job.status === 'Gönderiliyor' ? `
                <div class="mb-3">
                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                        <span>İlerleme</span>
                        <span>${progressPercentage}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: ${progressPercentage}%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Batch ${job.currentBatch || 0}/${job.totalBatches || 0}</span>
                        <span>Tahmini Maliyet: ₺${(job.estimatedCost || 0).toFixed(2)}</span>
                    </div>
                </div>
            ` : ''}

            <div class="flex items-center justify-between">
                <div class="text-xs text-gray-500">
                    ${job.completedAt ? `Tamamlandı: ${formatDate(job.completedAt)}` : ''}
                    ${job.actualCost ? ` • Gerçek Maliyet: ₺${job.actualCost.toFixed(2)}` : ''}
                </div>
                <div class="flex space-x-2">
                    ${job.status === 'Gönderiliyor' ? `
                        <button onclick="refreshJobDetails(${job.id})" class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            Yenile
                        </button>
                    ` : ''}
                    <button onclick="viewJobDetails(${job.id})" class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        Detaylar
                    </button>
                </div>
            </div>

            ${job.errorMessage ? `
                <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                    <strong>Hata:</strong> ${job.errorMessage}
                </div>
            ` : ''}
        </div>
    `;
}

// Job status renkleri
function getJobStatusColor(status) {
    switch (status) {
        case 'Hazırlanıyor': return 'bg-yellow-100 text-yellow-800';
        case 'Gönderiliyor': return 'bg-blue-100 text-blue-800';
        case 'Tamamlandı': return 'bg-green-100 text-green-800';
        case 'Hata': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

// Job status ikonları
function getJobStatusIcon(status) {
    switch (status) {
        case 'Hazırlanıyor':
            return '<svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>';
        case 'Gönderiliyor':
            return '<svg class="w-5 h-5 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
        case 'Tamamlandı':
            return '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
        case 'Hata':
            return '<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
        default:
            return '<svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
    }
}

// Tarih formatlama
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Job detaylarını yenile
async function refreshJobDetails(jobId) {
    try {
        const response = await fetch(`/BulkMessaging/GetJobDetails/${jobId}`);
        const result = await response.json();

        if (result.success) {
            // Job history'deki ilgili item'ı güncelle
            const jobIndex = jobHistoryData.findIndex(job => job.id === jobId);
            if (jobIndex !== -1) {
                jobHistoryData[jobIndex] = result.data;
                displayJobHistory();
            }
        }
    } catch (error) {
        console.error('Error refreshing job details:', error);
    }
}

// Job detaylarını görüntüle
function viewJobDetails(jobId) {
    const job = jobHistoryData.find(j => j.id === jobId);
    if (job) {
        alert(`Job Detayları:\n\nBaşlık: ${job.title}\nDurum: ${job.status}\nHedef: ${job.totalRecipients}\nİşlenen: ${job.processedRecipients}\nBaşarılı: ${job.successfulSends}\nHata: ${job.failedSends}\nMaliyet: ₺${(job.actualCost || job.estimatedCost || 0).toFixed(2)}`);
    }
}

// Job history hata durumu
function showJobHistoryError(message) {
    document.getElementById('jobHistoryLoading').classList.add('hidden');
    document.getElementById('jobHistoryList').classList.add('hidden');
    document.getElementById('noJobsState').classList.add('hidden');

    // Basit hata gösterimi
    const container = document.getElementById('jobHistoryList');
    container.innerHTML = `
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Hata Oluştu</h3>
            <p class="mt-1 text-sm text-gray-500">${message}</p>
            <button onclick="loadJobHistory()" class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
                Tekrar Dene
            </button>
        </div>
    `;
    container.classList.remove('hidden');
}



// ===== YENİ MÜŞTERİ EKLEME FONKSİYONLARI =====

// Yeni müşteri modal'ını aç
function openAddCustomerModal() {
    document.getElementById('addCustomerModal').classList.remove('hidden');
    resetAddCustomerForm();
}

// Yeni müşteri modal'ını kapat
function closeAddCustomerModal() {
    document.getElementById('addCustomerModal').classList.add('hidden');
    resetAddCustomerForm();
}

// Yeni müşteri formunu sıfırla
function resetAddCustomerForm() {
    document.getElementById('addCustomerForm').reset();
    document.getElementById('isActive').checked = true;
    document.getElementById('saveCustomerBtnText').textContent = 'Kaydet';
    document.getElementById('saveCustomerBtn').disabled = false;
}



// ===== EXCEL İMPORT FONKSİYONLARI =====

let currentImportJobId = null;
let importProgressInterval = null;
let customerImportConnection = null;

// Excel import modal'ını aç
function openBulkImportModal() {
    document.getElementById('bulkImportModal').classList.remove('hidden');
    resetImportModal();
}

// Excel import modal'ını kapat
function closeBulkImportModal() {
    document.getElementById('bulkImportModal').classList.add('hidden');
    if (importProgressInterval) {
        clearInterval(importProgressInterval);
        importProgressInterval = null;
    }
}

// Import modal'ını sıfırla
function resetImportModal() {
    // Tüm section'ları gizle
    document.getElementById('uploadSection').classList.remove('hidden');
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('resultSection').classList.add('hidden');

    // Form'u sıfırla
    document.getElementById('bulkImportForm').reset();
    document.getElementById('selectedFileName').classList.add('hidden');

    // Progress'i sıfırla
    document.getElementById('progressBar').style.width = '0%';
    document.getElementById('progressText').textContent = 'Başlatılıyor...';
    document.getElementById('progressStats').textContent = '';

    // Result'ları sıfırla
    document.getElementById('successResult').classList.add('hidden');
    document.getElementById('errorResult').classList.add('hidden');

    currentImportJobId = null;
    if (importProgressInterval) {
        clearInterval(importProgressInterval);
        importProgressInterval = null;
    }
}





// Progress section'ını göster
function showProgressSection() {
    document.getElementById('uploadSection').classList.add('hidden');
    document.getElementById('progressSection').classList.remove('hidden');
    document.getElementById('resultSection').classList.add('hidden');
}

// Progress tracking başlat (SignalR ile real-time, fallback olarak polling)
function startProgressTracking() {
    if (!currentImportJobId) return;

    // SignalR bağlantısı varsa sadece onu kullan, yoksa polling'e geri dön
    if (customerImportConnection && customerImportConnection.state === signalR.HubConnectionState.Connected) {
        console.log("Using SignalR for real-time progress tracking");
        // SignalR ile real-time güncelleme alacağız, polling'e gerek yok
        return;
    }

    // Fallback: SignalR yoksa polling kullan
    console.log("Falling back to polling for progress tracking");
    importProgressInterval = setInterval(async () => {
        try {
            const response = await fetch(`/Customer/GetImportProgress?jobId=${currentImportJobId}`);
            const result = await response.json();

            if (result.success && result.data) {
                // Modal progress'i güncelle (eğer modal açıksa)
                updateProgress(result.data);

                // Tracking area progress'i güncelle
                updateImportTrackingProgress(result.data);

                if (result.data.isCompleted) {
                    clearInterval(importProgressInterval);
                    importProgressInterval = null;
                    showResultSection(result.data);

                    // Başarılı tamamlanırsa toast göster
                    if (result.data.status === 'Tamamlandı') {
                        showNotification(`İçe aktarma tamamlandı! ${result.data.successCount} müşteri eklendi.`, 'success');
                    } else {
                        showNotification('İçe aktarma işlemi hata ile sonuçlandı.', 'error');
                    }
                }
            } else {
                clearInterval(importProgressInterval);
                importProgressInterval = null;
                showError('İşlem durumu alınamadı.');

                // Tracking area'da da hata göster
                const statusText = document.getElementById('importStatusText');
                if (statusText) {
                    statusText.textContent = 'İşlem durumu alınamadı.';
                }
            }
        } catch (error) {
            console.error('Progress tracking error:', error);
            clearInterval(importProgressInterval);
            importProgressInterval = null;
            showError('İşlem takibi sırasında hata oluştu.');
        }
    }, 5000); // SignalR olmadığında 5 saniyede bir kontrol et (daha az yük)
}

// Progress güncelle
function updateProgress(data) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressStats = document.getElementById('progressStats');

    progressBar.style.width = `${data.progressPercentage}%`;

    if (data.status === 'Bekliyor') {
        progressText.textContent = 'İşlem sıraya alındı...';
    } else if (data.status === 'İşleniyor') {
        progressText.textContent = 'Dosya işleniyor...';
    }

    if (data.totalRows > 0) {
        progressStats.innerHTML = `
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>Toplam: ${data.totalRows}</div>
                <div>İşlenen: ${data.processedRows}</div>
                <div>Başarılı: ${data.successCount}</div>
                <div>Hata: ${data.errorCount}</div>
            </div>
        `;
    }
}

// Result section'ını göster
function showResultSection(data) {
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('resultSection').classList.remove('hidden');

    if (data.status === 'Tamamlandı') {
        document.getElementById('successResult').classList.remove('hidden');
        document.getElementById('errorResult').classList.add('hidden');

        document.getElementById('successStats').innerHTML = `
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Toplam Satır:</strong> ${data.totalRows}</div>
                <div><strong>Başarılı:</strong> ${data.successCount}</div>
                <div><strong>Hata:</strong> ${data.errorCount}</div>
                <div><strong>Süre:</strong> ${data.formattedDuration || '-'}</div>
            </div>
            ${data.errorCount > 0 && data.errorDetails ? `
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 class="text-sm font-medium text-yellow-800 mb-2">Hatalar:</h4>
                    <div class="text-xs text-yellow-700 max-h-32 overflow-y-auto">
                        ${data.errorDetails.split('\n').slice(0, 10).join('<br>')}
                        ${data.errorDetails.split('\n').length > 10 ? '<br>...' : ''}
                    </div>
                </div>
            ` : ''}
        `;
    } else {
        document.getElementById('successResult').classList.add('hidden');
        document.getElementById('errorResult').classList.remove('hidden');

        document.getElementById('errorMessage').textContent = data.errorDetails || 'Bilinmeyen bir hata oluştu.';
    }
}

// Hata göster
function showError(message) {
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('resultSection').classList.remove('hidden');
    document.getElementById('successResult').classList.add('hidden');
    document.getElementById('errorResult').classList.remove('hidden');

    document.getElementById('errorMessage').textContent = message;
}

// ===== ŞABLON İNDİRME FONKSİYONU =====

function downloadExcelTemplate() {
    window.location.href = '/Customer/DownloadExcelTemplate';
}

// ===== İMPORT DETAYLARI MODAL FONKSİYONLARI =====

let currentDetailJobId = null;

// Import detayları modal'ını aç
function openImportDetailsModal() {
    if (!currentImportJobId) {
        showNotification('Aktif bir import işlemi bulunamadı.', 'error');
        return;
    }

    currentDetailJobId = currentImportJobId;
    document.getElementById('importDetailsModal').classList.remove('hidden');
    loadImportDetails();
}

// Import detayları modal'ını kapat
function closeImportDetailsModal() {
    document.getElementById('importDetailsModal').classList.add('hidden');
    currentDetailJobId = null;
}

// Import detaylarını yükle
async function loadImportDetails() {
    if (!currentDetailJobId) return;

    try {
        const response = await fetch(`/Customer/GetImportProgress?jobId=${currentDetailJobId}`);
        const result = await response.json();

        if (result.success && result.data) {
            updateImportDetailsModal(result.data);
        } else {
            showNotification('Import detayları yüklenemedi.', 'error');
        }
    } catch (error) {
        console.error('Error loading import details:', error);
        showNotification('Import detayları yüklenirken hata oluştu.', 'error');
    }
}

// Import detayları modal'ını güncelle
function updateImportDetailsModal(data) {
    // Dosya adı
    document.getElementById('detailFileName').textContent = data.fileName || '-';

    // Durum badge
    const statusBadge = document.getElementById('detailStatusBadge');
    statusBadge.textContent = data.status || '-';
    statusBadge.className = `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(data.status)}`;

    // Zamanlar
    document.getElementById('detailStartedAt').textContent = data.formattedStartedAt || '-';
    document.getElementById('detailCompletedAt').textContent = data.formattedCompletedAt || '-';
    document.getElementById('detailDuration').textContent = data.formattedDuration || '-';

    // Progress
    const progressBar = document.getElementById('detailProgressBar');
    const progressText = document.getElementById('detailProgressText');
    progressBar.style.width = `${data.progressPercentage || 0}%`;
    progressText.textContent = `${data.progressPercentage || 0}%`;

    // İstatistikler
    document.getElementById('detailTotalRows').textContent = data.totalRows || 0;
    document.getElementById('detailSuccessCount').textContent = data.successCount || 0;
    document.getElementById('detailErrorCount').textContent = data.errorCount || 0;
    document.getElementById('detailProcessedRows').textContent = data.processedRows || 0;

    // Hata detayları
    const errorSection = document.getElementById('errorDetailsSection');
    const errorDetails = document.getElementById('detailErrorDetails');
    const errorCountBadge = document.getElementById('errorCountBadge');

    if (data.errorDetails && data.errorDetails.trim()) {
        errorSection.classList.remove('hidden');

        // Hata sayısını güncelle
        errorCountBadge.textContent = `${data.errorCount} hata`;

        // Hataları formatla
        const errors = data.errorDetails.split('\n').filter(error => error.trim());
        const formattedErrors = errors.map((error, index) => {
            const errorType = getErrorType(error);
            return `
                <div class="flex items-start space-x-2 py-2 ${index > 0 ? 'border-t border-red-100' : ''}">
                    <div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                        <span class="text-xs font-medium text-red-600">${index + 1}</span>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm text-red-800">${escapeHtml(error)}</div>
                        ${errorType ? `<div class="text-xs text-red-600 mt-1">${errorType}</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        errorDetails.innerHTML = formattedErrors;
    } else {
        errorSection.classList.add('hidden');
    }
}

// Durum badge class'ını al
function getStatusBadgeClass(status) {
    switch (status) {
        case 'Bekliyor':
            return 'bg-yellow-100 text-yellow-800';
        case 'İşleniyor':
            return 'bg-blue-100 text-blue-800';
        case 'Tamamlandı':
            return 'bg-green-100 text-green-800';
        case 'Hata':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

// Import detaylarını yenile
function refreshImportDetails() {
    loadImportDetails();
    showNotification('Detaylar yenilendi.', 'success');
}

// Hata tipini belirle
function getErrorType(error) {
    if (error.includes('E-posta zaten mevcut')) {
        return 'Çözüm: Bu e-posta adresi zaten sistemde kayıtlı. Farklı bir e-posta kullanın.';
    } else if (error.includes('Müşteri kodu zaten mevcut')) {
        return 'Çözüm: Bu müşteri kodu zaten kullanımda. Benzersiz bir kod belirleyin.';
    } else if (error.includes('Geçersiz e-posta adresi')) {
        return 'Çözüm: E-posta adresini doğru formatta yazın (örnek: <EMAIL>).';
    } else if (error.includes('zorunludur')) {
        return 'Çözüm: Zorunlu alanları doldurun (Ad, Soyad, E-posta).';
    } else if (error.includes('IX_Customers_ExternalId_CompanyId')) {
        return 'Çözüm: Sistem hatası - müşteri kodu çakışması. Lütfen tekrar deneyin.';
    } else if (error.includes('duplicate key')) {
        return 'Çözüm: Bu müşteri bilgileri zaten mevcut. Benzersiz bilgiler kullanın.';
    }
    return null;
}

// HTML escape fonksiyonu
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// ===== TELEFON NUMARASI FONKSİYONLARI =====

// Telefon numarasını formatla
function formatPhoneNumber(input) {
    // Sadece rakamları al
    let value = input.value.replace(/\D/g, '');

    // Türkiye telefon numarası formatı
    if (value.length > 0) {
        // 0 ile başlıyorsa kaldır
        if (value.startsWith('0')) {
            value = value.substring(1);
        }

        // 90 ile başlıyorsa kaldır (uluslararası format)
        if (value.startsWith('90') && value.length > 10) {
            value = value.substring(2);
        }

        // Maksimum 10 hane
        if (value.length > 10) {
            value = value.substring(0, 10);
        }

        // Formatla
        if (value.length > 0) {
            let formatted = '0';

            if (value.length > 0) {
                formatted += value.substring(0, 3);
            }
            if (value.length > 3) {
                formatted += ' ' + value.substring(3, 6);
            }
            if (value.length > 6) {
                formatted += ' ' + value.substring(6, 8);
            }
            if (value.length > 8) {
                formatted += ' ' + value.substring(8, 10);
            }

            input.value = formatted;
        }
    }
}

// Telefon numarası validasyonu
function validatePhoneNumber(phoneNumber) {
    if (!phoneNumber) return true; // Opsiyonel alan

    // Sadece rakamları al
    const digitsOnly = phoneNumber.replace(/\D/g, '');

    // 0 ile başlıyorsa kaldır
    let cleanNumber = digitsOnly;
    if (cleanNumber.startsWith('0')) {
        cleanNumber = cleanNumber.substring(1);
    }

    // 90 ile başlıyorsa kaldır
    if (cleanNumber.startsWith('90') && cleanNumber.length === 12) {
        cleanNumber = cleanNumber.substring(2);
    }

    // 10 haneli olmalı
    if (cleanNumber.length !== 10) {
        return false;
    }

    const firstDigit = cleanNumber[0];
    const secondDigit = cleanNumber[1];

    // Cep telefonu: 5XX ile başlar
    if (firstDigit === '5') {
        return true;
    }

    // Sabit hat: 2XX, 3XX, 4XX ile başlar ve ikinci hane 0 olamaz
    if ((firstDigit === '2' || firstDigit === '3' || firstDigit === '4') && secondDigit !== '0') {
        return true;
    }

    return false;
}

// Telefon input'larına event listener ekle
function setupPhoneInputs() {
    const phoneInput = document.getElementById('phone');
    const mobilePhoneInput = document.getElementById('mobilePhone');

    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            formatPhoneNumber(this);
        });

        phoneInput.addEventListener('blur', function() {
            if (this.value && !validatePhoneNumber(this.value)) {
                this.classList.add('border-red-500');
                this.classList.remove('border-gray-300');

                // Hata mesajı göster
                let errorMsg = this.parentNode.querySelector('.phone-error');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'phone-error mt-1 text-xs text-red-600';
                    this.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Geçerli bir Türkiye telefon numarası giriniz.';
            } else {
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');

                // Hata mesajını kaldır
                const errorMsg = this.parentNode.querySelector('.phone-error');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    }

    if (mobilePhoneInput) {
        mobilePhoneInput.addEventListener('input', function() {
            formatPhoneNumber(this);
        });

        mobilePhoneInput.addEventListener('blur', function() {
            if (this.value && !validatePhoneNumber(this.value)) {
                this.classList.add('border-red-500');
                this.classList.remove('border-gray-300');

                // Hata mesajı göster
                let errorMsg = this.parentNode.querySelector('.phone-error');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'phone-error mt-1 text-xs text-red-600';
                    this.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Geçerli bir Türkiye cep telefonu numarası giriniz.';
            } else {
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');

                // Hata mesajını kaldır
                const errorMsg = this.parentNode.querySelector('.phone-error');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    }
}

// ===== İMPORT TRAKİNG ALANI FONKSİYONLARI =====

// Import tracking alanını göster
function showImportTrackingArea() {
    const trackingArea = document.getElementById('importTrackingArea');
    if (trackingArea) {
        trackingArea.classList.remove('hidden');

        // İlk durumu ayarla
        document.getElementById('importStatusText').textContent = 'İşlem sıraya alındı...';
        document.getElementById('importProgressBar').style.width = '0%';
        document.getElementById('importTotalCount').textContent = '-';
        document.getElementById('importProcessedCount').textContent = '-';
        document.getElementById('importSuccessCount').textContent = '-';
        document.getElementById('importErrorCount').textContent = '-';
        document.getElementById('importActionButtons').classList.add('hidden');
    }
}

// Import tracking alanını gizle
function hideImportTrackingArea() {
    const trackingArea = document.getElementById('importTrackingArea');
    if (trackingArea) {
        trackingArea.classList.add('hidden');
    }

    // Progress tracking'i durdur
    if (importProgressInterval) {
        clearInterval(importProgressInterval);
        importProgressInterval = null;
    }
}

// Import tracking alanındaki progress'i güncelle
function updateImportTrackingProgress(data) {
    // Status text
    const statusText = document.getElementById('importStatusText');
    if (statusText) {
        if (data.status === 'Bekliyor') {
            statusText.textContent = 'İşlem sıraya alındı...';
        } else if (data.status === 'İşleniyor') {
            statusText.textContent = 'Dosya işleniyor...';
        } else if (data.status === 'Tamamlandı') {
            statusText.textContent = 'İşlem başarıyla tamamlandı!';
        } else if (data.status === 'Hata') {
            statusText.textContent = 'İşlem sırasında hata oluştu.';
        }
    }

    // Progress bar
    const progressBar = document.getElementById('importProgressBar');
    if (progressBar) {
        progressBar.style.width = `${data.progressPercentage}%`;
    }

    // Stats
    document.getElementById('importTotalCount').textContent = data.totalRows || '-';
    document.getElementById('importProcessedCount').textContent = data.processedRows || '-';
    document.getElementById('importSuccessCount').textContent = data.successCount || '-';
    document.getElementById('importErrorCount').textContent = data.errorCount || '-';

    // İşlem tamamlandıysa action butonlarını göster
    if (data.isCompleted) {
        document.getElementById('importActionButtons').classList.remove('hidden');

        // Progress tracking'i durdur
        if (importProgressInterval) {
            clearInterval(importProgressInterval);
            importProgressInterval = null;
        }
    }
}

// ===== SİGNALR BAĞLANTISI =====

// SignalR bağlantısını başlat
function initializeSignalRConnection() {
    if (customerImportConnection) {
        return; // Zaten bağlı
    }

    customerImportConnection = new signalR.HubConnectionBuilder()
        .withUrl("/customerImportHub")
        .withAutomaticReconnect()
        .build();

    // Import progress güncellemelerini dinle
    customerImportConnection.on("ImportProgressUpdate", function (progressData) {
        console.log("SignalR Progress Update:", progressData);

        // Eğer tracking area görünürse güncelle
        if (currentImportJobId === progressData.jobId) {
            updateImportTrackingProgress(progressData);

            // İşlem tamamlandıysa notification göster
            if (progressData.isCompleted) {
                if (progressData.status === 'Tamamlandı') {
                    showNotification(`İçe aktarma tamamlandı! ${progressData.successCount} müşteri eklendi.`, 'success');
                } else if (progressData.status === 'Hata') {
                    showNotification('İçe aktarma işlemi hata ile sonuçlandı.', 'error');
                }

                // Progress tracking'i durdur
                if (importProgressInterval) {
                    clearInterval(importProgressInterval);
                    importProgressInterval = null;
                }
            }
        }
    });

    // Bağlantıyı başlat
    customerImportConnection.start()
        .then(function () {
            console.log("CustomerImport SignalR connection started");
            // Company grubuna katıl
            return customerImportConnection.invoke("JoinCompanyGroup");
        })
        .then(function () {
            console.log("Joined company group for import updates");
        })
        .catch(function (err) {
            console.error("CustomerImport SignalR connection error:", err);
        });

    // Bağlantı koptuğunda
    customerImportConnection.onclose(function () {
        console.log("CustomerImport SignalR connection closed");
        customerImportConnection = null;
    });

    // Yeniden bağlandığında
    customerImportConnection.onreconnected(function () {
        console.log("CustomerImport SignalR reconnected");
        // Company grubuna tekrar katıl
        customerImportConnection.invoke("JoinCompanyGroup");
    });
}

// SignalR bağlantısını kapat
function closeSignalRConnection() {
    if (customerImportConnection) {
        customerImportConnection.stop();
        customerImportConnection = null;
    }
}

// ===== EVENT LİSTENER'LAR =====

// DOM hazır olduğunda event listener'ları ekle
document.addEventListener('DOMContentLoaded', function() {
    // SignalR bağlantısını başlat
    initializeSignalRConnection();

    // Telefon input'larını setup et
    setupPhoneInputs();
    // Buton event listener'ları ekle
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    const bulkImportBtn = document.getElementById('bulkImportBtn');
    const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');
    const bulkMessagingBtn = document.getElementById('bulkMessagingBtn');

    if (addCustomerBtn) {
        addCustomerBtn.addEventListener('click', openAddCustomerModal);
    }

    if (bulkImportBtn) {
        bulkImportBtn.addEventListener('click', openBulkImportModal);
    }

    if (downloadTemplateBtn) {
        downloadTemplateBtn.addEventListener('click', downloadExcelTemplate);
    }

    if (bulkMessagingBtn) {
        bulkMessagingBtn.addEventListener('click', openBulkMessagingModal);
    }

    // Import tracking area event listeners
    const hideTrackingBtn = document.getElementById('hideTrackingBtn');
    if (hideTrackingBtn) {
        hideTrackingBtn.addEventListener('click', hideImportTrackingArea);
    }

    const refreshCustomersBtn = document.getElementById('refreshCustomersBtn');
    if (refreshCustomersBtn) {
        refreshCustomersBtn.addEventListener('click', function() {
            loadCustomers();
            showNotification('Müşteri listesi yenilendi.', 'success');
        });
    }

    const viewImportDetailsBtn = document.getElementById('viewImportDetailsBtn');
    if (viewImportDetailsBtn) {
        viewImportDetailsBtn.addEventListener('click', openImportDetailsModal);
    }

    // Excel file input event listener
    const excelFileInput = document.getElementById('excelFile');
    if (excelFileInput) {
        excelFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileNameDiv = document.getElementById('selectedFileName');

            if (file) {
                fileNameDiv.textContent = `Seçilen dosya: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                fileNameDiv.classList.remove('hidden');
            } else {
                fileNameDiv.classList.add('hidden');
            }
        });
    }

    // Form event listeners
    const addCustomerForm = document.getElementById('addCustomerForm');
    if (addCustomerForm) {
        addCustomerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const btn = document.getElementById('saveCustomerBtn');
            const btnText = document.getElementById('saveCustomerBtnText');
            const originalText = btnText.textContent;

            // Button'u disable et
            btn.disabled = true;
            btnText.textContent = 'Kaydediliyor...';

            try {
                const formData = new FormData(this);
                const data = {};

                // Form verilerini object'e çevir
                for (let [key, value] of formData.entries()) {
                    if (this.querySelector(`[name="${key}"]`).type === 'checkbox') {
                        data[key] = this.querySelector(`[name="${key}"]`).checked;
                    } else if (this.querySelector(`[name="${key}"]`).type === 'number') {
                        data[key] = value ? parseFloat(value) : 0;
                    } else {
                        data[key] = value || null;
                    }
                }

                const response = await fetch('/Customer/CreateCustomer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Müşteri başarıyla eklendi!', 'success');
                    closeAddCustomerModal();
                    loadCustomers(); // Müşteri listesini yenile
                } else {
                    showNotification(result.message || 'Müşteri eklenirken hata oluştu.', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            } finally {
                btn.disabled = false;
                btnText.textContent = originalText;
            }
        });
    }

    // Bulk import form event listener
    const bulkImportForm = document.getElementById('bulkImportForm');
    if (bulkImportForm) {
        bulkImportForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInput = document.getElementById('excelFile');
            const file = fileInput.files[0];

            if (!file) {
                showNotification('Lütfen bir dosya seçin.', 'error');
                return;
            }

            // Dosya boyutu kontrolü
            if (file.size > 10 * 1024 * 1024) {
                showNotification('Dosya boyutu 10MB\'dan büyük olamaz.', 'error');
                return;
            }

            // Dosya formatı kontrolü
            const allowedTypes = ['.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                showNotification('Sadece .xlsx ve .xls dosyaları desteklenir.', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/Customer/BulkImport', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success && result.jobId) {
                    currentImportJobId = result.jobId;

                    // Modal'ı kapat ve tracking alanını göster
                    closeBulkImportModal();
                    showImportTrackingArea();
                    startProgressTracking();

                    showNotification('İçe aktarma işlemi başlatıldı! İşlemi yukarıdaki alandan takip edebilirsiniz.', 'success');
                } else {
                    showNotification(result.message || 'İçe aktarma başlatılırken hata oluştu.', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            }
        });
    }

    // Sayfa kapandığında SignalR bağlantısını kapat
    window.addEventListener('beforeunload', function() {
        closeSignalRConnection();
    });
});
</script>

<!-- Yeni Müşteri Ekleme Modal -->
<div id="addCustomerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">Yeni Müşteri Ekle</h3>
        <button onclick="closeAddCustomerModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4">
        <form id="addCustomerForm" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Ad -->
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700">Ad *</label>
              <input type="text" id="firstName" name="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Soyad -->
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700">Soyad *</label>
              <input type="text" id="lastName" name="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- E-posta -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">E-posta *</label>
              <input type="email" id="email" name="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Telefon -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700">Telefon</label>
              <input type="tel" id="phone" name="phone" placeholder="0212 123 45 67" maxlength="15" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <p class="mt-1 text-xs text-gray-500">Örnek: 0212 123 45 67 (Sabit hat)</p>
            </div>

            <!-- Cep Telefonu -->
            <div>
              <label for="mobilePhone" class="block text-sm font-medium text-gray-700">Cep Telefonu</label>
              <input type="tel" id="mobilePhone" name="mobilePhone" placeholder="0555 123 45 67" maxlength="15" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <p class="mt-1 text-xs text-gray-500">Örnek: 0555 123 45 67 (Cep telefonu)</p>
            </div>

            <!-- Doğum Tarihi -->
            <div>
              <label for="birthDate" class="block text-sm font-medium text-gray-700">Doğum Tarihi</label>
              <input type="date" id="birthDate" name="birthDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Cinsiyet -->
            <div>
              <label for="genderId" class="block text-sm font-medium text-gray-700">Cinsiyet</label>
              <select id="genderId" name="genderId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                <option value="">Seçiniz</option>
                <option value="1">Erkek</option>
                <option value="2">Kadın</option>
              </select>
            </div>

            <!-- Şehir -->
            <div>
              <label for="city" class="block text-sm font-medium text-gray-700">Şehir</label>
              <input type="text" id="city" name="city" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- İlçe -->
            <div>
              <label for="district" class="block text-sm font-medium text-gray-700">İlçe</label>
              <input type="text" id="district" name="district" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Müşteri Kodu -->
            <div>
              <label for="customerCode" class="block text-sm font-medium text-gray-700">Müşteri Kodu</label>
              <input type="text" id="customerCode" name="customerCode" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Üyelik Tipi -->
            <div>
              <label for="membershipType" class="block text-sm font-medium text-gray-700">Üyelik Tipi</label>
              <input type="text" id="membershipType" name="membershipType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Puan Bakiyesi -->
            <div>
              <label for="pointBalance" class="block text-sm font-medium text-gray-700">Puan Bakiyesi</label>
              <input type="number" id="pointBalance" name="pointBalance" value="0" min="0" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Kredi Limiti -->
            <div>
              <label for="creditLimit" class="block text-sm font-medium text-gray-700">Kredi Limiti</label>
              <input type="number" id="creditLimit" name="creditLimit" value="0" min="0" step="0.01" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Meslek -->
            <div>
              <label for="profession" class="block text-sm font-medium text-gray-700">Meslek</label>
              <input type="text" id="profession" name="profession" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Eğitim Seviyesi -->
            <div>
              <label for="educationLevel" class="block text-sm font-medium text-gray-700">Eğitim Seviyesi</label>
              <input type="text" id="educationLevel" name="educationLevel" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>

          <!-- Checkbox'lar -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
            <div class="flex items-center">
              <input type="checkbox" id="isActive" name="isActive" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="isActive" class="ml-2 block text-sm text-gray-900">Aktif</label>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="emailPermission" name="emailPermission" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="emailPermission" class="ml-2 block text-sm text-gray-900">E-posta İzni</label>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="smsPermission" name="smsPermission" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="smsPermission" class="ml-2 block text-sm text-gray-900">SMS İzni</label>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="kvkkApproval" name="kvkkApproval" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="kvkkApproval" class="ml-2 block text-sm text-gray-900">KVKK Onayı</label>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="membershipAgreementApproval" name="membershipAgreementApproval" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="membershipAgreementApproval" class="ml-2 block text-sm text-gray-900">Üyelik Sözleşmesi Onayı</label>
            </div>
          </div>

          <!-- Butonlar -->
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button type="button" onclick="closeAddCustomerModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              İptal
            </button>
            <button type="submit" id="saveCustomerBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              <span id="saveCustomerBtnText">Kaydet</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Excel Import Modal -->
<div id="bulkImportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">Excel İle Toplu Müşteri Ekleme</h3>
        <button onclick="closeBulkImportModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4">
        <!-- Upload Section -->
        <div id="uploadSection">
          <div class="mb-4">
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="m9 12 2 2 4-4"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800">Önemli Bilgiler</h3>
                  <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                      <li>Excel dosyası .xlsx formatında olmalıdır</li>
                      <li>Maksimum dosya boyutu: 10MB</li>
                      <li>Ad, Soyad ve E-posta alanları zorunludur</li>
                      <li>Mevcut e-posta adresleri atlanacaktır</li>
                      <li>İşlem arka planda çalışacak ve bildirim alacaksınız</li>
                    </ul>
                  </div>
                  <div class="mt-3">
                    <button id="downloadTemplateBtn" class="inline-flex items-center px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                      Excel Şablonunu İndir
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <form id="bulkImportForm" enctype="multipart/form-data">
            <div class="mb-4">
              <label for="excelFile" class="block text-sm font-medium text-gray-700 mb-2">Excel Dosyası Seçin</label>
              <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                <div class="space-y-1 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <div class="flex text-sm text-gray-600">
                    <label for="excelFile" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                      <span>Dosya seçin</span>
                      <input id="excelFile" name="excelFile" type="file" accept=".xlsx,.xls" class="sr-only" required>
                    </label>
                    <p class="pl-1">veya sürükleyip bırakın</p>
                  </div>
                  <p class="text-xs text-gray-500">Sadece .xlsx dosyaları (max 10MB)</p>
                </div>
              </div>
              <div id="selectedFileName" class="mt-2 text-sm text-gray-600 hidden"></div>
            </div>

            <div class="flex justify-end space-x-3">
              <button type="button" onclick="closeBulkImportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                İptal
              </button>
              <button type="submit" id="startImportBtn" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                <span id="startImportBtnText">İçe Aktarmayı Başlat</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Progress Section -->
        <div id="progressSection" class="hidden">
          <div class="text-center">
            <div class="mb-4">
              <svg class="animate-spin mx-auto h-12 w-12 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">İçe Aktarma İşlemi Devam Ediyor</h3>
            <p class="text-gray-600 mb-4">Dosyanız işleniyor, lütfen bekleyin...</p>

            <div class="bg-gray-200 rounded-full h-2 mb-4">
              <div id="progressBar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>

            <div class="text-sm text-gray-600">
              <div id="progressText">Başlatılıyor...</div>
              <div id="progressStats" class="mt-2"></div>
            </div>
          </div>
        </div>

        <!-- Result Section -->
        <div id="resultSection" class="hidden">
          <div class="text-center">
            <div id="successResult" class="hidden">
              <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">İçe Aktarma Tamamlandı!</h3>
              <div id="successStats" class="text-gray-600 mb-4"></div>
            </div>

            <div id="errorResult" class="hidden">
              <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">İçe Aktarma Hatası</h3>
              <div id="errorMessage" class="text-red-600 mb-4"></div>
            </div>

            <div class="flex justify-center space-x-3">
              <button onclick="resetImportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Yeni İçe Aktarma
              </button>
              <button onclick="closeBulkImportModal(); loadCustomers();" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Müşteri Listesini Yenile
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Import Detayları Modal -->
<div id="importDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">İçe Aktarma Detayları</h3>
        <button onclick="closeImportDetailsModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="mt-4">
        <!-- Job Info Section -->
        <div class="bg-gray-50 rounded-lg p-4 mb-4">
          <h4 class="text-md font-medium text-gray-900 mb-3">İşlem Bilgileri</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Dosya Adı</label>
              <p id="detailFileName" class="mt-1 text-sm text-gray-900">-</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Durum</label>
              <p id="detailStatus" class="mt-1">
                <span id="detailStatusBadge" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Başlangıç Zamanı</label>
              <p id="detailStartedAt" class="mt-1 text-sm text-gray-900">-</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Bitiş Zamanı</label>
              <p id="detailCompletedAt" class="mt-1 text-sm text-gray-900">-</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">İşlem Süresi</label>
              <p id="detailDuration" class="mt-1 text-sm text-gray-900">-</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">İlerleme</label>
              <div class="mt-1">
                <div class="bg-gray-200 rounded-full h-2">
                  <div id="detailProgressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p id="detailProgressText" class="text-xs text-gray-600 mt-1">0%</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics Section -->
        <div class="bg-white border rounded-lg p-4 mb-4">
          <h4 class="text-md font-medium text-gray-900 mb-3">İstatistikler</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600" id="detailTotalRows">0</div>
              <div class="text-sm text-gray-600">Toplam Satır</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600" id="detailSuccessCount">0</div>
              <div class="text-sm text-gray-600">Başarılı</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600" id="detailErrorCount">0</div>
              <div class="text-sm text-gray-600">Hata</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-600" id="detailProcessedRows">0</div>
              <div class="text-sm text-gray-600">İşlenen</div>
            </div>
          </div>
        </div>

        <!-- Error Details Section -->
        <div id="errorDetailsSection" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4 hidden">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-md font-medium text-red-900">Hata Detayları</h4>
            <span id="errorCountBadge" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              0 hata
            </span>
          </div>
          <div class="bg-white border border-red-200 rounded p-3 max-h-64 overflow-y-auto">
            <div id="detailErrorDetails" class="text-sm text-red-800">
              <div class="text-gray-500 italic">Hata bulunamadı.</div>
            </div>
          </div>
          <div class="mt-3 text-xs text-red-600">
            <svg class="w-4 h-4 inline mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            Bu hatalar Excel dosyasındaki veri sorunlarından kaynaklanmaktadır. Dosyayı düzenleyip tekrar yükleyebilirsiniz.
          </div>
        </div>

        <!-- Actions Section -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
          <button onclick="refreshImportDetails()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
            <svg class="w-4 h-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c2.12 0 4.07.74 5.61 1.98"></path>
              <path d="M17 3l4 4-4 4"></path>
            </svg>
            Yenile
          </button>
          <button onclick="closeImportDetailsModal()" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Kapat
          </button>
        </div>
      </div>
    </div>
  </div>
</div>