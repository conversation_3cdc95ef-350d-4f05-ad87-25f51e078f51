@model PushDashboard.ViewModels.StoreIndexViewModel

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">

  <div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex items-center">
      <div class="p-3 rounded-full bg-purple-light text-purple">
        <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 2v20m8-10H4"></path>
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-gray-500 text-sm">K<PERSON>i Ba<PERSON>i</h3>
        <p class="text-2xl font-semibold">@Model.Stats.FormattedCredits</p>
      </div>
    </div>
    <div class="mt-4 text-sm text-gray-600">
      @Model.Stats.FormattedLastUsage
    </div>
  </div>
  
  <div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex items-center">
      <div class="p-3 rounded-full bg-green-light text-green">
        <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-gray-500 text-sm">Sahip Olunan</h3>
        <p class="text-2xl font-semibold">@Model.OwnedModules.Count</p>
      </div>
    </div>
    <div class="mt-4 text-sm text-gray-600">
      @Model.Modules.Count toplam modül
    </div>
  </div>

  <div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex items-center">
      <div class="p-3 rounded-full bg-blue-light text-blue">
        <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-gray-500 text-sm">Aktif Modül</h3>
        <p class="text-2xl font-semibold">@Model.Stats.ActiveModules</p>
      </div>
    </div>
    <div class="mt-4 text-sm text-green">
      @(Model.OwnedModules.Count - Model.Stats.ActiveModules) pasif modül
    </div>
  </div>

  <div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex items-center">
      <div class="p-3 rounded-full bg-yellow-light text-yellow">
        <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M12 6v6l4 2"></path>
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-gray-500 text-sm">Bekleyen</h3>
        <p class="text-2xl font-semibold">@Model.AvailableModules.Count</p>
      </div>
    </div>
    <div class="mt-4 text-sm text-gray-600">
      Satın alınabilir modül
    </div>
  </div>

</div>
