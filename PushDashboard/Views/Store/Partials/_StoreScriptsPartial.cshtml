<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
  // Module purchase functionality
  function purchaseModule(moduleId, moduleName, modulePrice) {
    showPurchaseModal(moduleId, moduleName, modulePrice);
  }

  // Show insufficient balance modal
  function showInsufficientBalanceModal(data) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.id = 'insufficient-balance-modal';

    modal.innerHTML = `
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Yetersiz Bakiye</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500 mb-4">${data.message}</p>
            <div class="bg-gray-50 p-3 rounded-md text-left">
              <div class="flex justify-between text-sm">
                <span>Gerekli Tutar:</span>
                <span class="font-medium">₺${data.requiredAmount.toFixed(2)}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span>Mevcut Bakiye:</span>
                <span class="font-medium">₺${data.currentBalance.toFixed(2)}</span>
              </div>
              <div class="flex justify-between text-sm border-t pt-2 mt-2">
                <span>Eksik Tutar:</span>
                <span class="font-medium text-red-600">₺${data.missingAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>
          <div class="items-center px-4 py-3">
            <button id="add-credit-btn" 
                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-32 mr-2 hover:bg-blue-700">
              Kredi Ekle
            </button>
            <button id="close-modal-btn" 
                    class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24">
              Kapat
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Event listeners
    document.getElementById('add-credit-btn').addEventListener('click', () => {
      // Redirect to credit purchase page or show credit purchase modal
      window.location.href = '/Settings#billing';
      document.body.removeChild(modal);
    });

    document.getElementById('close-modal-btn').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }
  
  function showPurchaseModal(moduleId, moduleName, modulePrice) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.id = 'purchase-modal';

    modal.innerHTML = `
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Modül Satın Al</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              <strong>${moduleName}</strong> modülünü <strong>${modulePrice}</strong> karşılığında satın almak istediğinizden emin misiniz?
            </p>
          </div>
          <div class="items-center px-4 py-3">
            <button id="confirm-purchase" 
                    class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-green-700">
              Satın Al
            </button>
            <button id="cancel-purchase" 
                    class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24">
              İptal
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Event listeners
    document.getElementById('confirm-purchase').addEventListener('click', () => {
      performPurchase(moduleId, moduleName, modulePrice);
      document.body.removeChild(modal);
    });

    document.getElementById('cancel-purchase').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  async function performPurchase(moduleId, moduleName, modulePrice) {
    try {
      // Show loading notification
      showNotification('Satın alma işlemi gerçekleştiriliyor...', 'info');

      // Get anti-forgery token
      const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

      // Prepare purchase data
      const purchaseData = {
        moduleId: parseInt(moduleId),
        moduleName: moduleName,
        price: parseFloat(modulePrice.replace('₺', '').replace(',', '.')),
        paymentMethod: 'credit',
        acceptTerms: true
      };

      // Make API call
      const response = await axios.post('/Store/PurchaseModule', purchaseData, {
        headers: {
          'Content-Type': 'application/json',
          'RequestVerificationToken': token
        }
      });

      if (response.data.success) {
        // Show success notification with balance info
        const successMessage = response.data.newBalance !== undefined 
          ? `${moduleName} başarıyla satın alındı! Yeni bakiye: ₺${response.data.newBalance.toFixed(2)}`
          : `${moduleName} başarıyla satın alındı!`;
        
        showNotification(successMessage, 'success');

        // Refresh transaction history and page to show updated module status
        if (typeof window.loadTransactionHistory === "function") {
          window.loadTransactionHistory(1); // Reload first page of transactions
        }
        
        setTimeout(() => {
          window.location.reload();
        }, 2000);      } else {
        // Handle different error types
        if (response.data.errorType === 'insufficient_balance') {
          showInsufficientBalanceModal(response.data);
        } else {
          showNotification(response.data.message, 'error');
        }
      }

    } catch (error) {
      console.error('Purchase error:', error);
      if (error.response && error.response.data && error.response.data.message) {
        if (error.response.data.errorType === 'insufficient_balance') {
          showInsufficientBalanceModal(error.response.data);
        } else {
          showNotification(error.response.data.message, 'error');
        }
      } else {
        showNotification('Satın alma işlemi sırasında bir hata oluştu.', 'error');
      }
    }
  }

  // Filter functionality
  // Filter functionality
  function initializeFilters() {
    const categorySelect = document.getElementById('category-filter');
    const statusSelect = document.getElementById('status-filter');

    if (categorySelect) {
      categorySelect.addEventListener('change', function() {
        filterModules();
      });
    }

    if (statusSelect) {
      statusSelect.addEventListener('change', function() {
        filterModules();
      });
    }

    // Initialize filter results counter
    updateResultsCount();
  }

  function filterModules() {
    const categorySelect = document.getElementById('category-filter');
    const statusSelect = document.getElementById('status-filter');
    
    const selectedCategory = categorySelect?.value || '';
    const selectedStatus = statusSelect?.value || '';

    const moduleCards = document.querySelectorAll('.module-card');
    let visibleCount = 0;

    moduleCards.forEach(card => {
      let showCard = true;

      // Filter by status
      if (selectedStatus) {
        const cardStatus = card.getAttribute('data-status');
        if (cardStatus !== selectedStatus) {
          showCard = false;
        }
      }

      // Filter by category
      if (selectedCategory && showCard) {
        const cardCategoryId = card.getAttribute('data-category-id');
        if (cardCategoryId !== selectedCategory) {
          showCard = false;
        }
      }

      // Show/hide card with animation
      if (showCard) {
        card.style.display = 'block';
        card.style.opacity = '0';
        setTimeout(() => {
          card.style.opacity = '1';
          card.style.transition = 'opacity 0.3s ease-in-out';
        }, 50);
        visibleCount++;
      } else {
        card.style.opacity = '0';
        card.style.transition = 'opacity 0.3s ease-in-out';
        setTimeout(() => {
          card.style.display = 'none';
        }, 300);
      }
    });

    // Update results count
    updateResultsCount();
    
    // Show filter results info if filtering is active
    showFilterResults(selectedCategory, selectedStatus, visibleCount);
  }

  function updateResultsCount() {
    const visibleCards = document.querySelectorAll('.module-card:not([style*="display: none"])');
    const totalCards = document.querySelectorAll('.module-card');
    
    if (document.getElementById('visible-count')) {
      document.getElementById('visible-count').textContent = visibleCards.length;
    }
    if (document.getElementById('total-count')) {
      document.getElementById('total-count').textContent = totalCards.length;
    }
  }

  function showFilterResults(selectedCategory, selectedStatus, visibleCount) {
    const filterResults = document.getElementById('filter-results');
    const totalCards = document.querySelectorAll('.module-card').length;
    
    if (selectedCategory || selectedStatus) {
      filterResults.classList.remove('hidden');
      
      // Update filter description
      let filterDescription = 'Filtreler: ';
      const filters = [];
      
      if (selectedCategory) {
        const categorySelect = document.getElementById('category-filter');
        const categoryName = categorySelect.options[categorySelect.selectedIndex].text;
        filters.push(`Kategori: ${categoryName}`);
      }
      
      if (selectedStatus) {
        const statusText = selectedStatus === 'owned' ? 'Sahip Olunan' : 'Satın Alınabilir';
        filters.push(`Durum: ${statusText}`);
      }
      
      filterDescription += filters.join(', ');
      
      filterResults.innerHTML = `
        <div class="flex items-center justify-between">
          <p class="text-sm text-blue-800">
            ${filterDescription} - <span class="font-medium">${visibleCount}</span> modül gösteriliyor (toplam ${totalCards} modül)
          </p>
          <button onclick="clearFilters()" class="text-sm text-blue-600 hover:text-blue-800 underline">
            Filtreleri Temizle
          </button>
        </div>
      `;
    } else {
      filterResults.classList.add('hidden');
    }
    updateResultsCount();
  }

  
  function clearFilters() {
    document.getElementById('category-filter').value = '';
    document.getElementById('status-filter').value = '';
    filterModules();
  }

  function updateResultsCount() {
    const visibleCards = document.querySelectorAll('.module-card:not([style*="display: none"])');
    const totalCards = document.querySelectorAll('.module-card');
    
    // You can add a results counter element to show filtered results
    console.log(`Showing ${visibleCards.length} of ${totalCards.length} modules`);
  }

  // Module toggle functionality
  function toggleModule(moduleId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const actionText = isActive ? 'pasif yapmak' : 'aktif yapmak';

    if (confirm(`Bu modülü ${actionText} istediğinizden emin misiniz?`)) {
      performToggleModule(moduleId, !isActive);
    }
  }

  async function performToggleModule(moduleId, activate) {
    try {
      showNotification(`Modül durumu güncelleniyor...`, 'info');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const actionText = activate ? 'aktif yapıldı' : 'pasif yapıldı';
      showNotification(`Modül başarıyla ${actionText}!`, 'success');

      // Update UI or refresh
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Toggle error:', error);
      showNotification('Modül durumu güncellenirken bir hata oluştu.', 'error');
    }
  }

  // Notification function
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;
    
    const colors = {
      success: 'bg-green-500 text-white',
      error: 'bg-red-500 text-white',
      warning: 'bg-yellow-500 text-white',
      info: 'bg-blue-500 text-white'
    };
    
    notification.className += ` ${colors[type] || colors.info}`;
    notification.innerHTML = `
      <div class="flex items-center justify-between">
        <span>${message}</span>
        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
      notification.classList.add('translate-x-0');
    }, 100);
    
    // Auto remove
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }, 5000);
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
  });

  // Export functions for global use
  window.purchaseModule = purchaseModule;
  window.toggleModule = toggleModule;
  window.showNotification = showNotification;


  </script>
