@model PushDashboard.ViewModels.StoreIndexViewModel

<!-- Modül Grid -->
<div class="bg-white rounded-lg shadow-sm p-6">
  <div class="flex justify-between items-center mb-6">
    <h3 class="text-lg font-semibold">Mevcut Modüller</h3>
    <div class="flex space-x-2">
      <select id="category-filter" name="category" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        <option value="">Tüm <PERSON></option>
        @foreach (var category in Model.Categories)
        {
          <option value="@category.Id">
            @category.Name
          </option>
        }
      </select>
      <select id="status-filter" name="status" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        <option value="">Tüm Durumlar</option>
        <option value="owned"><PERSON><PERSON></option>
        <option value="available"><PERSON><PERSON><PERSON>n <PERSON></option>
      </select>
    </div>
  </div>

  <div id="modules-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @foreach (var module in Model.Modules)
    {
      @await Html.PartialAsync("Partials/_ModuleCardPartial", module)
    }
  </div>

  @if (!Model.Modules.Any())
  {
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
        <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Modül bulunamadı</h3>
      <p class="mt-1 text-sm text-gray-500">Seçilen kriterlere uygun modül bulunmuyor.</p>
    </div>
  }

  <!-- Filter Results Info -->
  <div id="filter-results" class="hidden mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
    <p class="text-sm text-blue-800">
      <span id="visible-count">0</span> modül gösteriliyor (toplam <span id="total-count">0</span> modül)
    </p>
  </div>
</div>
