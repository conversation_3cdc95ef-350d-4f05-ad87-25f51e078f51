@model PushDashboard.ViewModels.ChangePasswordViewModel

<div id="change-password-section">
    <h4 class="font-medium mb-4"><PERSON><PERSON><PERSON></h4>
    
    <!-- Success/Error Messages -->
    <div id="password-message" class="hidden mb-4">
        <div id="password-message-content" class="px-4 py-3 rounded"></div>
    </div>
    
    <form id="change-password-form" class="space-y-4">
        <div>
            <label for="CurrentPassword" class="block text-sm font-medium text-gray-700 mb-1">
                Mevcut Şifre <span class="text-red-500">*</span>
            </label>
            <input type="password" 
                   id="CurrentPassword" 
                   name="CurrentPassword" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                   required>
            <span id="CurrentPassword-error" class="text-red-500 text-sm hidden"></span>
        </div>
        
        <div>
            <label for="NewPassword" class="block text-sm font-medium text-gray-700 mb-1">
                <PERSON><PERSON> <span class="text-red-500">*</span>
            </label>
            <input type="password" 
                   id="NewPassword" 
                   name="NewPassword" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                   minlength="6"
                   required>
            <span id="NewPassword-error" class="text-red-500 text-sm hidden"></span>
            <p class="text-xs text-gray-500 mt-1">En az 6 karakter olmalıdır</p>
        </div>
        
        <div>
            <label for="ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
                Yeni Şifre (Tekrar) <span class="text-red-500">*</span>
            </label>
            <input type="password" 
                   id="ConfirmPassword" 
                   name="ConfirmPassword" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                   required>
            <span id="ConfirmPassword-error" class="text-red-500 text-sm hidden"></span>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" 
                    id="change-password-btn"
                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed">
                Şifreyi Güncelle
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const changePasswordForm = document.getElementById('change-password-form');
    const messageDiv = document.getElementById('password-message');
    const messageContent = document.getElementById('password-message-content');
    
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitButton = document.getElementById('change-password-btn');
            const originalText = submitButton.innerHTML;
            
            // Clear previous errors
            clearPasswordErrors();
            hidePasswordMessage();
            
            try {
                // Set loading state
                setPasswordButtonLoading(submitButton, true);
                
                const formData = {
                    currentPassword: document.getElementById('CurrentPassword').value,
                    newPassword: document.getElementById('NewPassword').value,
                    confirmPassword: document.getElementById('ConfirmPassword').value
                };
                
                // Client-side validation
                if (!validatePasswordForm(formData)) {
                    return;
                }
                
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
                
                const response = await axios.post('/Account/ChangePasswordAjax', formData, {
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    }
                });
                
                if (response.data.success) {
                    showPasswordMessage(response.data.message, 'success');
                    changePasswordForm.reset();
                } else {
                    showPasswordMessage(response.data.message, 'error');
                }
            } catch (error) {
                console.error('Error changing password:', error);
                if (error.response && error.response.data && error.response.data.message) {
                    showPasswordMessage(error.response.data.message, 'error');
                } else {
                    showPasswordMessage('Şifre değiştirilirken bir hata oluştu.', 'error');
                }
            } finally {
                resetPasswordButton(submitButton, originalText);
            }
        });
    }
    
    function validatePasswordForm(data) {
        let isValid = true;
        
        if (!data.currentPassword) {
            showPasswordFieldError('CurrentPassword', 'Mevcut şifre gereklidir.');
            isValid = false;
        }
        
        if (!data.newPassword || data.newPassword.length < 6) {
            showPasswordFieldError('NewPassword', 'Yeni şifre en az 6 karakter olmalıdır.');
            isValid = false;
        }
        
        if (data.newPassword !== data.confirmPassword) {
            showPasswordFieldError('ConfirmPassword', 'Şifreler eşleşmiyor.');
            isValid = false;
        }
        
        return isValid;
    }
    
    function showPasswordFieldError(fieldId, message) {
        const errorSpan = document.getElementById(fieldId + '-error');
        const field = document.getElementById(fieldId);
        
        if (errorSpan && field) {
            errorSpan.textContent = message;
            errorSpan.classList.remove('hidden');
            field.classList.add('border-red-500');
        }
    }
    
    function clearPasswordErrors() {
        const errorSpans = document.querySelectorAll('#change-password-form [id$="-error"]');
        const fields = document.querySelectorAll('#change-password-form input');
        
        errorSpans.forEach(span => {
            span.classList.add('hidden');
            span.textContent = '';
        });
        
        fields.forEach(field => {
            field.classList.remove('border-red-500');
        });
    }
    
    function showPasswordMessage(message, type) {
        messageContent.textContent = message;
        messageContent.className = `px-4 py-3 rounded ${type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'}`;
        messageDiv.classList.remove('hidden');
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                hidePasswordMessage();
            }, 5000);
        }
    }
    
    function hidePasswordMessage() {
        messageDiv.classList.add('hidden');
    }
    
    function setPasswordButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Güncelleniyor...';
        }
    }
    
    function resetPasswordButton(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
});
</script>
