@model PushDashboard.ViewModels.TwoFactorStatusViewModel

<div id="two-factor-section" class="border-t pt-6">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h4 class="font-medium"><PERSON><PERSON></h4>
            <p class="text-sm text-gray-500">Hesabınızı daha gü<PERSON>li hale getirin</p>
        </div>
        <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" 
                   id="two-factor-toggle" 
                   class="sr-only peer" 
                   @(Model.IsEnabled ? "checked" : "")>
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
        </label>
    </div>
    
    <div id="two-factor-status">
        @if (Model.IsEnabled)
        {
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-green-800 font-medium">İki faktörlü doğrulama etkin</span>
                </div>
                <p class="text-green-700 text-sm mt-2">Hesabınız ek güvenlik katmanı ile korunuyor.</p>
            </div>
        }
        else
        {
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <span class="text-yellow-800 font-medium">İki faktörlü doğrulama devre dışı</span>
                </div>
                <p class="text-yellow-700 text-sm mt-2">Hesabınızın güvenliğini artırmak için iki faktörlü doğrulamayı etkinleştirin.</p>
            </div>
        }
    </div>
    
    <!-- Success/Error Messages -->
    <div id="two-factor-message" class="hidden mt-4">
        <div id="two-factor-message-content" class="px-4 py-3 rounded"></div>
    </div>
</div>

<!-- 2FA Setup Modal -->
<div id="two-factor-setup-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">İki Faktörlü Doğrulama Kurulumu</h3>
                    <button id="close-setup-modal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div id="setup-step-1" class="setup-step">
                    <p class="text-sm text-gray-600 mb-4">
                        Authenticator uygulamanızla (Google Authenticator, Authy, vb.) aşağıdaki QR kodu tarayın:
                    </p>
                    
                    <div class="text-center mb-4">
                        <div id="qr-code-container" class="inline-block p-4 bg-white border rounded-lg">
                            <!-- QR Code will be inserted here -->
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Manuel giriş için kod:
                        </label>
                        <div class="flex">
                            <input type="text" 
                                   id="manual-entry-key" 
                                   readonly 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm font-mono">
                            <button id="copy-key-btn" 
                                    class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 text-sm">
                                Kopyala
                            </button>
                        </div>
                    </div>
                    
                    <button id="continue-setup-btn" 
                            class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
                        Devam Et
                    </button>
                </div>
                
                <div id="setup-step-2" class="setup-step hidden">
                    <p class="text-sm text-gray-600 mb-4">
                        Authenticator uygulamanızdan 6 haneli doğrulama kodunu girin:
                    </p>
                    
                    <form id="verify-setup-form">
                        <div class="mb-4">
                            <input type="text" 
                                   id="verification-code" 
                                   placeholder="000000"
                                   maxlength="6"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md text-center text-lg font-mono focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        
                        <div class="flex space-x-3">
                            <button type="button" 
                                    id="back-setup-btn"
                                    class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                                Geri
                            </button>
                            <button type="submit" 
                                    id="enable-2fa-btn"
                                    class="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed">
                                Etkinleştir
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggle = document.getElementById('two-factor-toggle');
    const setupModal = document.getElementById('two-factor-setup-modal');
    const closeModalBtn = document.getElementById('close-setup-modal');
    const continueBtn = document.getElementById('continue-setup-btn');
    const backBtn = document.getElementById('back-setup-btn');
    const verifyForm = document.getElementById('verify-setup-form');
    const copyKeyBtn = document.getElementById('copy-key-btn');
    
    let currentSetupData = null;
    
    // Toggle 2FA
    if (toggle) {
        toggle.addEventListener('change', async function() {
            if (this.checked) {
                // Enable 2FA - show setup modal
                await startTwoFactorSetup();
            } else {
                // Disable 2FA
                await disableTwoFactor();
            }
        });
    }
    
    // Modal controls
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', function() {
            hideSetupModal();
            // Reset toggle if setup was cancelled
            if (toggle && !@Model.IsEnabled.ToString().ToLower()) {
                toggle.checked = false;
            }
        });
    }
    
    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            showStep2();
        });
    }
    
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            showStep1();
        });
    }
    
    if (copyKeyBtn) {
        copyKeyBtn.addEventListener('click', function() {
            const keyInput = document.getElementById('manual-entry-key');
            keyInput.select();
            document.execCommand('copy');
            
            const originalText = this.textContent;
            this.textContent = 'Kopyalandı!';
            setTimeout(() => {
                this.textContent = originalText;
            }, 2000);
        });
    }
    
    if (verifyForm) {
        verifyForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await enableTwoFactor();
        });
    }
    
    // Auto-format verification code input
    const verificationInput = document.getElementById('verification-code');
    if (verificationInput) {
        verificationInput.addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '').substring(0, 6);
        });
    }
    
    async function startTwoFactorSetup() {
        try {
            const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            
            const response = await axios.post('/Settings/SetupTwoFactor', {}, {
                headers: {
                    'RequestVerificationToken': token
                }
            });
            
            if (response.data.success) {
                currentSetupData = response.data.data;
                showSetupModal();
            } else {
                showTwoFactorMessage(response.data.message, 'error');
                toggle.checked = false;
            }
        } catch (error) {
            console.error('Error setting up 2FA:', error);
            showTwoFactorMessage('Kurulum sırasında bir hata oluştu.', 'error');
            toggle.checked = false;
        }
    }
    
    async function enableTwoFactor() {
        const verificationCode = document.getElementById('verification-code').value;
        const enableBtn = document.getElementById('enable-2fa-btn');
        
        if (verificationCode.length !== 6) {
            showTwoFactorMessage('Lütfen 6 haneli doğrulama kodunu girin.', 'error');
            return;
        }
        
        try {
            const originalText = enableBtn.innerHTML;
            enableBtn.disabled = true;
            enableBtn.innerHTML = 'Etkinleştiriliyor...';
            
            const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            
            const response = await axios.post('/Settings/EnableTwoFactor', {
                verificationCode: verificationCode
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': token
                }
            });
            
            if (response.data.success) {
                hideSetupModal();
                updateTwoFactorStatus(true);
                showTwoFactorMessage(response.data.message, 'success');
            } else {
                showTwoFactorMessage(response.data.message, 'error');
            }
        } catch (error) {
            console.error('Error enabling 2FA:', error);
            showTwoFactorMessage('Etkinleştirme sırasında bir hata oluştu.', 'error');
        } finally {
            enableBtn.disabled = false;
            enableBtn.innerHTML = 'Etkinleştir';
        }
    }
    
    async function disableTwoFactor() {
        if (!confirm('İki faktörlü doğrulamayı devre dışı bırakmak istediğinizden emin misiniz?')) {
            toggle.checked = true;
            return;
        }
        
        try {
            const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
            
            const response = await axios.post('/Settings/DisableTwoFactor', {}, {
                headers: {
                    'RequestVerificationToken': token
                }
            });
            
            if (response.data.success) {
                updateTwoFactorStatus(false);
                showTwoFactorMessage(response.data.message, 'success');
            } else {
                showTwoFactorMessage(response.data.message, 'error');
                toggle.checked = true;
            }
        } catch (error) {
            console.error('Error disabling 2FA:', error);
            showTwoFactorMessage('Devre dışı bırakma sırasında bir hata oluştu.', 'error');
            toggle.checked = true;
        }
    }
    
    function showSetupModal() {
        if (currentSetupData) {
            // Load QR code
            const qrContainer = document.getElementById('qr-code-container');
            qrContainer.innerHTML = `<img src="/Settings/GetQrCode?qrCodeUri=${encodeURIComponent(currentSetupData.qrCodeUri)}" alt="QR Code" class="w-48 h-48">`;
            
            // Set manual entry key
            document.getElementById('manual-entry-key').value = currentSetupData.manualEntryKey;
            
            showStep1();
            setupModal.classList.remove('hidden');
        }
    }
    
    function hideSetupModal() {
        setupModal.classList.add('hidden');
        currentSetupData = null;
        document.getElementById('verification-code').value = '';
    }
    
    function showStep1() {
        document.getElementById('setup-step-1').classList.remove('hidden');
        document.getElementById('setup-step-2').classList.add('hidden');
    }
    
    function showStep2() {
        document.getElementById('setup-step-1').classList.add('hidden');
        document.getElementById('setup-step-2').classList.remove('hidden');
        document.getElementById('verification-code').focus();
    }
    
    function updateTwoFactorStatus(isEnabled) {
        const statusDiv = document.getElementById('two-factor-status');
        
        if (isEnabled) {
            statusDiv.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-green-800 font-medium">İki faktörlü doğrulama etkin</span>
                    </div>
                    <p class="text-green-700 text-sm mt-2">Hesabınız ek güvenlik katmanı ile korunuyor.</p>
                </div>
            `;
        } else {
            statusDiv.innerHTML = `
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span class="text-yellow-800 font-medium">İki faktörlü doğrulama devre dışı</span>
                    </div>
                    <p class="text-yellow-700 text-sm mt-2">Hesabınızın güvenliğini artırmak için iki faktörlü doğrulamayı etkinleştirin.</p>
                </div>
            `;
        }
        
        toggle.checked = isEnabled;
    }
    
    function showTwoFactorMessage(message, type) {
        const messageDiv = document.getElementById('two-factor-message');
        const messageContent = document.getElementById('two-factor-message-content');
        
        messageContent.textContent = message;
        messageContent.className = `px-4 py-3 rounded ${type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'}`;
        messageDiv.classList.remove('hidden');
        
        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                messageDiv.classList.add('hidden');
            }, 5000);
        }
    }
});
</script>
