
<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>@ViewData["Title"] - Pushonica</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#6366f1',
              dark: '#4f46e5',
              light: '#a5b4fc',
            },
            green: {
              light: '#dcfce7',
              DEFAULT: '#22c55e',
            },
            blue: {
              light: '#dbeafe',
              DEFAULT: '#3b82f6',
            },
            purple: {
              light: '#f3e8ff',
              DEFAULT: '#a855f7',
            },
            yellow: {
              light: '#fef9c3',
              DEFAULT: '#eab308',
            },
            red: {
              light: '#fee2e2',
              DEFAULT: '#ef4444',
            },
            orange: {
              light: '#fed7aa',
              DEFAULT: '#f97316',
            },
          }
        }
      }
    }
  </script>
</head>
<body class="bg-gray-50 text-gray-800 min-h-screen flex flex-col">
  @RenderBody()

  @await RenderSectionAsync("Scripts", required: false)
</body>
</html>