<script>
  let activeSessions = [];
  let sessionsLoaded = false;

  // Load active sessions when security tab is clicked
  async function loadActiveSessions() {
    console.log("loadActiveSessions function called");
    try {
      showSessionsLoading();

      const response = await axios.get('/Settings/GetActiveSessionsAjax');
      console.log('Active Sessions API Response:', response.data);

      if (response.data.success) {
        activeSessions = response.data.data.sessions || [];
        sessionsLoaded = true;

        if (!activeSessions || activeSessions.length === 0) {
          showSessionsEmpty();
        } else {
          displayActiveSessions();
        }
      } else {
        showSessionsError(response.data.message);
      }
    } catch (error) {
      console.error('Error loading active sessions:', error);
      if (error.response && error.response.data && error.response.data.message) {
        showSessionsError(error.response.data.message);
      } else {
        showSessionsError('Oturumlar yüklenirken bir hata o<PERSON>.');
      }
    }
  }

  function showSessionsLoading() {
    document.getElementById('sessions-loading').classList.remove('hidden');
    document.getElementById('sessions-error').classList.add('hidden');
    document.getElementById('sessions-container').classList.add('hidden');
    document.getElementById('sessions-empty').classList.add('hidden');
  }

  function showSessionsError(message) {
    document.getElementById('sessions-loading').classList.add('hidden');
    document.getElementById('sessions-error').classList.remove('hidden');
    document.getElementById('sessions-container').classList.add('hidden');
    document.getElementById('sessions-empty').classList.add('hidden');
    
    const errorElement = document.getElementById('sessions-error');
    const errorMessage = errorElement.querySelector('p');
    if (errorMessage) {
      errorMessage.textContent = message || 'Oturumlar yüklenirken bir hata oluştu.';
    }
  }

  function showSessionsEmpty() {
    document.getElementById('sessions-loading').classList.add('hidden');
    document.getElementById('sessions-error').classList.add('hidden');
    document.getElementById('sessions-container').classList.add('hidden');
    document.getElementById('sessions-empty').classList.remove('hidden');
  }

  function displayActiveSessions() {
    document.getElementById('sessions-loading').classList.add('hidden');
    document.getElementById('sessions-error').classList.add('hidden');
    document.getElementById('sessions-container').classList.remove('hidden');
    document.getElementById('sessions-empty').classList.add('hidden');

    const sessionsList = document.getElementById('sessions-list');
    sessionsList.innerHTML = '';

    activeSessions.forEach(session => {
      const sessionElement = createSessionElement(session);
      sessionsList.appendChild(sessionElement);
    });

    // Setup terminate all button
    setupTerminateAllButton();
  }

  function createSessionElement(session) {
    const sessionDiv = document.createElement('div');
    sessionDiv.className = 'flex items-center justify-between p-3 border rounded-lg';
    sessionDiv.setAttribute('data-session-id', session.id);

    const deviceIcon = getDeviceIcon(session.deviceType);
    const iconColor = session.isCurrent ? 'text-green-600' : 'text-blue-600';
    const iconBg = session.isCurrent ? 'bg-green-100' : 'bg-blue-100';

    sessionDiv.innerHTML = `
      <div class="flex items-center">
        <div class="w-8 h-8 ${iconBg} rounded-full flex items-center justify-center mr-3">
          <span class="text-sm">${deviceIcon}</span>
        </div>
        <div>
          <p class="font-medium">${session.formattedDeviceInfo}</p>
          <p class="text-sm text-gray-500">${session.formattedLocation} • ${session.formattedLastActivity}</p>
          <p class="text-xs text-gray-400">IP: ${session.ipAddress}</p>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <span class="${session.statusBadgeClass} text-sm font-medium">${session.statusBadge}</span>
        ${!session.isCurrent ? `<button onclick="terminateSession(${session.id}, '${session.formattedDeviceInfo}')" class="text-red-600 hover:text-red-800 text-sm ml-2">Sonlandır</button>` : ''}
      </div>
    `;

    return sessionDiv;
  }

  function getDeviceIcon(deviceType) {
    switch (deviceType?.toLowerCase()) {
      case 'mobile':
        return '📱';
      case 'tablet':
        return '📱';
      case 'desktop':
      default:
        return '💻';
    }
  }

  async function terminateSession(sessionId, deviceInfo) {
    showModal(
      'Oturumu Sonlandır',
      `"${deviceInfo}" cihazındaki oturumu sonlandırmak istediğinizden emin misiniz?`,
      'Sonlandır',
      () => performTerminateSession(sessionId),
      'warning'
    );
  }

  async function performTerminateSession(sessionId) {
    try {
      const response = await axios.post('/Settings/TerminateSessionAjax', { 
        sessionId 
      }, {
        headers: {
          'Content-Type': 'application/json',
          'RequestVerificationToken': token
        }
      });

      if (response.data.success) {
        showToast('Başarılı', response.data.message, 'success');
        // Remove session from local array
        activeSessions = activeSessions.filter(session => session.id !== sessionId);
        // Refresh display
        displayActiveSessions();
      } else {
        showToast('Hata', response.data.message, 'error');
      }
    } catch (error) {
      console.error('Error terminating session:', error);
      if (error.response && error.response.data && error.response.data.message) {
        showToast('Hata', error.response.data.message, 'error');
      } else {
        showToast('Hata', 'Oturum sonlandırılırken bir hata oluştu.', 'error');
      }
    }
  }

  function setupTerminateAllButton() {
    const terminateAllBtn = document.getElementById('terminate-all-sessions-btn');
    if (terminateAllBtn) {
      terminateAllBtn.onclick = function() {
        const otherSessions = activeSessions.filter(session => !session.isCurrent);
        if (otherSessions.length === 0) {
          showToast('Bilgi', 'Sonlandırılacak başka oturum bulunmuyor.', 'info');
          return;
        }

        showModal(
          'Tüm Diğer Oturumları Sonlandır',
          `${otherSessions.length} aktif oturumu sonlandırmak istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
          'Tümünü Sonlandır',
          performTerminateAllOtherSessions,
          'warning'
        );
      };
    }
  }

  async function performTerminateAllOtherSessions() {
    try {
      const response = await axios.post('/Settings/TerminateAllOtherSessionsAjax', {}, {
        headers: {
          'Content-Type': 'application/json',
          'RequestVerificationToken': token
        }
      });

      if (response.data.success) {
        showToast('Başarılı', response.data.message, 'success');
        // Refresh sessions
        loadActiveSessions();
      } else {
        showToast('Hata', response.data.message, 'error');
      }
    } catch (error) {
      console.error('Error terminating all other sessions:', error);
      if (error.response && error.response.data && error.response.data.message) {
        showToast('Hata', error.response.data.message, 'error');
      } else {
        showToast('Hata', 'Diğer oturumlar sonlandırılırken bir hata oluştu.', 'error');
      }
    }
  }

  // Make functions globally available
  window.loadActiveSessions = loadActiveSessions;
  window.terminateSession = terminateSession;
  window.performTerminateSession = performTerminateSession;
  window.performTerminateAllOtherSessions = performTerminateAllOtherSessions;
</script>
