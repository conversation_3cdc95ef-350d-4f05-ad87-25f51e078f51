@model PushDashboard.ViewModels.SettingsViewModel

<!-- <PERSON>ura Bilgileri -->
<div id="billing" class="tab-content">
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold">Fatura Bilgileri</h3>
      @if (!Model.CanManageUsers)
      {
        <div class="flex items-center text-sm text-gray-500">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          <PERSON><PERSON><PERSON> gör<PERSON><PERSON><PERSON><PERSON>e
        </div>
      }
    </div>
    
    <!-- Validation Summary -->
    <div id="billing-validation-summary" class="hidden mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Lütfen aşağıdaki hataları düzeltin:</h3>
          <div id="billing-error-list" class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1"></ul>
          </div>
        </div>
      </div>
    </div>

    <form id="billing-form" class="space-y-6" novalidate>
      @Html.AntiForgeryToken()      <!-- Fatura Türü -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="BillingType" class="block text-sm font-medium text-gray-700 mb-1">
            Fatura Türü <span class="text-red-500">*</span>
          </label>
          <select id="BillingType" name="BillingType" required 
                  @(!Model.CanManageUsers ? "disabled" : "")
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")">
            <option value="Individual">Bireysel</option>
            <option value="Corporate" selected>Kurumsal</option>
          </select>
          <div id="BillingType-error" class="mt-1 text-sm text-red-600 hidden"></div>
        </div>
        
        <!-- Vergi Dairesi -->
        <div id="tax-office-field">
          <label for="TaxOffice" class="block text-sm font-medium text-gray-700 mb-1">
            Vergi Dairesi
          </label>
          <input type="text" id="TaxOffice" name="TaxOffice" 
                 value="@Model.BillingInformation.TaxOffice" 
                 maxlength="100"
                 @(!Model.CanManageUsers ? "readonly" : "")
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")" 
                 placeholder="Vergi dairesini girin">
          <div id="TaxOffice-error" class="mt-1 text-sm text-red-600 hidden"></div>
        </div>
      </div>

      <!-- Vergi Numarası ve TC Kimlik -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Vergi Numarası -->
        <div id="tax-number-field">
          <label for="TaxNumber" class="block text-sm font-medium text-gray-700 mb-1">
            Vergi Numarası
          </label>
          <input type="text" id="TaxNumber" name="TaxNumber" 
                 value="@Model.BillingInformation.TaxNumber" 
                 maxlength="10" pattern="[0-9]{10}"
                 @(!Model.CanManageUsers ? "readonly" : "")
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")" 
                 placeholder="10 haneli vergi numarası">
          <div id="TaxNumber-error" class="mt-1 text-sm text-red-600 hidden"></div>
          <p class="mt-1 text-xs text-gray-500">10 haneli sayı olmalıdır</p>
        </div>
        
        <!-- TC Kimlik No -->
        <div id="identity-number-field">
          <label for="IdentityNumber" class="block text-sm font-medium text-gray-700 mb-1">
            TC Kimlik No
          </label>
          <input type="text" id="IdentityNumber" name="IdentityNumber" 
                 value="@Model.BillingInformation.IdentityNumber" 
                 maxlength="11" pattern="[0-9]{11}"
                 @(!Model.CanManageUsers ? "readonly" : "")
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")" 
                 placeholder="11 haneli TC kimlik numarası">
          <div id="IdentityNumber-error" class="mt-1 text-sm text-red-600 hidden"></div>
          <p class="mt-1 text-xs text-gray-500">11 haneli sayı olmalıdır</p>
        </div>
      </div>


      <!-- Firma Adı / Ad Soyad -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Firma Adı (Kurumsal için) -->
        <div id="company-name-field">
          <label for="CompanyName" class="block text-sm font-medium text-gray-700 mb-1">
            Firma Adı <span class="text-red-500">*</span>
          </label>
          <input type="text" id="CompanyName" name="CompanyName" 
                 maxlength="100"
                 @(!Model.CanManageUsers ? "readonly" : "")
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")" 
                 placeholder="Firma adını girin">
          <div id="CompanyName-error" class="mt-1 text-sm text-red-600 hidden"></div>
        </div>
        
        <!-- Ad Soyad (Bireysel için) -->
        <div id="full-name-field" style="display: none;">
          <label for="FullName" class="block text-sm font-medium text-gray-700 mb-1">
            Ad Soyad <span class="text-red-500">*</span>
          </label>
          <input type="text" id="FullName" name="FullName" 
                 maxlength="100"
                 @(!Model.CanManageUsers ? "readonly" : "")
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")" 
                 placeholder="Ad ve soyadınızı girin">
          <div id="FullName-error" class="mt-1 text-sm text-red-600 hidden"></div>
        </div>
      </div>
      <!-- Fatura Adresi -->
      <div>
        <label for="BillingAddress" class="block text-sm font-medium text-gray-700 mb-1">
          Fatura Adresi <span class="text-red-500">*</span>
        </label>
        <textarea id="BillingAddress" name="BillingAddress" rows="3" required
                  minlength="10" maxlength="500"
                  @(!Model.CanManageUsers ? "readonly" : "")
                  placeholder="Fatura adresinizi detaylı olarak girin" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(!Model.CanManageUsers ? "bg-gray-50 cursor-not-allowed" : "")">@Model.BillingInformation.BillingAddress</textarea>
        <div id="BillingAddress-error" class="mt-1 text-sm text-red-600 hidden"></div>
        <p class="mt-1 text-xs text-gray-500">En az 10, en fazla 500 karakter</p>
      </div>

      <!-- Submit Button -->
      @if (Model.CanManageUsers)
      {
        <div class="flex justify-end">
          <button type="submit" id="billing-submit-btn" 
                  class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="btn-text">Fatura Bilgilerini Kaydet</span>
            <span class="btn-loading hidden">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Kaydediliyor...
            </span>
          </button>
        </div>
      }
      else
      {
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Düzenleme Yetkisi Gerekli</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>Fatura bilgilerini düzenlemek için firma sahibi yetkisine sahip olmanız gerekmektedir. Mevcut bilgileri görüntüleyebilirsiniz ancak değişiklik yapamazsınız.</p>
              </div>
            </div>
          </div>
        </div>
      }
    </form>
  </div>
</div>