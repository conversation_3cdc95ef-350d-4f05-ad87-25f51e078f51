@model PushDashboard.ViewModels.SettingsViewModel

<!-- Profil Bilgileri -->
<div id="profile" class="tab-content active">
  <div class="bg-white rounded-lg shadow-sm p-6">
    @if (TempData["SuccessMessage"] != null)
    {
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          showNotification('@Html.Raw(Html.Encode(TempData["SuccessMessage"].ToString()))', 'success');
        });
      </script>
    }
    @if (TempData["ErrorMessage"] != null)
    {
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          showNotification('@Html.Raw(Html.Encode(TempData["ErrorMessage"].ToString()))', 'error');
        });
      </script>
    }

    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold">Profil Bilgileri</h3>
      <div class="flex items-center space-x-2">
        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
        <span class="text-sm text-gray-600">Aktif</span>
      </div>
    </div>

    <!-- Kullanıcı Profil Formu - AJAX -->
    <form id="user-profile-form" class="space-y-6 mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="UserProfile_FirstName" class="block text-sm font-medium text-gray-700 mb-1">Ad</label>
          <input id="UserProfile_FirstName" name="UserProfile.FirstName" value="@Model.UserProfile.FirstName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
        <div>
          <label for="UserProfile_LastName" class="block text-sm font-medium text-gray-700 mb-1">Soyad</label>
          <input id="UserProfile_LastName" name="UserProfile.LastName" value="@Model.UserProfile.LastName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
        <div>
          <label for="UserProfile_Email" class="block text-sm font-medium text-gray-700 mb-1">E-posta</label>
          <input id="UserProfile_Email" name="UserProfile.Email" type="email" value="@Model.UserProfile.Email" readonly class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
        </div>
        <div>
          <label for="UserProfile_PhoneNumber" class="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
          <input id="UserProfile_PhoneNumber" name="UserProfile.PhoneNumber" type="tel" value="@Model.UserProfile.PhoneNumber" placeholder="+90 ************" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
      </div>

      <div class="flex justify-end">
        <button type="submit" class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
          Profil Bilgilerini Kaydet
        </button>
      </div>
    </form>

    <!-- Firma Bilgileri -->
    @if (Model.CanEditCompany)
    {
      <div class="border-t pt-6">
        <h4 class="text-lg font-semibold mb-6">Firma Bilgileri</h4>
        <form id="company-profile-form" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="CompanyProfile_Name" class="block text-sm font-medium text-gray-700 mb-1">
                Firma Adı <span class="text-red-500">*</span>
              </label>
              <input id="CompanyProfile_Name" name="CompanyProfile.Name" value="@Model.CompanyProfile.Name" placeholder="Şirket adınız" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            <div>
              <label for="CompanyProfile_Phone" class="block text-sm font-medium text-gray-700 mb-1">
                Firma Telefonu
              </label>
              <input id="CompanyProfile_Phone" name="CompanyProfile.Phone" value="@Model.CompanyProfile.Phone" placeholder="+90 ************" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            <div class="md:col-span-2">
              <label for="CompanyProfile_Address" class="block text-sm font-medium text-gray-700 mb-1">
                Firma Adresi
              </label>
              <textarea id="CompanyProfile_Address" name="CompanyProfile.Address" rows="3" placeholder="Şirket adresiniz" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">@Model.CompanyProfile.Address</textarea>
            </div>
          </div>

          <div class="flex justify-end">
            <button type="submit" class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
              Firma Bilgilerini Kaydet
            </button>
          </div>
        </form>
      </div>
    }
    else
    {
      <div class="border-t pt-6">
        <h4 class="text-lg font-semibold mb-4">Firma Bilgileri</h4>
        <div class="bg-gray-50 p-4 rounded-md">
          <p class="text-gray-600">Firma: <strong>@Model.CompanyName</strong></p>
          <p class="text-sm text-gray-500 mt-2">Firma bilgilerini düzenlemek için yetkiniz bulunmamaktadır.</p>
        </div>
      </div>
    }
  </div>
</div>
