@model PushDashboard.ViewModels.SettingsViewModel

<!-- <PERSON>ü -->
<div class="lg:w-1/4">
  <div class="bg-white rounded-lg shadow-sm p-4">
    <nav class="space-y-2">
      <button class="tab-button active w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="profile">
        <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
        Profil Bilgileri
      </button>
      <button class="tab-button w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="payment">
        <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
          <line x1="1" y1="10" x2="23" y2="10"></line>
        </svg>
        Ödeme Yöntemleri
      </button>
      <button class="tab-button w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="billing">
        <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        Fatura Bilgileri
      </button>
      <button class="tab-button w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="notifications">
        <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        Bildirimler
      </button>
      <button class="tab-button w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="security">
        <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 12l2 2 4-4"></path>
          <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
          <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
          <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
          <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
        </svg>
        Güvenlik
      </button>
      @if (Model.CanManageUsers)
      {
        <button class="tab-button w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center" data-tab="users">
          <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="8.5" cy="7" r="4"></circle>
            <line x1="20" x2="20" y1="8" y2="14"></line>
            <line x1="23" x2="17" y1="11" y2="11"></line>
          </svg>
          Kullanıcılar
        </button>
      }
    </nav>
  </div>
</div>
