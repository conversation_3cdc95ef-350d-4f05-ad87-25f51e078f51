@model PushDashboard.ViewModels.SettingsViewModel

<!-- Bildirimler -->
<div id="notifications" class="tab-content">
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-semibold mb-6">Bildir<PERSON></h3>

    <form id="notification-preferences-form" class="space-y-6">
      <!-- E-posta Bildirimleri -->
      <div>
        <h4 class="font-medium mb-4">E-posta Bildirimleri</h4>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">Fatura Bildirimleri</p>
              <p class="text-sm text-gray-500">Yeni faturalar için e-posta alın</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="EmailInvoiceNotifications" name="EmailInvoiceNotifications" @(Model.NotificationPreferences.EmailInvoiceNotifications ? "checked" : "") class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">Kredi Bildirimleri</p>
              <p class="text-sm text-gray-500">Kredi değişiklikleri için e-posta alın</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="EmailCreditNotifications" name="EmailCreditNotifications" @(Model.NotificationPreferences.EmailCreditNotifications ? "checked" : "") class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">Pazarlama Bildirimleri</p>
              <p class="text-sm text-gray-500">Yeni özellikler ve kampanyalar hakkında bilgi alın</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="EmailMarketingNotifications" name="EmailMarketingNotifications" @(Model.NotificationPreferences.EmailMarketingNotifications ? "checked" : "") class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- SMS Bildirimleri -->
      <div class="border-t pt-6">
        <h4 class="font-medium mb-4">SMS Bildirimleri</h4>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">Güvenlik Uyarıları</p>
              <p class="text-sm text-gray-500">Hesap güvenliği için SMS alın</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="SmsSecurityAlerts" name="SmsSecurityAlerts" @(Model.NotificationPreferences.SmsSecurityAlerts ? "checked" : "") class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">Ödeme Bildirimleri</p>
              <p class="text-sm text-gray-500">Ödeme işlemleri için SMS alın</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="SmsPaymentNotifications" name="SmsPaymentNotifications" @(Model.NotificationPreferences.SmsPaymentNotifications ? "checked" : "") class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      <div class="flex justify-end pt-6">
        <button type="submit" class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
          Bildirimleri Kaydet
        </button>
      </div>
    </form>
  </div>
</div>
