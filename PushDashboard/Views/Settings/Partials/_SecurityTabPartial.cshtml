@model PushDashboard.ViewModels.SettingsViewModel

<!-- <PERSON><PERSON><PERSON><PERSON> -->
<div id="security" class="tab-content">
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h3 class="text-lg font-semibold mb-6">Güvenlik Ayarları</h3>

    <div class="space-y-6">
      <!-- <PERSON><PERSON><PERSON> -->
      @await Html.PartialAsync("_ChangePasswordPartial", new PushDashboard.ViewModels.ChangePasswordViewModel())
      
      <!-- <PERSON><PERSON>ğrulama -->
      @await Html.PartialAsync("_TwoFactorPartial", Model.TwoFactorStatus)
      
      <!-- Aktif <PERSON> -->
      <div class="border-t pt-6">
        <h4 class="font-medium mb-4">Aktif Oturumlar</h4>
        
        <!-- Loading State -->
        <div id="sessions-loading" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p class="mt-2 text-gray-600">Oturumlar yükleniyor...</p>
        </div>

        <!-- Error State -->
        <div id="sessions-error" class="hidden text-center py-8">
          <div class="text-red-500 mb-2">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-gray-600 mb-4">Oturumlar yüklenirken bir hata oluştu.</p>
          <button onclick="loadActiveSessions()" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
            Tekrar Dene
          </button>
        </div>

        <!-- Sessions Container -->
        <div id="sessions-container" class="hidden">
          <div id="sessions-list" class="space-y-3">
            <!-- Sessions will be loaded here dynamically -->
          </div>
          
          <button id="terminate-all-sessions-btn" class="mt-4 text-red-600 hover:text-red-800 text-sm font-medium">
            Tüm Diğer Oturumları Sonlandır
          </button>
        </div>

        <!-- Empty State -->
        <div id="sessions-empty" class="hidden text-center py-8">
          <div class="text-gray-400 mb-2">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          <p class="text-gray-600">Aktif oturum bulunamadı.</p>
        </div>
      </div>
    </div>
  </div>
</div>
