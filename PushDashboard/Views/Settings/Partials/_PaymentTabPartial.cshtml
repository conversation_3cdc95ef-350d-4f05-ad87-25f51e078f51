@model PushDashboard.ViewModels.SettingsViewModel

<!-- Ödeme Yöntemleri -->
<div id="payment" class="tab-content">
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold">Ödeme Yöntemleri</h3>
      <button class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark" id="add-card-btn">
        <PERSON><PERSON>
      </button>
    </div>

    <!-- Kay<PERSON><PERSON><PERSON> Kartlar -->
    <div class="space-y-4 mb-6">
      <!-- Kart 1 -->
      <div class="border rounded-lg p-4 flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-12 h-8 bg-blue-600 rounded flex items-center justify-center mr-4">
            <span class="text-white text-xs font-bold">VISA</span>
          </div>
          <div>
            <p class="font-medium">**** **** **** 1234</p>
            <p class="text-sm text-gray-500">Son kullanma: 12/26</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Varsayılan</span>
          <button class="text-gray-400 hover:text-gray-600">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Kart 2 -->
      <div class="border rounded-lg p-4 flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-12 h-8 bg-red-600 rounded flex items-center justify-center mr-4">
            <span class="text-white text-xs font-bold">MC</span>
          </div>
          <div>
            <p class="font-medium">**** **** **** 5678</p>
            <p class="text-sm text-gray-500">Son kullanma: 08/25</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button class="text-sm text-primary hover:text-primary-dark">Varsayılan Yap</button>
          <button class="text-gray-400 hover:text-gray-600">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Yeni Kart Ekleme Formu -->
    <div id="add-card-form" class="hidden border-t pt-6">
      <h4 class="font-medium mb-4">Yeni Kart Ekle</h4>
      <form class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Kart Numarası</label>
          <input type="text" placeholder="1234 5678 9012 3456" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Son Kullanma</label>
            <input type="text" placeholder="MM/YY" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
            <input type="text" placeholder="123" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          </div>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Kart Sahibi Adı</label>
          <input type="text" placeholder="JOHN DOE" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
        <div class="flex items-center">
          <input type="checkbox" id="default-card" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
          <label for="default-card" class="ml-2 block text-sm text-gray-700">Bu kartı varsayılan yap</label>
        </div>
        <div class="flex space-x-3">
          <button type="button" id="cancel-add-card" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            İptal
          </button>
          <button type="submit" class="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
            Kartı Ekle
          </button>
        </div>
      </form>
    </div>

    <!-- Otomatik Ödeme -->
    <div class="border-t pt-6">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="font-medium">Otomatik Ödeme</h4>
          <p class="text-sm text-gray-500">Faturaları otomatik olarak öde</p>
        </div>
        <label class="relative inline-flex items-center cursor-pointer">
          <input type="checkbox" checked class="sr-only peer">
          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
        </label>
      </div>
    </div>
  </div>
</div>
