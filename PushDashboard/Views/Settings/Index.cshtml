@model PushDashboard.ViewModels.SettingsViewModel
@{
  ViewData["Title"] = "Settings";
}

@section Scripts
{
  @await Html.PartialAsync("Partials/_UserManagementScriptsPartial")
  @await Html.PartialAsync("Partials/_ActiveSessionsScriptsPartial")
  @await Html.PartialAsync("Partials/_SettingsScriptsPartial")
  @await Html.PartialAsync("_ValidationScriptsPartial")
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800">Ayarlar</h2>
      <p class="text-gray-600">Hesap bilgilerinizi ve tercihlerinizi yönetin</p>
    </div>

    <div class="flex flex-col lg:flex-row gap-6">
      @await Html.PartialAsync("Partials/_SettingsNavigationPartial", Model)

      <!-- Sa<PERSON> -->
      <div class="lg:w-3/4">
        @await Html.PartialAsync("Partials/_ProfileTabPartial", Model)
        @await Html.PartialAsync("Partials/_BillingTabPartial", Model)
        @await Html.PartialAsync("Partials/_PaymentTabPartial", Model)
        @await Html.PartialAsync("Partials/_NotificationsTabPartial", Model)
        @await Html.PartialAsync("Partials/_SecurityTabPartial", Model)
        @await Html.PartialAsync("Partials/_UsersTabPartial", Model)
      </div>
    </div>
</main>
