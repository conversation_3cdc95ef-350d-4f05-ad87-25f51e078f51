@model PushDashboard.ViewModels.BasketIndexViewModel
@{
    ViewData["Title"] = "Sepetler";
}

<div class="space-y-6">
  <!-- Header -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Sepetler</h1>
      <p class="mt-1 text-sm text-gray-500">E-ticaret sepetlerini görüntüleyin ve yönetin</p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
      <button id="syncBaskets" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
        <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 12c0 1-1 1-1 1s-1 0-1-1 1-1 1-1 1 0 1 1z"></path>
          <path d="M16 12c0 1-1 1-1 1s-1 0-1-1 1-1 1-1 1 0 1 1z"></path>
          <path d="M11 12c0 1-1 1-1 1s-1 0-1-1 1-1 1-1 1 0 1 1z"></path>
          <path d="M6 12c0 1-1 1-1 1s-1 0-1-1 1-1 1-1 1 0 1 1z"></path>
          <path d="M1 12c0 1-1 1-1 1s-1 0-1-1 1-1 1-1 1 0 1 1z"></path>
        </svg>
        Senkronize Et
      </button>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="statsContainer">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="8" cy="21" r="1"></circle>
              <circle cx="19" cy="21" r="1"></circle>
              <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Toplam Sepet</dt>
              <dd class="text-lg font-medium text-gray-900" id="totalBaskets">-</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-green-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22,4 12,14.01 9,11.01"></polyline>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Aktif Sepet</dt>
              <dd class="text-lg font-medium text-gray-900" id="activeBaskets">-</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-yellow-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Terk Edilmiş</dt>
              <dd class="text-lg font-medium text-gray-900" id="abandonedBaskets">-</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div id="totalValueCard" class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Toplam Değer</dt>
              <dd class="text-lg font-medium text-gray-900" id="totalValue">-</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Currency Totals -->
  <div id="currencyTotalsContainer" class="hidden">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Para Birimi Bazlı Toplamlar</h3>
        <div id="currencyTotalsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <!-- Para birimi toplamları buraya yüklenecek -->
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="searchTerm" class="block text-sm font-medium text-gray-700">Arama</label>
          <input type="text" id="searchTerm" placeholder="Müşteri adı, e-posta..." class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
        </div>
        <div>
          <label for="statusFilter" class="block text-sm font-medium text-gray-700">Durum</label>
          <select id="statusFilter" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
            <option value="">Tümü</option>
            <option value="active">Aktif</option>
            <option value="abandoned">Terk Edilmiş</option>
            <option value="inactive">Pasif</option>
            <option value="member">Üyelikli</option>
            <option value="guest">Üyeliksiz</option>
          </select>
        </div>
        <div>
          <label for="sortBy" class="block text-sm font-medium text-gray-700">Sıralama</label>
          <select id="sortBy" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
            <option value="basketdate">Sepet Tarihi</option>
            <option value="customername">Müşteri Adı</option>
            <option value="totalamount">Toplam Tutar</option>
            <option value="productcount">Ürün Sayısı</option>
          </select>
        </div>
        <div>
          <label for="sortDirection" class="block text-sm font-medium text-gray-700">Yön</label>
          <select id="sortDirection" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
            <option value="desc">Azalan</option>
            <option value="asc">Artan</option>
          </select>
        </div>
      </div>
      <div class="mt-4 flex justify-end space-x-3">
        <button id="clearFilters" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
          Temizle
        </button>
        <button id="applyFilters" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
          Filtrele
        </button>
      </div>
    </div>
  </div>

  <!-- Baskets Table -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Sepet Listesi</h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500" id="recordInfo">Kayıt yükleniyor...</span>
          <div class="flex items-center space-x-1">
            <button id="prevPage" class="p-1 rounded-md text-gray-400 hover:text-gray-500 disabled:opacity-50" disabled>
              <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15,18 9,12 15,6"></polyline>
              </svg>
            </button>
            <span class="text-sm text-gray-700" id="pageInfo">-</span>
            <button id="nextPage" class="p-1 rounded-md text-gray-400 hover:text-gray-500 disabled:opacity-50" disabled>
              <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div id="loadingIndicator" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Sepetler yükleniyor...
      </div>
    </div>

    <div id="basketsTableContainer" class="hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Müşteri
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Para Birimi
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ürün Sayısı
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Toplam Tutar
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sepet Tarihi
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody id="basketsTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Sepetler buraya yüklenecek -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="emptyState" class="hidden p-8 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="8" cy="21" r="1"></circle>
        <circle cx="19" cy="21" r="1"></circle>
        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Sepet bulunamadı</h3>
      <p class="mt-1 text-sm text-gray-500">Henüz hiç sepet bulunmuyor veya arama kriterlerinize uygun sepet yok.</p>
    </div>
  </div>
</div>

<!-- Basket Detail Modal -->
<div id="basketModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Sepet Detayları</h3>
        <button onclick="closeBasketModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <div id="basketModalContent">
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    </div>
  </div>
</div>

@section Scripts {
<script>
let currentPage = 1;
let currentFilters = {};
let isLoading = false;

// Sayfa yüklendiğinde sepetleri yükle
document.addEventListener('DOMContentLoaded', function() {
    loadBaskets();
    loadCurrencyTotals();
    setupEventListeners();
});

function setupEventListeners() {
    // Sync button
    document.getElementById('syncBaskets').addEventListener('click', syncBaskets);

    // Filter buttons
    document.getElementById('applyFilters').addEventListener('click', applyFilters);
    document.getElementById('clearFilters').addEventListener('click', clearFilters);

    // Pagination buttons
    document.getElementById('prevPage').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadBaskets();
        }
    });

    document.getElementById('nextPage').addEventListener('click', () => {
        currentPage++;
        loadBaskets();
    });

    // Enter key support for search
    document.getElementById('searchTerm').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
}

function loadBaskets() {
    if (isLoading) return;

    isLoading = true;
    showLoading();

    const params = new URLSearchParams({
        page: currentPage,
        pageSize: 50,
        ...currentFilters
    });

    fetch(`/Basket/GetBaskets?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStats(data.data.stats);
                updateBasketsTable(data.data.baskets);
                updatePagination(data.data.pagination);
            } else {
                showNotification('Hata: ' + data.message, 'error');
                showEmptyState();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Sepetler yüklenirken hata oluştu.', 'error');
            showEmptyState();
        })
        .finally(() => {
            isLoading = false;
            hideLoading();
        });
}

function updateStats(stats) {
    document.getElementById('totalBaskets').textContent = stats.totalBaskets.toLocaleString();
    document.getElementById('activeBaskets').textContent = stats.activeBaskets.toLocaleString();
    document.getElementById('abandonedBaskets').textContent = stats.abandonedBaskets.toLocaleString();

    // Toplam değer kartını göster/gizle
    const totalValueCard = document.getElementById('totalValueCard');
    if (stats.totalValue > 0) {
        totalValueCard.classList.remove('hidden');
        document.getElementById('totalValue').textContent = stats.formattedTotalValue;
    } else {
        totalValueCard.classList.add('hidden');
    }
}

function loadCurrencyTotals() {
    fetch('/Basket/GetCurrencyTotals')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCurrencyTotals(data.data);
            } else {
                console.error('Currency totals error:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading currency totals:', error);
        });
}

function updateCurrencyTotals(currencyTotals) {
    const container = document.getElementById('currencyTotalsContainer');
    const grid = document.getElementById('currencyTotalsGrid');

    if (!currencyTotals || currencyTotals.length === 0) {
        container.classList.add('hidden');
        return;
    }

    // Birden fazla para birimi varsa göster
    if (currencyTotals.length > 1) {
        container.classList.remove('hidden');

        grid.innerHTML = currencyTotals.map(currency => `
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-bold text-blue-600">${currency.currencySymbol}</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">${currency.currency}</p>
                            <p class="text-xs text-gray-500">${currency.basketCount} sepet</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-gray-900">${currency.formattedTotalAmount}</p>
                    </div>
                </div>
            </div>
        `).join('');
    } else {
        container.classList.add('hidden');
    }
}

function updateBasketsTable(baskets) {
    const tbody = document.getElementById('basketsTableBody');

    if (baskets.length === 0) {
        showEmptyState();
        return;
    }

    tbody.innerHTML = baskets.map(basket => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <svg class="w-5 h-5 ${basket.profileIconClass}" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">${basket.displayCustomerName}</div>
                        <div class="text-sm text-gray-500">${basket.displayCustomerEmail}</div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${basket.membershipStatusBadgeClass}">
                            ${basket.membershipStatusText}
                        </span>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        ${basket.currency}
                    </span>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${basket.productCount}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${basket.formattedTotalAmount}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${basket.formattedBasketDate}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div class="flex space-x-2">
                    <button onclick="viewBasket('${basket.externalId}')" class="text-primary hover:text-primary-dark">
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    showBasketsTable();
}

function updatePagination(pagination) {
    document.getElementById('recordInfo').textContent =
        `${pagination.startRecord}-${pagination.endRecord} / ${pagination.totalCount} kayıt`;
    document.getElementById('pageInfo').textContent =
        `${pagination.currentPage} / ${pagination.totalPages}`;

    document.getElementById('prevPage').disabled = !pagination.hasPrevious;
    document.getElementById('nextPage').disabled = !pagination.hasNext;
}

function applyFilters() {
    currentFilters = {
        searchTerm: document.getElementById('searchTerm').value,
        status: document.getElementById('statusFilter').value,
        sortBy: document.getElementById('sortBy').value,
        sortDirection: document.getElementById('sortDirection').value
    };

    currentPage = 1;
    loadBaskets();
}

function clearFilters() {
    document.getElementById('searchTerm').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('sortBy').value = 'basketdate';
    document.getElementById('sortDirection').value = 'desc';

    currentFilters = {};
    currentPage = 1;
    loadBaskets();
}

function syncBaskets() {
    const button = document.getElementById('syncBaskets');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Senkronize ediliyor...
    `;

    fetch('/Basket/SyncBaskets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            loadBaskets(); // Verileri yenile
            loadCurrencyTotals(); // Para birimi toplamlarını yenile
        } else {
            showNotification('Hata: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Senkronizasyon sırasında hata oluştu.', 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function showLoading() {
    document.getElementById('loadingIndicator').classList.remove('hidden');
    document.getElementById('basketsTableContainer').classList.add('hidden');
    document.getElementById('emptyState').classList.add('hidden');
}

function hideLoading() {
    document.getElementById('loadingIndicator').classList.add('hidden');
}

function showBasketsTable() {
    document.getElementById('basketsTableContainer').classList.remove('hidden');
    document.getElementById('emptyState').classList.add('hidden');
}

function showEmptyState() {
    document.getElementById('basketsTableContainer').classList.add('hidden');
    document.getElementById('emptyState').classList.remove('hidden');
}

// Global değişkenler
let currentBasketId = null;
let currentBasketItemsPage = 1;
const basketItemsPageSize = 10;

// Sepet detaylarını görüntüle
function viewBasket(guidBasketId) {
    currentBasketId = guidBasketId;
    currentBasketItemsPage = 1;

    openBasketModal();

    // Önce sepet bilgilerini yükle
    fetch(`/Basket/GetBasketDetails?guidBasketId=${encodeURIComponent(guidBasketId)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBasketModal(data.data);
                // Sonra ürünleri sayfalama ile yükle
                loadBasketItems(guidBasketId, 1);
            } else {
                showNotification('Hata: ' + data.message, 'error');
                closeBasketModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Bir hata oluştu.', 'error');
            closeBasketModal();
        });
}

// Sepet ürünlerini sayfalama ile yükle
function loadBasketItems(guidBasketId, page = 1) {
    const itemsContainer = document.getElementById('basketItemsContainer');
    if (!itemsContainer) return;

    // Loading göster
    itemsContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p class="mt-2 text-sm text-gray-500">Ürünler yükleniyor...</p>
        </div>
    `;

    fetch(`/Basket/GetBasketItemsPaged?guidBasketId=${encodeURIComponent(guidBasketId)}&page=${page}&pageSize=${basketItemsPageSize}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateBasketItems(data.data);
                currentBasketItemsPage = page;
            } else {
                itemsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-sm text-red-600">Ürünler yüklenirken hata oluştu: ${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading basket items:', error);
            itemsContainer.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-sm text-red-600">Ürünler yüklenirken bir hata oluştu.</p>
                </div>
            `;
        });
}

function openBasketModal() {
    document.getElementById('basketModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeBasketModal() {
    document.getElementById('basketModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function populateBasketModal(basket) {
    const content = document.getElementById('basketModalContent');

    content.innerHTML = `
        <div class="space-y-6">
            <!-- Sepet Bilgileri -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-medium text-gray-900 mb-3">Sepet Bilgileri</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Müşteri</label>
                        <div class="mt-1 flex items-center space-x-2">
                            <p class="text-sm text-gray-900">${basket.displayCustomerName || basket.customerName}</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${basket.membershipStatusBadgeClass || 'bg-gray-100 text-gray-800'}">
                                ${basket.membershipStatusText || (basket.customerName ? 'Üyelikli' : 'Üyeliksiz')}
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">E-posta</label>
                        <p class="mt-1 text-sm text-gray-900">${basket.displayCustomerEmail || basket.customerEmail}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sepet Tarihi</label>
                        <p class="mt-1 text-sm text-gray-900">${basket.formattedBasketDate}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Son Güncelleme</label>
                        <p class="mt-1 text-sm text-gray-900">${basket.formattedLastUpdateDate}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Durum</label>
                        <div class="mt-1 flex space-x-2">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${basket.statusBadgeClass}">
                                ${basket.statusText}
                            </span>
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${basket.abandonedStatusBadgeClass}">
                                ${basket.abandonedStatusText}
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Toplam Tutar</label>
                        <p class="mt-1 text-sm text-gray-900 font-semibold">${basket.formattedTotalAmount}</p>
                    </div>
                </div>
            </div>

            <!-- Sepet Ürünleri -->
            <div>
                <h4 class="text-lg font-medium text-gray-900 mb-3">Sepet Ürünleri (${basket.productCount || 0})</h4>
                <div id="basketItemsContainer">
                    <!-- Ürünler buraya yüklenecek -->
                </div>
            </div>
        </div>
    `;
}

// Sepet ürünlerini populate et
function populateBasketItems(pagedData) {
    const container = document.getElementById('basketItemsContainer');
    if (!container) return;

    if (pagedData.items && pagedData.items.length > 0) {
        container.innerHTML = `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ürün</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Adet</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim Fiyat</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${pagedData.items.map(item => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            ${item.productImage ?
                                                `<img class="h-10 w-10 rounded object-cover" src="${item.productImage}" alt="${item.productName}">` :
                                                `<div class="h-10 w-10 rounded bg-gray-200 flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                                        <circle cx="9" cy="9" r="2"></circle>
                                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                                                    </svg>
                                                </div>`
                                            }
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">${item.productName}</div>
                                            <div class="text-sm text-gray-500">${item.productCode}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${item.formattedQuantity}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${item.formattedUnitPrice}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                                    ${item.formattedTotalPrice}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <!-- Sayfalama -->
            ${pagedData.totalPages > 1 ? `
                <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                    <div class="flex flex-1 justify-between sm:hidden">
                        <button onclick="loadBasketItems('${currentBasketId}', ${pagedData.currentPage - 1})"
                                ${!pagedData.hasPreviousPage ? 'disabled' : ''}
                                class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            Önceki
                        </button>
                        <button onclick="loadBasketItems('${currentBasketId}', ${pagedData.currentPage + 1})"
                                ${!pagedData.hasNextPage ? 'disabled' : ''}
                                class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            Sonraki
                        </button>
                    </div>
                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Toplam <span class="font-medium">${pagedData.totalItems}</span> üründen
                                <span class="font-medium">${((pagedData.currentPage - 1) * pagedData.pageSize) + 1}</span> -
                                <span class="font-medium">${Math.min(pagedData.currentPage * pagedData.pageSize, pagedData.totalItems)}</span> arası gösteriliyor
                            </p>
                        </div>
                        <div>
                            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                <button onclick="loadBasketItems('${currentBasketId}', ${pagedData.currentPage - 1})"
                                        ${!pagedData.hasPreviousPage ? 'disabled' : ''}
                                        class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">Önceki</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                ${Array.from({length: pagedData.totalPages}, (_, i) => i + 1).map(pageNum => `
                                    <button onclick="loadBasketItems('${currentBasketId}', ${pageNum})"
                                            class="relative inline-flex items-center px-4 py-2 text-sm font-semibold ${pageNum === pagedData.currentPage ? 'bg-primary text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary' : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'}">
                                        ${pageNum}
                                    </button>
                                `).join('')}

                                <button onclick="loadBasketItems('${currentBasketId}', ${pagedData.currentPage + 1})"
                                        ${!pagedData.hasNextPage ? 'disabled' : ''}
                                        class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">Sonraki</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            ` : ''}
        `;
    } else {
        container.innerHTML = `
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="8" cy="21" r="1"></circle>
                    <circle cx="19" cy="21" r="1"></circle>
                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Sepet boş</h3>
                <p class="mt-1 text-sm text-gray-500">Bu sepette henüz ürün bulunmuyor.</p>
            </div>
        `;
    }
}
</script>
}
