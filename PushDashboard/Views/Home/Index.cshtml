@model PushDashboard.ViewModels.DashboardIndexViewModel
@{
    ViewData["Title"] = "Dashboard";
}

@section Scripts
{
    @await Html.PartialAsync("Partials/_DashboardScriptsPartial")
}

<div class="mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">Dashboard</h2>
            <p class="text-gray-600">E-ticaret bildirim sistemi genel bakış</p>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">Son güncelleme:</span>
            <span id="lastUpdate" class="text-sm font-medium text-gray-700">@DateTime.Now.ToString("HH:mm")</span>
            <button id="refreshBtn" class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
            </button>
        </div>
    </div>
</div>

@await Html.PartialAsync("Partials/_DashboardStatsPartial", Model.Stats)

@await Html.PartialAsync("Partials/_DashboardQuickActionsPartial", Model.QuickActions)

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    @await Html.PartialAsync("Partials/_ModuleUsagePartial", Model.ModuleUsage)
    @await Html.PartialAsync("Partials/_IntegrationStatusPartial", Model.IntegrationStatus)
</div>

@await Html.PartialAsync("Partials/_DashboardChartsPartial", Model.Charts)

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    @await Html.PartialAsync("Partials/_RecentActivitiesPartial", Model.RecentActivities)
    @await Html.PartialAsync("Partials/_NotificationMetricsPartial", Model.NotificationMetrics)
</div>
