@model PushDashboard.ViewModels.DashboardStatsViewModel

<!-- Dashboard Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <!-- Credit Balance Card -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-gray-500 text-sm font-medium">Mevcut Bakiye</h3>
                <p id="creditBalance" class="text-2xl font-bold text-gray-900">@Model.FormattedCreditBalance</p>
                <p class="text-xs text-gray-500 mt-1">Bu ay harcanan: @Model.FormattedMonthlySpending</p>
            </div>
        </div>
    </div>

    <!-- Active Modules Card -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-gray-500 text-sm font-medium">Aktif Modüller</h3>
                <p id="activeModules" class="text-2xl font-bold text-gray-900">@Model.ActiveModules</p>
                <p class="text-xs text-gray-500 mt-1">@Model.ActiveIntegrations entegrasyon aktif</p>
            </div>
        </div>
    </div>

    <!-- Notifications Today Card -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-gray-500 text-sm font-medium">Bugün Gönderilen</h3>
                <p id="notificationsToday" class="text-2xl font-bold text-gray-900">@Model.NotificationsSentToday</p>
                <p class="text-xs text-gray-500 mt-1">Bu ay: @Model.NotificationsSentThisMonth bildirim</p>
            </div>
        </div>
    </div>

    <!-- Customers Card -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-gray-500 text-sm font-medium">Toplam Müşteri</h3>
                <p class="text-2xl font-bold text-gray-900">@Model.TotalCustomers</p>
                <p class="text-xs text-gray-500 mt-1">Teslimat oranı: @Model.FormattedDeliveryRate</p>
            </div>
        </div>
    </div>
</div>

<!-- Performance Summary Bar -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Sistem Durumu: <span class="font-medium text-green-600">Çevrimiçi</span></span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Ortalama Teslimat: <span class="font-medium">@Model.FormattedDeliveryRate</span></span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Son Aktivite: <span class="font-medium">@Model.FormattedLastActivity</span></span>
            </div>
        </div>
        <div class="text-sm text-gray-500">
            <span id="realTimeIndicator" class="inline-flex items-center">
                <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Canlı
            </span>
        </div>
    </div>
</div>
