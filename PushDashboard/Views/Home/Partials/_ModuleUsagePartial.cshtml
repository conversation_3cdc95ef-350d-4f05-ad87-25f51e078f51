@model List<PushDashboard.ViewModels.ModuleUsageOverviewViewModel>

<!-- Module Usage Overview -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900"><PERSON><PERSON><PERSON><PERSON> Kullanımı</h3>
            <p class="text-sm text-gray-600"><PERSON>u ayki modül performansı ve kullanım istatistikleri</p>
        </div>
        <a href="/Store" class="text-sm text-primary hover:text-primary-dark font-medium">
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> →
        </a>
    </div>

    @if (Model.Any())
    {
        <div class="space-y-4">
            @foreach (var module in Model.Take(5))
            {
                <div class="flex items-center justify-between p-4 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center @GetModuleColorClass(module.ModuleColor)">
                            @if (!string.IsNullOrEmpty(module.ModuleIcon))
                            {
                                @await Html.PartialAsync("Partials/_IconPartial", module.ModuleIcon)
                            }
                            else
                            {
                                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            }
                        </div>
                        <div class="ml-4">
                            <div class="flex items-center">
                                <h4 class="font-medium text-gray-900">@module.ModuleName</h4>
                                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full @module.StatusBadgeClass">
                                    @module.StatusText
                                </span>
                            </div>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="text-sm text-gray-500">
                                    Kullanım: <span class="font-medium">@module.UsageCountThisMonth</span>
                                </span>
                                <span class="text-sm text-gray-500">
                                    Başarı: <span class="font-medium">@module.FormattedSuccessRate</span>
                                </span>
                                <span class="text-sm text-gray-500">
                                    Son: <span class="font-medium">@module.FormattedLastUsed</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-900">@module.FormattedCost</div>
                        <div class="text-sm text-gray-500">Bu ay</div>
                    </div>
                </div>
            }
        </div>

        @if (Model.Count > 5)
        {
            <div class="mt-4 text-center">
                <a href="/Store" class="text-sm text-primary hover:text-primary-dark font-medium">
                    +@(Model.Count - 5) modül daha görüntüle
                </a>
            </div>
        }
    }
    else
    {
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h4 class="text-lg font-medium text-gray-900 mb-2">Henüz modül yok</h4>
            <p class="text-gray-600 mb-4">Mağazadan modül satın alarak başlayın</p>
            <a href="/Store" class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary-dark transition-colors">
                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Mağazayı Ziyaret Et
            </a>
        </div>
    }
</div>

@functions {
    private string GetModuleColorClass(string? color)
    {
        if (string.IsNullOrEmpty(color))
            return "bg-gray-100 text-gray-600";

        return color.ToLower() switch
        {
            var c when c.Contains("blue") => "bg-blue-100 text-blue-600",
            var c when c.Contains("green") => "bg-green-100 text-green-600",
            var c when c.Contains("purple") => "bg-purple-100 text-purple-600",
            var c when c.Contains("pink") => "bg-pink-100 text-pink-600",
            var c when c.Contains("orange") => "bg-orange-100 text-orange-600",
            var c when c.Contains("red") => "bg-red-100 text-red-600",
            var c when c.Contains("yellow") => "bg-yellow-100 text-yellow-600",
            _ => "bg-gray-100 text-gray-600"
        };
    }
}
