@model List<PushDashboard.ViewModels.NotificationMetricsViewModel>

<!-- Notification Metrics -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">B<PERSON><PERSON><PERSON></h3>
            <p class="text-sm text-gray-600">Kanal bazında performans analizi</p>
        </div>
        <div class="flex items-center space-x-2">
            <button class="text-sm text-gray-500 hover:text-gray-700" onclick="refreshMetrics()">
                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
            </button>
        </div>
    </div>

    @if (Model.Any())
    {
        <div class="space-y-4">
            @foreach (var metric in Model)
            {
                <div class="border border-gray-100 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-lg @GetChannelColorClass(metric.ChannelType) flex items-center justify-center mr-3">
                                @await Html.PartialAsync("Partials/_ChannelIconPartial", metric.ChannelIcon)
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">@metric.ChannelName</h4>
                                <p class="text-sm text-gray-500">@metric.ChannelType.ToUpper()</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-semibold text-gray-900">@metric.FormattedDeliveryRate</div>
                            <div class="text-sm text-gray-500">Teslimat Oranı</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-3">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Bugün Gönderilen</span>
                            <span>@metric.SentToday</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="@GetProgressBarColor(metric.DeliveryRateToday) h-2 rounded-full transition-all duration-300" 
                                 style="width: @(metric.DeliveryRateToday)%"></div>
                        </div>
                    </div>

                    <!-- Metrics Grid -->
                    <div class="grid grid-cols-4 gap-4 text-center">
                        <div>
                            <div class="text-sm font-semibold text-gray-900">@metric.SentToday</div>
                            <div class="text-xs text-gray-500">Bugün</div>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">@metric.SentThisWeek</div>
                            <div class="text-xs text-gray-500">Bu Hafta</div>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">@metric.SentThisMonth</div>
                            <div class="text-xs text-gray-500">Bu Ay</div>
                        </div>
                        <div>
                            <div class="text-sm font-semibold text-gray-900">@metric.FormattedCostThisMonth</div>
                            <div class="text-xs text-gray-500">Aylık Maliyet</div>
                        </div>
                    </div>

                    <!-- Status Indicators -->
                    <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-600">Başarılı: @metric.DeliveredToday</span>
                            </div>
                            @if (metric.FailedToday > 0)
                            {
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-600">Başarısız: @metric.FailedToday</span>
                                </div>
                            }
                        </div>
                        <div class="text-xs text-gray-500">
                            Bugünkü maliyet: @metric.FormattedCostToday
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Summary -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-lg font-semibold text-gray-900">@Model.Sum(m => m.SentToday)</div>
                    <div class="text-sm text-gray-500">Toplam Bugün</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-gray-900">@Model.Sum(m => m.DeliveredToday)</div>
                    <div class="text-sm text-gray-500">Başarılı Teslimat</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-gray-900">₺@Model.Sum(m => m.CostToday).ToString("N2")</div>
                    <div class="text-sm text-gray-500">Günlük Maliyet</div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
            </svg>
            <h4 class="text-lg font-medium text-gray-900 mb-2">Henüz bildirim yok</h4>
            <p class="text-gray-600 mb-4">Modüllerinizi kullanarak bildirim göndermeye başlayın</p>
            <a href="/Store" class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary-dark transition-colors">
                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                </svg>
                Modül Satın Al
            </a>
        </div>
    }
</div>

<script>
function refreshMetrics() {
    // Implement real-time metrics refresh
    console.log('Refreshing notification metrics...');
    
    // Show loading state
    const refreshBtn = event.target.closest('button');
    refreshBtn.classList.add('animate-spin');
    
    // Simulate API call
    setTimeout(() => {
        refreshBtn.classList.remove('animate-spin');
        showNotification('Metrikler güncellendi', 'success');
    }, 1000);
}
</script>

@functions {
    private string GetChannelColorClass(string channelType)
    {
        return channelType.ToLower() switch
        {
            "email" => "bg-blue-100 text-blue-600",
            "whatsapp" => "bg-green-100 text-green-600",
            "sms" => "bg-purple-100 text-purple-600",
            _ => "bg-gray-100 text-gray-600"
        };
    }

    private string GetProgressBarColor(double deliveryRate)
    {
        return deliveryRate switch
        {
            >= 90 => "bg-green-500",
            >= 70 => "bg-yellow-500",
            >= 50 => "bg-orange-500",
            _ => "bg-red-500"
        };
    }
}
