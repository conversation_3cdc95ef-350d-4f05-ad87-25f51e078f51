@model List<PushDashboard.ViewModels.IntegrationStatusViewModel>

<!-- Integration Status -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">Entegrasyon Durumu</h3>
            <p class="text-sm text-gray-600">Bağlı servisler ve senkronizasyon durumu</p>
        </div>
        <a href="/Integration" class="text-sm text-primary hover:text-primary-dark font-medium">
            Yönet →
        </a>
    </div>

    @if (Model.Any())
    {
        <div class="space-y-4">
            @foreach (var integration in Model)
            {
                <div class="flex items-center justify-between p-4 border border-gray-100 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                            @await Html.PartialAsync("Partials/_IntegrationIconPartial", integration.IntegrationType)
                        </div>
                        <div class="ml-4">
                            <div class="flex items-center">
                                <h4 class="font-medium text-gray-900">@integration.IntegrationName</h4>
                                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full @integration.StatusBadgeClass">
                                    @integration.StatusText
                                </span>
                            </div>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="text-sm text-gray-500">
                                    Tip: <span class="font-medium">@integration.IntegrationType</span>
                                </span>
                                <span class="text-sm text-gray-500">
                                    Son Sync: <span class="font-medium">@integration.FormattedLastSync</span>
                                </span>
                                @if (integration.SuccessfulOperationsToday > 0 || integration.FailedOperationsToday > 0)
                                {
                                    <span class="text-sm text-gray-500">
                                        Başarı: <span class="font-medium">@integration.FormattedSuccessRate</span>
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        @if (integration.IsConnected)
                        {
                            <div class="flex items-center text-green-600">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-sm font-medium">Bağlı</span>
                            </div>
                        }
                        else
                        {
                            <div class="flex items-center text-red-600">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                <span class="text-sm font-medium">Bağlantı Hatası</span>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(integration.LastError))
                        {
                            <button class="text-red-500 hover:text-red-700" 
                                    title="@integration.LastError"
                                    onclick="showErrorDetails('@integration.IntegrationName', '@integration.LastError')">
                                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </button>
                        }
                    </div>
                </div>
            }
        </div>

        <!-- Integration Summary -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-lg font-semibold text-gray-900">@Model.Count(i => i.IsConnected && i.IsActive)</div>
                    <div class="text-sm text-gray-500">Aktif</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-gray-900">@Model.Sum(i => i.SuccessfulOperationsToday)</div>
                    <div class="text-sm text-gray-500">Bugün Başarılı</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-gray-900">@Model.Sum(i => i.FailedOperationsToday)</div>
                    <div class="text-sm text-gray-500">Bugün Hatalı</div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <h4 class="text-lg font-medium text-gray-900 mb-2">Henüz entegrasyon yok</h4>
            <p class="text-gray-600 mb-4">E-ticaret platformlarınızı bağlayarak başlayın</p>
            <a href="/Integration" class="inline-flex items-center px-4 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary-dark transition-colors">
                <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Entegrasyonları Yönet
            </a>
        </div>
    }
</div>

<!-- Error Details Modal -->
<div id="errorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div>
                        <h3 id="errorModalTitle" class="text-lg font-semibold text-gray-900"></h3>
                        <p class="text-sm text-gray-500">Entegrasyon Hatası</p>
                    </div>
                </div>
                <div class="mb-6">
                    <p id="errorModalMessage" class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md"></p>
                </div>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeErrorModal()" class="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                        Kapat
                    </button>
                    <a href="/Integration" class="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary-dark">
                        Düzelt
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showErrorDetails(integrationName, errorMessage) {
    document.getElementById('errorModalTitle').textContent = integrationName;
    document.getElementById('errorModalMessage').textContent = errorMessage;
    document.getElementById('errorModal').classList.remove('hidden');
}

function closeErrorModal() {
    document.getElementById('errorModal').classList.add('hidden');
}
</script>
