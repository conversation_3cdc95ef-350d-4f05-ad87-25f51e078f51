using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Models;

namespace PushDashboard.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Company> Companies { get; set; }
    public DbSet<Module> Modules { get; set; }
    public DbSet<ModuleCategory> ModuleCategories { get; set; }

    public DbSet<CompanyModule> CompanyModules { get; set; }
    public DbSet<CompanyModuleSettings> CompanyModuleSettings { get; set; }
    public DbSet<NotificationPreferences> NotificationPreferences { get; set; }
    public DbSet<UserSession> UserSessions { get; set; }
    public DbSet<Customer> Customers { get; set; }
    public DbSet<CustomerImportJob> CustomerImportJobs { get; set; }
    public DbSet<Basket> Baskets { get; set; }
    public DbSet<BasketItem> BasketItems { get; set; }
    public DbSet<SyncLog> SyncLogs { get; set; }

    public DbSet<Integration> Integrations { get; set; }
    public DbSet<CompanyIntegration> CompanyIntegrations { get; set; }
    public DbSet<EmailTemplate> EmailTemplates { get; set; }
    public DbSet<CompanyEmailTemplate> CompanyEmailTemplates { get; set; }
    public DbSet<UserInvitation> UserInvitations { get; set; }

    // Module Usage Tracking
    public DbSet<ModuleUsageLog> ModuleUsageLogs { get; set; }

    // Comment Requests
    public DbSet<CommentRequest> CommentRequests { get; set; }



    // Bulk Messaging
    public DbSet<BulkMessage> BulkMessages { get; set; }
    public DbSet<BulkMessageRecipient> BulkMessageRecipients { get; set; }

    // Order Status Notifications
    public DbSet<OrderStatusNotification> OrderStatusNotifications { get; set; }
    public DbSet<OrderStatusChangeLog> OrderStatusChangeLogs { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // Command timeout ayarı (5 dakika)
            optionsBuilder.UseNpgsql(options => options.CommandTimeout(300));
        }
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // PostgreSQL için DateTime UTC dönüştürme
        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                {
                    property.SetValueConverter(new Microsoft.EntityFrameworkCore.Storage.ValueConversion.ValueConverter<DateTime, DateTime>(
                        v => v.Kind == DateTimeKind.Utc ? v : DateTime.SpecifyKind(v, DateTimeKind.Utc),
                        v => DateTime.SpecifyKind(v, DateTimeKind.Utc)));
                }
            }
        }

        // Configure the relationship between User and Company
        builder.Entity<ApplicationUser>()
            .HasOne(u => u.Company)
            .WithMany(c => c.Users)
            .HasForeignKey(u => u.CompanyId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure the relationship between User and NotificationPreferences
        builder.Entity<ApplicationUser>()
            .HasOne(u => u.NotificationPreferences)
            .WithOne(np => np.User)
            .HasForeignKey<NotificationPreferences>(np => np.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure the relationship between User and UserSessions
        builder.Entity<UserSession>()
            .HasOne(us => us.User)
            .WithMany(u => u.UserSessions)
            .HasForeignKey(us => us.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure CompanyModule unique constraint
        CompanyModule.ConfigureEntity(builder);

        // Configure Module relationships
        builder.Entity<Module>()
            .HasOne(m => m.Category)
            .WithMany(c => c.Modules)
            .HasForeignKey(m => m.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);



        // Configure CompanyModule relationships
        builder.Entity<CompanyModule>()
            .HasOne(cm => cm.Company)
            .WithMany(c => c.CompanyModules)
            .HasForeignKey(cm => cm.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CompanyModule>()
            .HasOne(cm => cm.Module)
            .WithMany(m => m.CompanyModules)
            .HasForeignKey(cm => cm.ModuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CompanyModule>()
            .HasOne(cm => cm.PurchasedByUser)
            .WithMany()
            .HasForeignKey(cm => cm.PurchasedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<CompanyModuleSettings>()
            .HasOne(cms => cms.CompanyModule)
            .WithOne(cm => cm.Settings)
            .HasForeignKey<CompanyModuleSettings>(cms => cms.CompanyModuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CompanyModuleSettings>()
            .HasOne(cms => cms.UpdatedByUser)
            .WithMany()
            .HasForeignKey(cms => cms.UpdatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure CompanyIntegration relationships
        builder.Entity<CompanyIntegration>()
            .HasOne(ci => ci.Company)
            .WithMany()
            .HasForeignKey(ci => ci.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CompanyIntegration>()
            .HasOne(ci => ci.Integration)
            .WithMany(i => i.CompanyIntegrations)
            .HasForeignKey(ci => ci.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure CompanyIntegration unique constraint
        builder.Entity<CompanyIntegration>()
            .HasIndex(ci => new { ci.CompanyId, ci.IntegrationId })
            .IsUnique();

        // Configure Customer relationships
        builder.Entity<Customer>()
            .HasOne(c => c.Company)
            .WithMany()
            .HasForeignKey(c => c.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure CustomerImportJob relationships
        builder.Entity<CustomerImportJob>()
            .HasOne(cij => cij.Company)
            .WithMany()
            .HasForeignKey(cij => cij.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for CustomerImportJob
        builder.Entity<CustomerImportJob>()
            .HasIndex(cij => cij.CompanyId)
            .HasDatabaseName("IX_CustomerImportJobs_CompanyId_Performance");

        builder.Entity<CustomerImportJob>()
            .HasIndex(cij => cij.Status)
            .HasDatabaseName("IX_CustomerImportJobs_Status_Performance");

        builder.Entity<CustomerImportJob>()
            .HasIndex(cij => cij.CreatedAt)
            .HasDatabaseName("IX_CustomerImportJobs_CreatedAt_Performance");

        // Configure SyncLog relationships
        builder.Entity<SyncLog>()
            .HasOne(sl => sl.Company)
            .WithMany()
            .HasForeignKey(sl => sl.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure Customer unique constraint for ExternalId and CompanyId
        builder.Entity<Customer>()
            .HasIndex(c => new { c.ExternalId, c.CompanyId })
            .IsUnique();

        // Configure Basket relationships
        builder.Entity<Basket>()
            .HasOne(b => b.Company)
            .WithMany()
            .HasForeignKey(b => b.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure Basket index for ExternalId and CompanyId (performance only, not unique)
        builder.Entity<Basket>()
            .HasIndex(b => new { b.ExternalId, b.CompanyId })
            .HasDatabaseName("IX_Baskets_ExternalId_CompanyId_Performance");

        // Configure BasketItem relationships
        builder.Entity<BasketItem>()
            .HasOne(bi => bi.Basket)
            .WithMany(b => b.BasketItems)
            .HasForeignKey(bi => bi.BasketId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance index'leri
        builder.Entity<Basket>()
            .HasIndex(b => b.CompanyId)
            .HasDatabaseName("IX_Baskets_CompanyId_Performance");

        builder.Entity<Basket>()
            .HasIndex(b => b.BasketDate)
            .HasDatabaseName("IX_Baskets_BasketDate_Performance");

        builder.Entity<BasketItem>()
            .HasIndex(bi => bi.BasketId)
            .HasDatabaseName("IX_BasketItems_BasketId_Performance");

        // Configure CompanyEmailTemplate relationships
        builder.Entity<CompanyEmailTemplate>()
            .HasOne(cet => cet.Company)
            .WithMany()
            .HasForeignKey(cet => cet.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // No foreign key constraint for EmailTemplate to allow negative IDs for custom templates
        // builder.Entity<CompanyEmailTemplate>()
        //     .HasOne(cet => cet.EmailTemplate)
        //     .WithMany(et => et.CompanyTemplates)
        //     .HasForeignKey(cet => cet.EmailTemplateId)
        //     .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CompanyEmailTemplate>()
            .HasOne(cet => cet.LastModifiedByUser)
            .WithMany()
            .HasForeignKey(cet => cet.LastModifiedBy)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure CompanyEmailTemplate unique constraint
        builder.Entity<CompanyEmailTemplate>()
            .HasIndex(cet => new { cet.CompanyId, cet.EmailTemplateId })
            .IsUnique();

        // Configure UserInvitation relationships
        builder.Entity<UserInvitation>()
            .HasOne(ui => ui.Company)
            .WithMany()
            .HasForeignKey(ui => ui.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<UserInvitation>()
            .HasOne(ui => ui.CreatedByUser)
            .WithMany()
            .HasForeignKey(ui => ui.CreatedBy)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure UserInvitation unique constraint for Email and CompanyId (prevent duplicate invitations)
        builder.Entity<UserInvitation>()
            .HasIndex(ui => new { ui.Email, ui.CompanyId })
            .IsUnique()
            .HasFilter("\"IsUsed\" = false"); // Only enforce uniqueness for unused invitations

        // Configure UserInvitation index for InvitationToken
        builder.Entity<UserInvitation>()
            .HasIndex(ui => ui.InvitationToken)
            .IsUnique();

        // Configure ModuleUsageLog relationships
        builder.Entity<ModuleUsageLog>()
            .HasOne(mul => mul.Company)
            .WithMany()
            .HasForeignKey(mul => mul.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ModuleUsageLog>()
            .HasOne(mul => mul.Module)
            .WithMany()
            .HasForeignKey(mul => mul.ModuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ModuleUsageLog>()
            .HasOne(mul => mul.User)
            .WithMany()
            .HasForeignKey(mul => mul.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for ModuleUsageLog
        builder.Entity<ModuleUsageLog>()
            .HasIndex(mul => mul.CompanyId)
            .HasDatabaseName("IX_ModuleUsageLogs_CompanyId_Performance");

        builder.Entity<ModuleUsageLog>()
            .HasIndex(mul => mul.CreatedAt)
            .HasDatabaseName("IX_ModuleUsageLogs_CreatedAt_Performance");

        builder.Entity<ModuleUsageLog>()
            .HasIndex(mul => new { mul.CompanyId, mul.ModuleId, mul.CreatedAt })
            .HasDatabaseName("IX_ModuleUsageLogs_Company_Module_Date_Performance");

        // Configure CommentRequest relationships
        builder.Entity<CommentRequest>()
            .HasOne(cr => cr.Company)
            .WithMany()
            .HasForeignKey(cr => cr.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for CommentRequest
        builder.Entity<CommentRequest>()
            .HasIndex(cr => cr.CompanyId)
            .HasDatabaseName("IX_CommentRequests_CompanyId_Performance");

        builder.Entity<CommentRequest>()
            .HasIndex(cr => cr.CreatedAt)
            .HasDatabaseName("IX_CommentRequests_CreatedAt_Performance");

        builder.Entity<CommentRequest>()
            .HasIndex(cr => cr.Status)
            .HasDatabaseName("IX_CommentRequests_Status_Performance");



        // Configure BulkMessage relationships
        builder.Entity<BulkMessage>()
            .HasOne(bm => bm.Company)
            .WithMany()
            .HasForeignKey(bm => bm.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<BulkMessage>()
            .HasOne(bm => bm.User)
            .WithMany()
            .HasForeignKey(bm => bm.UserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Configure BulkMessageRecipient relationships
        builder.Entity<BulkMessageRecipient>()
            .HasOne(bmr => bmr.BulkMessage)
            .WithMany(bm => bm.Recipients)
            .HasForeignKey(bmr => bmr.BulkMessageId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<BulkMessageRecipient>()
            .HasOne(bmr => bmr.Customer)
            .WithMany()
            .HasForeignKey(bmr => bmr.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for BulkMessage
        builder.Entity<BulkMessage>()
            .HasIndex(bm => bm.CompanyId)
            .HasDatabaseName("IX_BulkMessages_CompanyId_Performance");

        builder.Entity<BulkMessage>()
            .HasIndex(bm => bm.CreatedAt)
            .HasDatabaseName("IX_BulkMessages_CreatedAt_Performance");

        builder.Entity<BulkMessage>()
            .HasIndex(bm => bm.Status)
            .HasDatabaseName("IX_BulkMessages_Status_Performance");

        builder.Entity<BulkMessage>()
            .HasIndex(bm => new { bm.CompanyId, bm.Status, bm.CreatedAt })
            .HasDatabaseName("IX_BulkMessages_Company_Status_Date_Performance");

        // Performance indexes for BulkMessageRecipient
        builder.Entity<BulkMessageRecipient>()
            .HasIndex(bmr => bmr.BulkMessageId)
            .HasDatabaseName("IX_BulkMessageRecipients_BulkMessageId_Performance");

        builder.Entity<BulkMessageRecipient>()
            .HasIndex(bmr => bmr.Status)
            .HasDatabaseName("IX_BulkMessageRecipients_Status_Performance");

        builder.Entity<BulkMessageRecipient>()
            .HasIndex(bmr => new { bmr.BulkMessageId, bmr.Status })
            .HasDatabaseName("IX_BulkMessageRecipients_BulkMessage_Status_Performance");

        // Configure OrderStatusNotification relationships
        builder.Entity<OrderStatusNotification>()
            .HasOne(osn => osn.Company)
            .WithMany()
            .HasForeignKey(osn => osn.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure OrderStatusNotification unique constraint
        builder.Entity<OrderStatusNotification>()
            .HasIndex(osn => new { osn.CompanyId, osn.OrderStatus })
            .IsUnique();

        // Performance indexes for OrderStatusNotification
        builder.Entity<OrderStatusNotification>()
            .HasIndex(osn => osn.CompanyId)
            .HasDatabaseName("IX_OrderStatusNotifications_CompanyId_Performance");

        builder.Entity<OrderStatusNotification>()
            .HasIndex(osn => osn.IsActive)
            .HasDatabaseName("IX_OrderStatusNotifications_IsActive_Performance");

        // Configure OrderStatusChangeLog relationships
        builder.Entity<OrderStatusChangeLog>()
            .HasOne(oscl => oscl.Company)
            .WithMany()
            .HasForeignKey(oscl => oscl.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for OrderStatusChangeLog
        builder.Entity<OrderStatusChangeLog>()
            .HasIndex(oscl => oscl.CompanyId)
            .HasDatabaseName("IX_OrderStatusChangeLogs_CompanyId_Performance");

        builder.Entity<OrderStatusChangeLog>()
            .HasIndex(oscl => oscl.OrderId)
            .HasDatabaseName("IX_OrderStatusChangeLogs_OrderId_Performance");

        builder.Entity<OrderStatusChangeLog>()
            .HasIndex(oscl => oscl.StatusChangedAt)
            .HasDatabaseName("IX_OrderStatusChangeLogs_StatusChangedAt_Performance");

        builder.Entity<OrderStatusChangeLog>()
            .HasIndex(oscl => new { oscl.CompanyId, oscl.StatusChangedAt })
            .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Date_Performance");

        builder.Entity<OrderStatusChangeLog>()
            .HasIndex(oscl => new { oscl.CompanyId, oscl.NewStatus, oscl.StatusChangedAt })
            .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Status_Date_Performance");
    }
}
