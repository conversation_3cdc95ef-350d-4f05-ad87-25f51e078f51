{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=5432;Database=pushonica;Userid=postgres;Password=******;Timeout=300;Command Timeout=300;Connection Idle Lifetime=300;"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "Pushonica Info", "Username": "<EMAIL>", "Password": "swpb jpyl atgu cpdx"}, "BaseUrl": "https://localhost:7004", "CommentScraperApi": {"BaseUrl": "http://127.0.0.1:5000", "ScrapeReviewsEndpoint": "/scrape-reviews", "JobStatusEndpoint": "/job/{0}"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}